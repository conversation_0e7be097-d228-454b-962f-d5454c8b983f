[{"dt": "Purchase Invoice", "fieldname": "section_break_foreign_import", "label": "Foreign Import Tracking", "fieldtype": "Section Break", "insert_after": "terms", "collapsible": 1, "depends_on": "eval:doc.currency != doc.company_default_currency", "doctype": "Custom Field"}, {"dt": "Purchase Invoice", "fieldname": "foreign_import_tracker", "label": "Foreign Import Tracker", "fieldtype": "Data", "read_only": 1, "print_hide": 1, "no_copy": 1, "insert_after": "section_break_foreign_import", "doctype": "Custom Field"}, {"dt": "Purchase Invoice", "fieldname": "enable_import_tracking", "label": "Enable Import Tracking", "fieldtype": "Check", "default": "1", "insert_after": "foreign_import_tracker", "depends_on": "eval:!doc.foreign_import_tracker", "doctype": "Custom Field"}, {"dt": "Payment Entry", "fieldname": "foreign_import_tracker", "label": "Foreign Import Tracker", "fieldtype": "Link", "options": "Foreign Import Transaction", "read_only": 1, "print_hide": 1, "no_copy": 1, "insert_after": "amended_from", "depends_on": "eval:doc.payment_type == 'Pay' && doc.party_type == 'Supplier'", "doctype": "Custom Field"}, {"dt": "Payment Entry", "fieldname": "exchange_difference_amount", "label": "Exchange Difference", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1, "insert_after": "foreign_import_tracker", "depends_on": "foreign_import_tracker", "doctype": "Custom Field"}, {"dt": "Landed Cost Voucher", "fieldname": "section_break_import_tracking", "label": "Import Tracking", "fieldtype": "Section Break", "insert_after": "amended_from", "collapsible": 1, "doctype": "Custom Field"}, {"dt": "Landed Cost Voucher", "fieldname": "foreign_import_trackers", "label": "Related Import Trackers", "fieldtype": "Table", "options": "Foreign Import LCV Details", "read_only": 1, "insert_after": "section_break_import_tracking", "doctype": "Custom Field"}, {"dt": "Company", "fieldname": "section_break_foreign_import", "label": "Foreign Import Settings", "fieldtype": "Section Break", "insert_after": "exchange_gain_loss_account", "collapsible": 1, "doctype": "Custom Field"}, {"dt": "Company", "fieldname": "auto_create_import_tracker", "label": "Auto Create Import Tracker", "fieldtype": "Check", "default": "1", "insert_after": "section_break_foreign_import", "doctype": "Custom Field"}, {"dt": "Company", "fieldname": "import_exchange_threshold", "label": "Exchange Difference Threshold", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "default": "1.00", "insert_after": "auto_create_import_tracker", "doctype": "Custom Field"}, {"dt": "Supplier", "fieldname": "track_import_exchanges", "label": "Track Import Exchange Differences", "fieldtype": "Check", "default": "1", "insert_after": "default_price_list", "doctype": "Custom Field"}, {"dt": "Supplier", "fieldname": "preferred_exchange_account", "label": "Preferred Exchange Account", "fieldtype": "Link", "options": "Account", "insert_after": "track_import_exchanges", "doctype": "Custom Field"}, {"dt": "Journal Entry", "fieldname": "foreign_import_tracker", "label": "Foreign Import Tracker", "fieldtype": "Link", "options": "Foreign Import Transaction", "read_only": 1, "print_hide": 1, "insert_after": "amended_from", "depends_on": "eval:doc.voucher_type == 'Exchange Gain Or Loss'", "doctype": "Custom Field"}]