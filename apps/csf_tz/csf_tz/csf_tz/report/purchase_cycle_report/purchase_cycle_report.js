// Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.query_reports["Purchase Cycle Report"] = {
    "filters": [
        {
            "fieldname": "from_date",
            "label": __("From Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.add_months(frappe.datetime.get_today(), -1)
        },
        {
            "fieldname": "to_date",
            "label": __("To Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.get_today()
        }
    ]
};
