import { useEffect, useState } from 'react'
import { useNavigate, Link, useLocation } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { frappeClient } from '@/lib/frappe'
import { useAuth } from '@/context/AuthContext'
import { toast } from 'sonner'

type RegistrationForm = {
  first_name: string
  last_name: string
  email: string
  password: string
  confirm_password: string
  phone: string
  job_title: string
  company: string
  address_line1: string
  address_line2: string
  city: string
  state: string
  postal_code: string
  country: string
}

const initialForm: RegistrationForm = {
  first_name: '',
  last_name: '',
  email: '',
  password: '',
  confirm_password: '',
  phone: '',
  job_title: '',
  company: '',
  address_line1: '',
  address_line2: '',
  city: '',
  state: '',
  postal_code: '',
  country: '',
}

export function RegisterPage() {
  const navigate = useNavigate()
  const location = useLocation()
  const [formData, setFormData] = useState<RegistrationForm>(initialForm)
  const [error, setError] = useState<string | null>(null)
  const { user, refreshProfile } = useAuth()

  useEffect(() => {
    if (user && location.pathname === '/register') {
      navigate('/packages', { replace: true })
    }
  }, [user, navigate, location.pathname])

  const registrationMutation = useMutation({
    mutationFn: async () => {
      if (formData.password !== formData.confirm_password) {
        throw new Error('Passwords do not match')
      }

      const profile = {
        first_name: formData.first_name,
        last_name: formData.last_name,
        email: formData.email,
        password: formData.password,
        phone: formData.phone,
        job_title: formData.job_title,
        company: formData.company,
        address_line1: formData.address_line1,
        address_line2: formData.address_line2,
        city: formData.city,
        state: formData.state,
        postal_code: formData.postal_code,
        country: formData.country,
      }

      await frappeClient.call('tourism.api.auth.register_user', { profile })
      await frappeClient.login(formData.email, formData.password)
    },
    onSuccess: async () => {
      setFormData(initialForm)
      const profile = await refreshProfile()
      toast.success('Account created successfully', {
        description: profile
          ? `Welcome to SevenSerenity Safaris, ${profile.full_name.split(' ')[0]}!`
          : 'Welcome to SevenSerenity Safaris.',
      })
      navigate('/packages')
    },
    onError: (err: any) => {
      const message =
        err?.response?.data?._server_messages
          ? (() => {
              try {
                const parsed = JSON.parse(err.response.data._server_messages)[0]
                const payload = JSON.parse(parsed)
                return payload.message ?? 'Registration failed'
              } catch {
                return 'Registration failed'
              }
            })()
          : err?.message ?? 'Registration failed'
      setError(message)
    },
  })

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault()
    setError(null)
    registrationMutation.mutate()
  }

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8 text-center">
          <h1 className="text-3xl sm:text-4xl font-display font-bold text-gray-900 mb-2">
            Create Your Account
          </h1>
          <p className="text-gray-600">
            Build your profile to manage bookings, save preferences, and explore tailored experiences.
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Professional Profile</CardTitle>
          </CardHeader>
          <CardContent>
            <form className="space-y-8" onSubmit={handleSubmit}>
              {/* Personal Information */}
              <section>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="first_name">First Name *</Label>
                    <Input
                      id="first_name"
                      name="first_name"
                      value={formData.first_name}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="last_name">Last Name</Label>
                    <Input
                      id="last_name"
                      name="last_name"
                      value={formData.last_name}
                      onChange={handleChange}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Mobile Number *</Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="password">Password *</Label>
                    <Input
                      id="password"
                      name="password"
                      type="password"
                      value={formData.password}
                      onChange={handleChange}
                      required
                      minLength={8}
                    />
                  </div>
                  <div>
                    <Label htmlFor="confirm_password">Confirm Password *</Label>
                    <Input
                      id="confirm_password"
                      name="confirm_password"
                      type="password"
                      value={formData.confirm_password}
                      onChange={handleChange}
                      required
                      minLength={8}
                    />
                  </div>
                </div>
              </section>

              {/* Professional Details */}
              <section>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Professional Details</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="job_title">Job Title</Label>
                    <Input
                      id="job_title"
                      name="job_title"
                      value={formData.job_title}
                      onChange={handleChange}
                    />
                  </div>
                  <div>
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </section>

              {/* Address */}
              <section>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Address</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <Label htmlFor="address_line1">Address Line 1 *</Label>
                    <Input
                      id="address_line1"
                      name="address_line1"
                      value={formData.address_line1}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="address_line2">Address Line 2</Label>
                    <Input
                      id="address_line2"
                      name="address_line2"
                      value={formData.address_line2}
                      onChange={handleChange}
                    />
                  </div>
                  <div>
                    <Label htmlFor="city">City *</Label>
                    <Input id="city" name="city" value={formData.city} onChange={handleChange} required />
                  </div>
                  <div>
                    <Label htmlFor="state">State / Region *</Label>
                    <Input id="state" name="state" value={formData.state} onChange={handleChange} required />
                  </div>
                  <div>
                    <Label htmlFor="postal_code">Postal Code *</Label>
                    <Input
                      id="postal_code"
                      name="postal_code"
                      value={formData.postal_code}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="country">Country *</Label>
                    <Input
                      id="country"
                      name="country"
                      value={formData.country}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
              </section>

              {error && <p className="text-sm text-red-600">{error}</p>}

              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <Button type="submit" className="w-full sm:w-auto" disabled={registrationMutation.isPending}>
                  {registrationMutation.isPending ? 'Creating Account...' : 'Create Account'}
                </Button>
                <p className="text-sm text-gray-600 text-center sm:text-right">
                  Already have an account?{' '}
                  <Link to="/login" className="text-safari-600 font-medium hover:underline">
                    Sign in
                  </Link>
                </p>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
