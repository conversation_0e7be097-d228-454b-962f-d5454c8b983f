import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Camera, Compass, Mountain, Palmtree, Users, Waves } from 'lucide-react'

const activities = [
  {
    id: 1,
    title: 'Signature Game Drives',
    description: 'Track the Big Five with seasoned guides across the Serengeti and Ngorongoro.',
    icon: Compass,
    duration: 'Half Day / Full Day',
    difficulty: 'Easy',
    color: 'bg-sky-100 text-sky-600',
  },
  {
    id: 2,
    title: 'Kilimanjaro Trekking',
    description: 'Guided expeditions to Uhuru Peak with acclimatization support and camp crews.',
    icon: Mountain,
    duration: '6-8 Days',
    difficulty: 'Challenging',
    color: 'bg-emerald-100 text-emerald-600',
  },
  {
    id: 3,
    title: 'Zanzibar Water Adventures',
    description: 'Snorkeling, diving, and sunset dhow cruises in turquoise Indian Ocean waters.',
    icon: Waves,
    duration: 'Flexible',
    difficulty: 'Easy to Moderate',
    color: 'bg-cyan-100 text-cyan-600',
  },
  {
    id: 4,
    title: 'Cultural Immersions',
    description: 'Visit Maasai bomas, spice farms, and UNESCO-listed Stone Town with local hosts.',
    icon: Users,
    duration: 'Half Day',
    difficulty: 'Easy',
    color: 'bg-purple-100 text-purple-600',
  },
  {
    id: 5,
    title: 'Photography Safaris',
    description: 'Custom routes, private vehicles, and pro tips for capturing the perfect shot.',
    icon: Camera,
    duration: 'Custom',
    difficulty: 'Easy to Moderate',
    color: 'bg-amber-100 text-amber-600',
  },
  {
    id: 6,
    title: 'Island Hopping Retreats',
    description: 'Sail between Zanzibar, Pemba, and Mafia to discover remote beaches and reefs.',
    icon: Palmtree,
    duration: '2-4 Days',
    difficulty: 'Easy',
    color: 'bg-orange-100 text-orange-600',
  },
]

export function ActivitiesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <section className="relative isolate overflow-hidden py-20 text-white">
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1500534314209-a25ddb2bd429?auto=format&fit=crop&w=1600&q=80"
            alt="Safari activities"
            className="h-full w-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#1b1f3b]/95 via-[#252b4f]/90 to-[#2c3d59]/85" />
        </div>
        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="uppercase tracking-[0.4em] text-sm font-semibold text-white/70">Experiences</p>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-display font-bold mt-4 drop-shadow-xl">
            Activities & Add-ons
          </h1>
          <p className="text-lg sm:text-2xl text-white/90 max-w-3xl mx-auto mt-6 drop-shadow">
            Mix and match curated adventures, from summit treks to dhow cruises, to personalize your itinerary.
          </p>
        </div>
      </section>

      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
            {activities.map((activity) => {
              const Icon = activity.icon
              return (
                <Card key={activity.id} className="flex flex-col hover:shadow-xl transition-shadow">
                  <CardHeader>
                    <div className={`h-14 w-14 rounded-full ${activity.color} flex items-center justify-center mb-4`}>
                      <Icon className="h-7 w-7" />
                    </div>
                    <CardTitle>{activity.title}</CardTitle>
                    <CardDescription>{activity.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Duration</span>
                        <Badge variant="outline">{activity.duration}</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Difficulty</span>
                        <Badge variant="outline">{activity.difficulty}</Badge>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full bg-safari-600 hover:bg-safari-700">Add to itinerary</Button>
                  </CardFooter>
                </Card>
              )
            })}
          </div>
        </div>
      </section>
    </div>
  )
}
