import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Plus, Quote, Star, ThumbsUp } from 'lucide-react'

interface Review {
  id: number
  name: string
  location: string
  initials: string
  rating: number
  date: string
  comment: string
  likes: number
}

const seedReviews: Review[] = [
  {
    id: 1,
    name: '<PERSON>',
    location: 'United States',
    initials: 'SJ',
    rating: 5,
    date: 'September 2024',
    comment:
      'An absolutely incredible experience! The guides were knowledgeable, the logistics seamless, and the wildlife sightings unforgettable.',
    likes: 14,
  },
  {
    id: 2,
    name: '<PERSON>',
    location: 'Italy',
    initials: 'MR',
    rating: 5,
    date: 'August 2024',
    comment: 'Our Kilimanjaro summit was expertly organized with supportive guides and delicious meals at every camp.',
    likes: 9,
  },
  {
    id: 3,
    name: '<PERSON>ki <PERSON>',
    location: 'Japan',
    initials: 'YT',
    rating: 5,
    date: 'July 2024',
    comment: 'Loved the blend of cultural visits and safari drives. Meeting the Maasai community was a highlight of the trip.',
    likes: 17,
  },
]

export function ReviewsPage() {
  const [reviews, setReviews] = useState<Review[]>(seedReviews)
  const [liked, setLiked] = useState<Set<number>>(new Set())
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [form, setForm] = useState({ name: '', location: '', rating: 5, comment: '' })

  const handleSubmit = () => {
    if (!form.name || !form.location || !form.comment) {
      return
    }
    const next: Review = {
      id: Date.now(),
      name: form.name,
      location: form.location,
      initials: form.name
        .split(' ')
        .map((part) => part[0])
        .join('')
        .toUpperCase(),
      rating: form.rating,
      date: new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
      comment: form.comment,
      likes: 0,
    }
    setReviews((prev) => [next, ...prev])
    setForm({ name: '', location: '', rating: 5, comment: '' })
    setIsDialogOpen(false)
  }

  const toggleLike = (id: number) => {
    setLiked((prev) => {
      const next = new Set(prev)
      if (next.has(id)) {
        next.delete(id)
        setReviews((reviews) => reviews.map((review) => (review.id === id ? { ...review, likes: review.likes - 1 } : review)))
      } else {
        next.add(id)
        setReviews((reviews) => reviews.map((review) => (review.id === id ? { ...review, likes: review.likes + 1 } : review)))
      }
      return next
    })
  }

  return (
    <div className="min-h-screen bg-white">
      <section className="relative isolate overflow-hidden py-20 text-white">
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?auto=format&fit=crop&w=1600&q=80"
            alt="Traveler reviews"
            className="h-full w-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#0f172a]/96 via-[#16273c]/92 to-[#1f3850]/88" />
        </div>
        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="uppercase tracking-[0.4em] text-sm font-semibold text-white/70">Word of Mouth</p>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-display font-bold mt-4 drop-shadow-xl">
            Guest Reviews & Stories
          </h1>
          <p className="text-lg sm:text-2xl text-white/90 max-w-3xl mx-auto mt-6 drop-shadow">
            Hear from travelers who trusted SevenSerenity Safaris with their once-in-a-lifetime adventures.
          </p>
        </div>
      </section>

      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-end mb-6">
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-safari-600 hover:bg-safari-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Write a review
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Share your story</DialogTitle>
                  <DialogDescription>Help other travelers plan with confidence.</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="review-name">Name</Label>
                    <Input
                      id="review-name"
                      value={form.name}
                      onChange={(e) => setForm((prev) => ({ ...prev, name: e.target.value }))}
                      placeholder="Jane Doe"
                    />
                  </div>
                  <div>
                    <Label htmlFor="review-location">Location</Label>
                    <Input
                      id="review-location"
                      value={form.location}
                      onChange={(e) => setForm((prev) => ({ ...prev, location: e.target.value }))}
                      placeholder="Your city or country"
                    />
                  </div>
                  <div>
                    <Label>Rating</Label>
                    <div className="flex gap-2 mt-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button key={star} type="button" onClick={() => setForm((prev) => ({ ...prev, rating: star }))}>
                          <Star
                            className={`h-6 w-6 transition-colors ${
                              star <= form.rating ? 'text-amber-500 fill-amber-500' : 'text-gray-300'
                            }`}
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="review-comment">Review</Label>
                    <Textarea
                      id="review-comment"
                      rows={4}
                      value={form.comment}
                      onChange={(e) => setForm((prev) => ({ ...prev, comment: e.target.value }))}
                      placeholder="Tell us what made your safari special"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSubmit} className="bg-safari-600 hover:bg-safari-700">
                    Submit
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {reviews.map((review) => (
              <Card key={review.id} className="hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarFallback className="bg-safari-600 text-white">{review.initials}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{review.name}</p>
                        <p className="text-sm text-gray-500">{review.location}</p>
                      </div>
                    </div>
                    <Quote className="h-6 w-6 text-amber-600/30" />
                  </div>
                  <div className="flex items-center gap-1 mt-3">
                    {[...Array(review.rating)].map((_, index) => (
                      <Star key={index} className="h-4 w-4 text-amber-500 fill-amber-500" />
                    ))}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{review.date}</p>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-700 leading-relaxed mb-4">{review.comment}</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleLike(review.id)}
                    className={liked.has(review.id) ? 'text-safari-600' : ''}
                  >
                    <ThumbsUp className={`mr-2 h-4 w-4 ${liked.has(review.id) ? 'fill-safari-600' : ''}`} />
                    {review.likes}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
