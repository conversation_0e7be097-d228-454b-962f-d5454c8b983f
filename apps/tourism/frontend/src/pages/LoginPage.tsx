import { useEffect, useState } from 'react'
import { useNavigate, Link, useLocation } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { frappeClient } from '@/lib/frappe'
import { useAuth } from '@/context/AuthContext'
import { toast } from 'sonner'

export function LoginPage() {
  const navigate = useNavigate()
  const location = useLocation()
  const [credentials, setCredentials] = useState({ email: '', password: '' })
  const [error, setError] = useState<string | null>(null)
  const { user, refreshProfile } = useAuth()

  useEffect(() => {
    if (user && location.pathname === '/login') {
      navigate('/packages', { replace: true })
    }
  }, [user, navigate, location.pathname])

  const loginMutation = useMutation({
    mutationFn: async () => {
      await frappeClient.login(credentials.email, credentials.password)
    },
    onSuccess: async () => {
      const profile = await refreshProfile()
      toast.success(profile ? `Welcome back, ${profile.full_name.split(' ')[0]}!` : 'Welcome back!')
      const redirectTo =
        (location.state as { from?: string } | undefined)?.from && typeof (location.state as any).from === 'string'
          ? (location.state as { from?: string }).from
          : '/packages'
      navigate(redirectTo, { replace: true })
    },
    onError: (err: any) => {
      const message =
        err?.response?.data?._server_messages
          ? (() => {
              try {
                const parsed = JSON.parse(err.response.data._server_messages)[0]
                const payload = JSON.parse(parsed)
                return payload.message ?? 'Unable to sign in'
              } catch {
                return 'Unable to sign in'
              }
            })()
          : err?.message ?? 'Unable to sign in'
      setError(message)
    },
  })

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault()
    setError(null)
    loginMutation.mutate()
  }

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target
    setCredentials(prev => ({ ...prev, [name]: value }))
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
        <Card>
          <CardHeader className="text-center space-y-2">
            <CardTitle className="text-3xl font-display text-gray-900">Welcome Back</CardTitle>
            <p className="text-gray-600 text-sm">
              Sign in to manage your bookings and discover personalised safari experiences.
            </p>
          </CardHeader>
          <CardContent>
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  value={credentials.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  value={credentials.password}
                  onChange={handleChange}
                  placeholder="••••••••"
                  required
                />
              </div>
              {error && <p className="text-sm text-red-600">{error}</p>}
              <Button type="submit" className="w-full" disabled={loginMutation.isPending}>
                {loginMutation.isPending ? 'Signing In...' : 'Sign In'}
              </Button>
            </form>

            <div className="mt-6 text-center text-sm text-gray-600">
              <span>New to SevenSerenity Safaris? </span>
              <Link to="/register" className="text-safari-600 font-medium hover:underline">
                Create an account
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
