import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { MapPin, Mountain, Sun, TreePine, Waves } from 'lucide-react'
import { ImageWithFallback } from '@/components/figma/ImageWithFallback'

const regions = [
  {
    title: 'Northern Safari Circuit',
    description: 'Serengeti, Ngorongoro, Tarangire, Lake Manyara',
    icon: MapPin,
    copy: 'Classic game-drive country featuring the Great Migration, crater vistas, and baobab valleys.',
    image: 'https://images.unsplash.com/photo-1464388334095-4e05c18297cd?auto=format&fit=crop&w=1200&q=80',
  },
  {
    title: 'Mount Kilimanjaro',
    description: "Africa's highest peak (5,895m)",
    icon: Mountain,
    copy: 'Dormant volcano with rainforest, alpine desert, and arctic summit zones.',
    image: 'https://images.unsplash.com/photo-1489392191049-fc10c97e64b6?auto=format&fit=crop&w=1200&q=80',
  },
  {
    title: 'Zanzibar Archipelago',
    description: 'Pristine reefs and UNESCO-listed Stone Town',
    icon: Waves,
    copy: 'Island hopping between coral reefs, spice farms, and historic Swahili architecture.',
    image: 'https://images.unsplash.com/photo-1667550507974-cc647990b75a?auto=format&fit=crop&w=1200&q=80',
  },
  {
    title: 'Southern Highlands',
    description: 'Ruaha, Selous, Mikumi',
    icon: TreePine,
    copy: 'Remote reserves with dramatic river systems, large predator densities, and fewer vehicles.',
    image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?auto=format&fit=crop&w=1200&q=80',
  },
]

const climate = [
  {
    title: 'Long Rains',
    period: 'March – May',
    details: 'Green landscapes, dramatic skies, and best-value lodge rates. Ideal for birders and photographers.',
  },
  {
    title: 'Dry Season',
    period: 'June – October',
    details: 'Prime wildlife viewing as animals gather at water sources. Cooler mornings and clear skies.',
  },
  {
    title: 'Short Rains',
    period: 'November – December',
    details: 'Brief afternoon showers refresh the bush. Perfect for seeing the Serengeti migration return south.',
  },
  {
    title: 'Hot Season',
    period: 'January – February',
    details: 'Calving season in the Serengeti and peak beach weather in Zanzibar.',
  },
]

export function GeographyPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <section className="relative isolate overflow-hidden py-20 text-white">
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1500534314209-a25ddb2bd429?auto=format&fit=crop&w=1600&q=80"
            alt="Tanzanian landscapes"
            className="h-full w-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#0f172a]/96 via-[#16273c]/92 to-[#1f3850]/88" />
        </div>
        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="uppercase tracking-[0.4em] text-sm font-semibold text-white/70">Plan by Region</p>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-display font-bold mt-4 drop-shadow-xl">
            Geography & Climate
          </h1>
          <p className="text-lg sm:text-2xl text-white/90 max-w-3xl mx-auto mt-6 drop-shadow">
            Understand where to go and when, from alpine peaks to tropical archipelagos.
          </p>
        </div>
      </section>

      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs defaultValue="regions" className="w-full">
            <TabsList className="grid w-full max-w-md mx-auto grid-cols-2 mb-10">
              <TabsTrigger value="regions">Regions</TabsTrigger>
              <TabsTrigger value="climate">Climate</TabsTrigger>
            </TabsList>

            <TabsContent value="regions">
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-8">
                {regions.map((region) => {
                  const Icon = region.icon
                  return (
                    <Card key={region.title} className="overflow-hidden hover:shadow-xl transition-shadow">
                      <div className="relative h-52">
                        <ImageWithFallback src={region.image} alt={region.title} className="h-full w-full object-cover" />
                      </div>
                      <CardHeader>
                        <div className="flex items-center gap-2">
                          <Icon className="h-5 w-5 text-safari-600" />
                          <CardTitle>{region.title}</CardTitle>
                        </div>
                        <CardDescription>{region.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600">{region.copy}</p>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </TabsContent>

            <TabsContent value="climate">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {climate.map((season) => (
                  <Card key={season.title} className="hover:shadow-xl transition-shadow">
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <Sun className="h-5 w-5 text-amber-500" />
                        <div>
                          <CardTitle className="text-lg">{season.title}</CardTitle>
                          <CardDescription>{season.period}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 leading-relaxed">{season.details}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>
    </div>
  )
}
