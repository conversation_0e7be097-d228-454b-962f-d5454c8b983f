import { useMemo, useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { ShoppingCart, Star, Plus } from 'lucide-react'
import { ImageWithFallback } from '@/components/figma/ImageWithFallback'

interface Product {
  id: number
  name: string
  description: string
  image: string
  price: string
  rating: number
}

const starterProducts: Product[] = [
  {
    id: 1,
    name: 'Maasai Beaded Necklace',
    description: 'Handcrafted statement piece created by Maasai artisans.',
    image: 'https://images.unsplash.com/photo-1500530855697-b586d89ba3ee?auto=format&fit=crop&w=800&q=80',
    price: '$45',
    rating: 4.8,
  },
  {
    id: 2,
    name: 'African Art Sculpture',
    description: 'Hand-carved ebony figurine sourced from local cooperatives.',
    image: 'https://images.unsplash.com/photo-1518548419970-58e3b4079ab2?auto=format&fit=crop&w=800&q=80',
    price: '$85',
    rating: 4.9,
  },
  {
    id: 3,
    name: 'Tanzanite Pendant',
    description: 'Ethically sourced Tanzanite encased in sterling silver.',
    image: 'https://images.unsplash.com/photo-1495333017190-08a507dd24aa?auto=format&fit=crop&w=800&q=80',
    price: '$250',
    rating: 5,
  },
  {
    id: 4,
    name: 'Spice Market Set',
    description: 'Curated Zanzibari spices with tasting notes and recipes.',
    image: 'https://images.unsplash.com/photo-1470337458703-46ad1756a187?auto=format&fit=crop&w=800&q=80',
    price: '$35',
    rating: 4.6,
  },
]

export function ShopPage() {
  const [products, setProducts] = useState<Product[]>(starterProducts)
  const [ratings, setRatings] = useState<Record<number, number[]>>({})
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [form, setForm] = useState({ name: '', description: '', price: '', image: '' })

  const handleAddProduct = () => {
    if (!form.name || !form.description || !form.price) {
      return
    }
    const nextProduct: Product = {
      id: Date.now(),
      name: form.name,
      description: form.description,
      price: form.price,
      image:
        form.image || 'https://images.unsplash.com/photo-1518548419970-58e3b4079ab2?auto=format&fit=crop&w=800&q=80',
      rating: 0,
    }
    setProducts((prev) => [...prev, nextProduct])
    setForm({ name: '', description: '', price: '', image: '' })
    setIsAddDialogOpen(false)
  }

  const handleRate = (productId: number, value: number) => {
    setRatings((prev) => {
      const existing = prev[productId] || []
      const updated = [...existing, value]
      const next = { ...prev, [productId]: updated }
      const avg = updated.reduce((a, b) => a + b, 0) / updated.length
      setProducts((products) =>
        products.map((product) => (product.id === productId ? { ...product, rating: Number(avg.toFixed(1)) } : product)),
      )
      return next
    })
  }

  const getRating = (product: Product) => {
    const values = ratings[product.id]
    if (!values || values.length === 0) {
      return product.rating
    }
    return Number((values.reduce((a, b) => a + b, 0) / values.length).toFixed(1))
  }

  const averageBasket = useMemo(() => {
    if (!products.length) return '$0'
    const numeric = products
      .map((p) => Number(p.price.replace(/[^0-9.]/g, '')))
      .filter((n) => !Number.isNaN(n))
    const avg = numeric.reduce((a, b) => a + b, 0) / Math.max(1, numeric.length)
    return `$${avg.toFixed(0)}`
  }, [products])

  return (
    <div className="min-h-screen bg-gray-50">
      <section className="relative isolate overflow-hidden py-20 text-white">
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1602524206512-28b178b96b1c?auto=format&fit=crop&w=1600&q=80"
            alt="Souvenir shop"
            className="h-full w-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#5c2b05]/95 via-[#733707]/90 to-[#8b4509]/85" />
        </div>
        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="uppercase tracking-[0.4em] text-sm font-semibold text-white/70">Marketplace</p>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-display font-bold mt-4 drop-shadow-xl">
            Shop Local Treasures
          </h1>
          <p className="text-lg sm:text-2xl text-white/90 max-w-3xl mx-auto mt-6 drop-shadow">
            Support Tanzanian artisans with curated jewelry, textiles, spices, and artwork.
          </p>
          <p className="mt-4 text-sm uppercase tracking-[0.4em] text-white/60">Average basket {averageBasket}</p>
        </div>
      </section>

      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-end mb-6">
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-safari-600 hover:bg-safari-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Product
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add a new product</DialogTitle>
                  <DialogDescription>Showcase bespoke crafts from your partners.</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="product-name">Name</Label>
                    <Input
                      id="product-name"
                      value={form.name}
                      onChange={(e) => setForm((prev) => ({ ...prev, name: e.target.value }))}
                      placeholder="Product name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="product-description">Description</Label>
                    <Textarea
                      id="product-description"
                      value={form.description}
                      onChange={(e) => setForm((prev) => ({ ...prev, description: e.target.value }))}
                      placeholder="Tell travelers why it's special"
                    />
                  </div>
                  <div>
                    <Label htmlFor="product-price">Price</Label>
                    <Input
                      id="product-price"
                      value={form.price}
                      onChange={(e) => setForm((prev) => ({ ...prev, price: e.target.value }))}
                      placeholder="$75"
                    />
                  </div>
                  <div>
                    <Label htmlFor="product-image">Image URL</Label>
                    <Input
                      id="product-image"
                      value={form.image}
                      onChange={(e) => setForm((prev) => ({ ...prev, image: e.target.value }))}
                      placeholder="https://..."
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddProduct} className="bg-safari-600 hover:bg-safari-700">
                    Save product
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <Card key={product.id} className="flex flex-col overflow-hidden hover:shadow-xl transition-shadow">
                <div className="relative h-48">
                  <ImageWithFallback src={product.image} alt={product.name} className="h-full w-full object-cover" />
                </div>
                <CardHeader className="flex-1">
                  <CardTitle className="text-lg">{product.name}</CardTitle>
                  <p className="text-sm text-gray-600">{product.description}</p>
                  <div className="flex items-center gap-1 mt-3">
                    <Star className="h-4 w-4 text-amber-500 fill-amber-500" />
                    <span className="text-sm">{getRating(product)}</span>
                    <span className="text-xs text-gray-500">/5</span>
                  </div>
                  <div className="flex gap-1 mt-3">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        onClick={() => handleRate(product.id, star)}
                        className="transition-transform hover:scale-110"
                        title={`Rate ${star} star${star > 1 ? 's' : ''}`}
                      >
                        <Star
                          className={`h-4 w-4 ${
                            star <= getRating(product) ? 'text-amber-500 fill-amber-500' : 'text-gray-300'
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-xl font-semibold text-safari-600">{product.price}</p>
                </CardContent>
                <CardFooter>
                  <Button className="w-full bg-safari-600 hover:bg-safari-700">
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to cart
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
