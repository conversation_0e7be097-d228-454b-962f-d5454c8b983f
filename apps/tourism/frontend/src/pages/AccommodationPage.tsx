import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { ImageWithFallback } from '@/components/figma/ImageWithFallback'
import { MapPin, Star } from 'lucide-react'

const accommodations = [
  {
    id: 1,
    name: 'Serengeti Luxury Lodge',
    description: 'Experience tented suites, starlit dinners, and sunrise drives in the heart of the plains.',
    image:
      'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?auto=format&fit=crop&w=1200&q=80',
    location: 'Serengeti National Park',
    rating: 4.9,
    price: 'From $450/night',
    tags: ['WiFi', 'Pool', 'Private Guide', 'Spa'],
  },
  {
    id: 2,
    name: 'Ngorongoro Crater Camp',
    description: 'Glamping domes perched above the crater rim with unrivaled sunrise views.',
    image:
      'https://images.unsplash.com/photo-1505691938895-1758d7feb511?auto=format&fit=crop&w=1200&q=80',
    location: 'Ngorongoro Conservation Area',
    rating: 4.8,
    price: 'From $380/night',
    tags: ['All-inclusive', 'Campfire', 'Guided Walks'],
  },
  {
    id: 3,
    name: 'Zanzibar Beach Retreat',
    description: 'Boutique villas dotted along pristine white-sand beaches and turquoise water.',
    image:
      'https://images.unsplash.com/photo-1501117716987-c8e1ecb210cc?auto=format&fit=crop&w=1200&q=80',
    location: 'Zanzibar Island',
    rating: 4.7,
    price: 'From $320/night',
    tags: ['Beach Access', 'Water Sports', 'Spa'],
  },
  {
    id: 4,
    name: 'Tarangire River Lodge',
    description: 'Intimate suites overlooking elephant herds along the Tarangire River.',
    image:
      'https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?auto=format&fit=crop&w=1200&q=80',
    location: 'Tarangire National Park',
    rating: 4.6,
    price: 'From $280/night',
    tags: ['Safari Drives', 'Infinity Pool', 'Restaurant'],
  },
]

export function AccommodationPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <section className="relative isolate overflow-hidden py-20 text-white">
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1500534314209-a25ddb2bd429?auto=format&fit=crop&w=1600&q=80"
            alt="Safari lodge"
            className="h-full w-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#0f172a]/95 via-[#1c2c44]/90 to-[#2f4e5f]/85" />
        </div>
        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="uppercase tracking-[0.4em] text-sm font-semibold text-white/70">Stay in Style</p>
          <h1 className="text-4xl sm:text-5xl md:text-6xl font-display font-bold mt-4 drop-shadow-xl">
            Luxury Accommodations
          </h1>
          <p className="text-lg sm:text-2xl text-white/90 max-w-3xl mx-auto mt-6 drop-shadow">
            Handpicked lodges, beach retreats, and crater camps that keep you close to the wilderness without
            sacrificing comfort.
          </p>
        </div>
      </section>

      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-8">
            {accommodations.map((accommodation) => (
              <Card key={accommodation.id} className="flex flex-col overflow-hidden shadow-sm hover:shadow-xl transition-shadow">
                <div className="relative h-48">
                  <ImageWithFallback
                    src={accommodation.image}
                    alt={accommodation.name}
                    className="h-full w-full object-cover"
                  />
                </div>
                <CardHeader className="flex-1">
                  <div className="flex items-start justify-between gap-3">
                    <div>
                      <CardTitle className="text-lg">{accommodation.name}</CardTitle>
                      <CardDescription className="mt-1 flex items-start gap-1 text-xs">
                        <MapPin className="h-3 w-3 mt-0.5 text-amber-600" />
                        <span>{accommodation.location}</span>
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-1 text-sm">
                      <Star className="h-4 w-4 text-amber-500 fill-amber-500" />
                      <span>{accommodation.rating.toFixed(1)}</span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">{accommodation.description}</p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {accommodation.tags.map((tag) => (
                      <span key={tag} className="text-xs rounded-full bg-gray-100 px-3 py-1 text-gray-700">
                        {tag}
                      </span>
                    ))}
                  </div>
                  <p className="text-lg font-semibold text-safari-600">{accommodation.price}</p>
                </CardContent>
                <CardFooter>
                  <Button className="w-full bg-safari-600 hover:bg-safari-700">View Details</Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
