import { createContext, useContext, type ReactNode } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { frappeClient } from '@/lib/frappe'

type CustomerSummary = {
  name: string
  customer_name: string
}

export type AuthProfile = {
  user: string
  full_name: string
  email: string
  initials: string
  customer?: CustomerSummary | null
  roles?: string[]
}

type AuthContextValue = {
  user: AuthProfile | null
  isLoading: boolean
  refreshProfile: () => Promise<any>
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined)

const PROFILE_QUERY_KEY = ['auth-profile'] as const

const fetchProfile = async (): Promise<AuthProfile | null> => {
  const response = await frappeClient.call('tourism.api.auth.get_profile')
  const payload = response?.message ?? response
  if (!payload || payload.is_guest) {
    return null
  }
  return payload as AuthProfile
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const queryClient = useQueryClient()

  const profileQuery = useQuery({
    queryKey: PROFILE_QUERY_KEY,
    queryFn: fetchProfile,
    staleTime: 5 * 60 * 1000,
  })

  const refreshProfile = async () => {
    const data = await queryClient.fetchQuery({
      queryKey: PROFILE_QUERY_KEY,
      queryFn: fetchProfile,
      staleTime: 0,
    })
    return data ?? null
  }

  const logout = async () => {
    try {
      await frappeClient.logout()
    } finally {
      queryClient.setQueryData(PROFILE_QUERY_KEY, null)
      await queryClient.invalidateQueries({ queryKey: PROFILE_QUERY_KEY })
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user: profileQuery.data ?? null,
        isLoading: profileQuery.isLoading,
        refreshProfile,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
