"""
Authentication and user onboarding helpers for the Tourism app.
"""
from __future__ import annotations

from typing import Any, Dict, Optional, <PERSON>ple

import frappe
from frappe import _


def _parse_profile_data(data: Any) -> Dict[str, Any]:
    """Accept payload from frappe.call (dict or JSON string) and normalise to dict."""
    if data is None:
        return {}

    if isinstance(data, str):
        try:
            return frappe.parse_json(data) or {}
        except Exception:
            frappe.throw(_("Invalid profile data."))

    if isinstance(data, dict):
        return data

    frappe.throw(_("Unsupported profile payload."))


def _require_fields(profile: Dict[str, Any], required_fields: Tuple[str, ...]) -> None:
    missing = [field for field in required_fields if not profile.get(field)]
    if missing:
        frappe.throw(
            _("Missing required information: {0}").format(", ".join(missing)),
            title=_("Incomplete Registration"),
        )


def _resolve_customer_defaults(profile: Dict[str, Any]) -> <PERSON><PERSON>[str, str]:
    """Pick a customer group and territory to satisfy mandatory fields."""
    defaults = frappe.defaults.get_defaults() if hasattr(frappe, "defaults") else {}

    customer_group = profile.get("customer_group") or defaults.get("customer_group")
    if customer_group and frappe.db.exists("Customer Group", customer_group):
        pass
    else:
        customer_group = frappe.db.get_value("Customer Group", {"is_group": 0}, "name")
        if not customer_group:
            frappe.throw(_("Please configure at least one Customer Group."))

    territory = profile.get("territory") or defaults.get("territory")
    if territory and frappe.db.exists("Territory", territory):
        pass
    else:
        territory = frappe.db.get_value("Territory", {"is_group": 0}, "name")
        if not territory:
            frappe.throw(_("Please configure at least one Territory."))

    return customer_group, territory


def _assign_booking_role(user: frappe.model.document.Document) -> bool:
    """Assign Booking role when available."""
    if not frappe.db.exists("Role", "Booking"):
        frappe.log_error("Booking role is missing.", "Tourism Registration")
        return False

    if "Booking" in {role.role for role in user.get("roles", [])}:
        return False

    user.add_roles("Booking")
    return True


@frappe.whitelist(allow_guest=True)
def register_user(profile: Any) -> Dict[str, Any]:
    """
    Register a new website user, capture their profile, and assign the Booking role.
    """
    profile_data = _parse_profile_data(profile)
    _require_fields(profile_data, ("email", "password", "first_name"))

    email = (profile_data.get("email") or "").strip().lower()
    first_name = (profile_data.get("first_name") or "").strip()
    last_name = (profile_data.get("last_name") or "").strip()
    phone = (profile_data.get("phone") or "").strip()

    if not email:
        frappe.throw(_("Email is required."))

    if frappe.db.exists("User", email):
        frappe.throw(_("An account with this email already exists. Please log in instead."))

    full_name = profile_data.get("full_name")
    if not full_name:
        full_name = " ".join(filter(None, [first_name, last_name])) or first_name

    user = frappe.get_doc({
        "doctype": "User",
        "email": email,
        "first_name": first_name or full_name,
        "last_name": last_name,
        "full_name": full_name,
        "user_type": "Website User",
        "send_welcome_email": 0,
        "enabled": 1,
        "mobile_no": phone,
    })
    user.flags.ignore_permissions = True
    user.new_password = profile_data.get("password")
    user.insert()

    _assign_booking_role(user)

    customer_group, territory = _resolve_customer_defaults(profile_data)

    customer = frappe.get_doc({
        "doctype": "Customer",
        "customer_name": full_name,
        "customer_type": "Individual",
        "email_id": email,
        "mobile_no": phone,
        "customer_group": customer_group,
        "territory": territory,
        "user": user.name,
    })
    customer.flags.ignore_permissions = True
    customer.insert()

    # Optional professional details
    job_title = (profile_data.get("job_title") or "").strip()
    company = (profile_data.get("company") or "").strip()

    contact_data = {
        "doctype": "Contact",
        "first_name": first_name or full_name,
        "last_name": last_name,
        "designation": job_title,
        "company_name": company,
        "user": user.name,
        "links": [{"link_doctype": "Customer", "link_name": customer.name}],
    }
    email_ids = []
    if email:
        email_ids.append({"email_id": email, "is_primary": 1})
    phone_nos = []
    if phone:
        phone_nos.append({"phone": phone, "is_primary_phone": 1})

    if email_ids:
        contact_data["email_ids"] = email_ids
    if phone_nos:
        contact_data["phone_nos"] = phone_nos

    contact = frappe.get_doc(contact_data)
    contact.flags.ignore_permissions = True
    contact.insert()

    address_line1 = (profile_data.get("address_line1") or "").strip()
    if address_line1:
        address = frappe.get_doc({
            "doctype": "Address",
            "address_title": full_name,
            "address_type": "Billing",
            "address_line1": address_line1,
            "address_line2": profile_data.get("address_line2"),
            "city": profile_data.get("city"),
            "state": profile_data.get("state"),
            "pincode": profile_data.get("postal_code"),
            "country": profile_data.get("country") or frappe.defaults.get_global_default("country"),
            "phone": phone,
            "email_id": email,
            "is_primary_address": 1,
            "links": [{"link_doctype": "Customer", "link_name": customer.name}],
        })
        address.flags.ignore_permissions = True
        address.insert()

        customer.db_set("customer_primary_address", address.name, commit=False)

    customer.db_set("customer_primary_contact", contact.name, commit=False)

    frappe.db.commit()

    return {
        "message": _("Account created successfully."),
        "user": user.name,
        "customer": customer.name,
    }


def ensure_booking_role_on_login(login_manager) -> None:
    """Hook executed on session creation to guarantee Booking role for website users."""
    user_id = getattr(login_manager, "user", None)
    if not user_id or user_id in ("Guest", "Administrator"):
        return

    try:
        user = frappe.get_doc("User", user_id)
    except frappe.DoesNotExistError:
        return

    if user.user_type != "Website User":
        return

    if _assign_booking_role(user):
        frappe.db.commit()


def _build_initials(full_name: Optional[str]) -> str:
    """Return up to two-letter initials inferred from a full name."""
    if not full_name:
        return "NA"

    parts = [part.strip() for part in full_name.split() if part.strip()]
    if not parts:
        return (full_name[:2] or "NA").upper()

    initials = "".join(part[0].upper() for part in parts[:2])
    return initials or (full_name[:2].upper() or "NA")


@frappe.whitelist(allow_guest=True)
def get_profile() -> Dict[str, Any]:
    """Return lightweight profile data for the current session."""
    user = frappe.session.user

    if not user or user == "Guest":
        return {"is_guest": True}

    user_info = frappe.db.get_value(
        "User",
        user,
        ["name", "full_name", "email", "username"],
        as_dict=True,
    )

    if not user_info:
        return {"is_guest": True}

    full_name = user_info.full_name or user_info.username or user_info.name
    customer: Optional[Dict[str, str]] = None
    try:
        if frappe.db.has_column("Customer", "user"):
            customer = frappe.db.get_value(
                "Customer",
                {"user": user},
                ["name", "customer_name"],
                as_dict=True,
            )
    except Exception:
        # Column check can still fail on some databases; fall back silently.
        customer = None

    if not customer and user_info.email:
        customer = frappe.db.get_value(
            "Customer",
            {"email_id": user_info.email},
            ["name", "customer_name"],
            as_dict=True,
        )

    return {
        "is_guest": False,
        "user": user_info.name,
        "full_name": full_name,
        "email": user_info.email,
        "initials": _build_initials(full_name),
        "customer": customer,
        "roles": frappe.get_roles(user),
    }
