msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-11-09 09:35+0000\n"
"PO-Revision-Date: 2025-11-10 10:46\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Swedish\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: sv-SE\n"
"X-Crowdin-File: /[frappe.crm] develop/crm/locale/main.pot\n"
"X-Crowdin-File-ID: 97\n"
"Language: sv_SE\n"

#: frontend/src/components/ViewControls.vue:1172
msgid " (New)"
msgstr " (Ny)"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:58
msgid "(No title)"
msgstr "(Namnlös)"

#: frontend/src/components/Modals/TaskModal.vue:87
#: frontend/src/components/Telephony/TaskPanel.vue:70
msgid "01/04/2024 11:30 PM"
msgstr "2024-04-01 23:30"

#: frontend/src/utils/index.js:172
msgid "1 hour ago"
msgstr "1 timme sedan"

#: frontend/src/composables/event.js:163
msgid "1 hr"
msgstr "1 timme"

#: frontend/src/utils/index.js:168
msgid "1 minute ago"
msgstr "1 minut sedan"

#: frontend/src/utils/index.js:187
msgid "1 month ago"
msgstr "1 månad sedan"

#: frontend/src/utils/index.js:183
msgid "1 week ago"
msgstr "1 vecka sedan"

#: frontend/src/utils/index.js:191
msgid "1 year ago"
msgstr "1 år sedan"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1-10"
msgstr "1-10"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1000+"
msgstr "1000+"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "11-50"
msgstr "11-50"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "201-500"
msgstr "201-500"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "501-1000"
msgstr "501-1000"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "51-200"
msgstr "51-200"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>META</b>"
msgstr "<b>META</b>"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>SHORTCUTS</b>"
msgstr "<b>GENVÄGAR</b>"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:95
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:95
msgid "<p>Dear {{ lead_name }},</p>\\n\\n<p>This is a reminder for the payment of {{ grand_total }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappé</p>"
msgstr "<p>Bästa {{ lead_name }},</p>\\n\\n<p>Detta är en påminnelse om betalningen av {{ grand_total }}.</p>\\n\\n<p>Tack,</p>\\n<p>.</p>"

#. Header text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<span class=\"h5\"><b>PORTAL</b></span>"
msgstr "<span class=\"h5\"><b>PORTAL</b></span>"

#: frontend/src/components/CommunicationArea.vue:79
msgid "@John, can you please check this?"
msgstr "@John, kan du kolla detta?"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:103
msgid "A Lead requires either a person's name or an organization's name"
msgstr "Potentiell Kund kräver antingen person namn eller organisation namn"

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.py:47
msgid "A lead sync source is already enabled for this Facebook Lead Form!"
msgstr ""

#: crm/templates/emails/helpdesk_invitation.html:5
msgid "A new account has been created for you at {0}"
msgstr "Ny konto har skapats för dig på {0}"

#. Label of the api_key (Data) field in DocType 'CRM Exotel Settings'
#. Label of the api_key (Data) field in DocType 'CRM Twilio Settings'
#. Label of the api_key (Data) field in DocType 'ERPNext CRM Settings'
#. Label of the api_key (Data) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "API Key"
msgstr "API Nyckel"

#: frontend/src/components/Settings/emailConfig.js:179
msgid "API Key is required"
msgstr "API Nyckel Erfordras"

#. Label of the api_secret (Password) field in DocType 'CRM Twilio Settings'
#. Label of the api_secret (Password) field in DocType 'ERPNext CRM Settings'
#. Label of the api_secret (Password) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "API Secret"
msgstr "API Hemlighet"

#. Label of the api_token (Password) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "API Token"
msgstr "API Token"

#: frontend/src/components/Telephony/TwilioCallUI.vue:88
msgid "Accept"
msgstr "Acceptera"

#: crm/fcrm/doctype/crm_invitation/crm_invitation.js:7
msgid "Accept Invitation"
msgstr "Acceptera Inbjudan"

#: frontend/src/components/Telephony/TwilioCallUI.vue:155
msgid "Accept call"
msgstr "Acceptera samtal"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted"
msgstr "Accepterad"

#. Label of the accepted_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted At"
msgstr "Accepterad"

#. Label of the access_key (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Access Key"
msgstr "Åtkomst Nyckel"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:156
msgid "Access Key is required for Service Provider: {0}"
msgstr "Åtkomst Nyckel erfordras för Tjänsteleverantör: {0}"

#. Label of the access_token (Small Text) field in DocType 'Facebook Page'
#. Label of the access_token (Password) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:87
#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:28
msgid "Access Token"
msgstr "Åtkomst Token"

#: frontend/src/components/Settings/CurrencySettings.vue:90
msgid "Access key"
msgstr "Åtkomst Nyckel"

#: frontend/src/components/Settings/CurrencySettings.vue:94
msgid "Access key for Exchangerate Host. Required for fetching exchange rates."
msgstr "Åtkomst Nyckel för Valuta Växling Tjänst. Erfordras för att hämta växelkurser."

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:133
msgid "Access token is required"
msgstr ""

#. Label of the account_id (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Account ID"
msgstr "Konto ID"

#: frontend/src/components/Settings/emailConfig.js:13
msgid "Account Name"
msgstr "Konto Namn"

#. Label of the account_sid (Data) field in DocType 'CRM Exotel Settings'
#. Label of the account_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Account SID"
msgstr "Konto SID"

#: frontend/src/components/Settings/emailConfig.js:165
msgid "Account name is required"
msgstr "Konto namn erfordras"

#: frontend/src/components/CustomActions.vue:69
#: frontend/src/components/ViewControls.vue:683
#: frontend/src/components/ViewControls.vue:1064
msgid "Actions"
msgstr "Åtgärder"

#: frontend/src/pages/Deal.vue:537 frontend/src/pages/Lead.vue:384
#: frontend/src/pages/MobileDeal.vue:430 frontend/src/pages/MobileLead.vue:337
msgid "Activity"
msgstr "Aktivitet"

#: frontend/src/components/Dashboard/AddChartModal.vue:41
#: frontend/src/components/Modals/AddExistingUserModal.vue:55
msgid "Add"
msgstr "Lägg till"

#: frontend/src/components/Settings/EmailAccountList.vue:19
msgid "Add Account"
msgstr "Lägg till konto"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:9
msgid "Add Assignee"
msgstr "Lägg till Tilldelad"

#: frontend/src/components/ColumnSettings.vue:68
#: frontend/src/components/Kanban/KanbanView.vue:157
msgid "Add Column"
msgstr "Lägg till Kolumn"

#: frontend/src/components/Modals/AddExistingUserModal.vue:4
#: frontend/src/components/Settings/Users.vue:21
msgid "Add Existing User"
msgstr "Lägg till Befintlig Användare"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:57
#: frontend/src/components/FieldLayoutEditor.vue:172
#: frontend/src/components/Kanban/KanbanSettings.vue:80
#: frontend/src/components/SidePanelLayoutEditor.vue:97
msgid "Add Field"
msgstr "Lägga Till Fält"

#: frontend/src/components/Filter.vue:136
msgid "Add Filter"
msgstr "Lägg till Filter"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:273
msgid "Add Lead or Deal"
msgstr "Lägg till Potentiellt Kund eller Affär"

#: frontend/src/components/Controls/Grid.vue:334
msgid "Add Row"
msgstr "Lägg till Rad "

#: frontend/src/components/FieldLayoutEditor.vue:197
#: frontend/src/components/SidePanelLayoutEditor.vue:127
msgid "Add Section"
msgstr "Lägg till Sektion"

#: frontend/src/components/SortBy.vue:142
msgid "Add Sort"
msgstr "Lägg till Sortering"

#: frontend/src/components/FieldLayoutEditor.vue:62
msgid "Add Tab"
msgstr "Lägg till Flik"

#. Label of the add_weekly_holidays_section (Section Break) field in DocType
#. 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add Weekly Holidays"
msgstr "Lägg till Veckovisa Helger"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:20
msgid "Add a condition"
msgstr "Lägg till villkor"

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:24
msgid "Add a name for your source"
msgstr ""

#: frontend/src/components/Telephony/ExotelCallUI.vue:183
#: frontend/src/components/Telephony/TwilioCallUI.vue:57
msgid "Add a note"
msgstr "Lägg till anteckning"

#: frontend/src/components/Telephony/ExotelCallUI.vue:191
msgid "Add a task"
msgstr "Lägg till uppgift"

#: frontend/src/components/Dashboard/AddChartModal.vue:4
msgid "Add chart"
msgstr "Lägg till diagram"

#: frontend/src/components/FieldLayoutEditor.vue:420
msgid "Add column"
msgstr "Lägg till kolumn"

#: frontend/src/components/ConditionsFilter/CFConditions.vue:23
#: frontend/src/components/ConditionsFilter/CFConditions.vue:82
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:28
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:64
msgid "Add condition"
msgstr "Lägg till villkor"

#: frontend/src/components/ConditionsFilter/CFConditions.vue:91
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:70
msgid "Add condition group"
msgstr "Lägg till villkorsgrupp"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:325
msgid "Add description"
msgstr "Lägg till beskrivning"

#: frontend/src/components/Modals/EventModal.vue:142
msgid "Add description."
msgstr "Lägg till beskrivning."

#: frontend/src/components/Telephony/TaskPanel.vue:17
msgid "Add description..."
msgstr "Lägg till beskrivning..."

#: frontend/src/components/Modals/AddExistingUserModal.vue:12
msgid "Add existing system users to this CRM. Assign them a role to grant access with their current credentials."
msgstr "Lägg till befintliga system användare. Tilldela dem roll för att ge åtkomst med deras nuvarande autentisering uppgifter."

#: frontend/src/components/ViewControls.vue:107
msgid "Add filter"
msgstr "Lägg till filter"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Add note"
msgstr "Lägg till anteckning"

#: frontend/src/pages/Welcome.vue:24
msgid "Add sample data"
msgstr "Lägg till exempel data"

#. Description of the 'Icon' (Code) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Add svg code or use feather icons e.g 'settings'"
msgstr "Lägg till svg kod eller använd fjäder ikoner, t.ex. \"inställningar\""

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Add task"
msgstr "Lägg till Uppgift"

#. Label of the add_to_holidays (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add to Holidays"
msgstr "Lägg till Helger"

#: frontend/src/components/Layouts/AppSidebar.vue:439
msgid "Add your first comment"
msgstr "Lägg till första kommentar"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:11
msgid "Add, edit, and manage email templates for various CRM communications"
msgstr "Lägg till, redigera och hantera e-post mallar för olika Säljstöd kommunikationer"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:12
msgid "Add, edit, and manage sources for automatic lead syncing to your CRM"
msgstr ""

#. Label of the address (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Address"
msgstr "Adress"

#: frontend/src/components/Modals/AddExistingUserModal.vue:94
#: frontend/src/components/Settings/InviteUserPage.vue:158
#: frontend/src/components/Settings/InviteUserPage.vue:165
#: frontend/src/components/Settings/Users.vue:86
#: frontend/src/components/Settings/Users.vue:126
#: frontend/src/components/Settings/Users.vue:192
#: frontend/src/components/Settings/Users.vue:242
#: frontend/src/components/Settings/Users.vue:245
msgid "Admin"
msgstr "Administratör"

#: crm/integrations/twilio/twilio_handler.py:144
msgid "Agent is unavailable to take the call, please call after some time."
msgstr "Agent är inte tillgänglig för att ta samtalet, vänligen ring efter en tid."

#. Name of a role
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:76
#: frontend/src/components/Settings/Users.vue:85
msgid "All"
msgstr "Alla"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:191
#: frontend/src/components/Calendar/CalendarEventPanel.vue:641
#: frontend/src/components/Modals/EventModal.vue:71
#: frontend/src/composables/event.js:122
msgid "All day"
msgstr "Hela dagen"

#. Label of the amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
#: frontend/src/pages/Contact.vue:507 frontend/src/pages/MobileContact.vue:507
#: frontend/src/pages/MobileOrganization.vue:451
#: frontend/src/pages/Organization.vue:481
msgid "Amount"
msgstr "Belopp"

#. Description of the 'Net Amount' (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Amount after discount"
msgstr "Belopp efter rabatt"

#: frontend/src/data/script.js:50 frontend/src/data/script.js:51
msgid "An error occurred"
msgstr "Ett fel uppstod"

#: frontend/src/data/document.js:66
msgid "An error occurred while updating the document"
msgstr "Ett fel inträffade vid uppdatering av dokument"

#. Description of the 'Favicon' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org]"
msgstr "Ikon fil med ico ändelse. Ska vara 16 x 16 px. Skapas med användning av favicon generator. [favicon-generator.org]"

#. Description of the 'Logo' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An image with 1:1 & 2:1 ratio is preferred"
msgstr "Bild med förhållandet 1:1 & 2:1 är att föredra"

#: frontend/src/components/Filter.vue:43 frontend/src/components/Filter.vue:81
msgid "And"
msgstr "Och"

#. Label of the annual_revenue (Currency) field in DocType 'CRM Deal'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Lead'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Annual Revenue"
msgstr "Årlig Omsätning"

#: frontend/src/components/Modals/DealModal.vue:200
#: frontend/src/components/Modals/LeadModal.vue:141
msgid "Annual Revenue should be a number"
msgstr "Årlig intäkt ska vara ett tal"

#: frontend/src/components/Settings/BrandSettings.vue:55
msgid "Appears in the left sidebar. Recommended size is 32x32 px in PNG or SVG"
msgstr "Visas i vänster sidofält. Rekommenderad storlek är 32x32 px i PNG eller SVG"

#: frontend/src/components/Settings/BrandSettings.vue:90
msgid "Appears next to the title in your browser tab. Recommended size is 32x32 px in PNG or ICO"
msgstr "Visas bredvid titeln i din webbläsarflik. Rekommenderad storlek är 32x32 px i PNG eller ICO"

#: frontend/src/components/Kanban/KanbanSettings.vue:101
#: frontend/src/components/Kanban/KanbanView.vue:46
msgid "Apply"
msgstr "Tillämpa"

#. Label of the apply_on (Link) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Apply On"
msgstr "Tillämpa På"

#. Label of the view (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Apply To"
msgstr "Tillämpa På"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:125
msgid "Apply on"
msgstr "Tillämpa"

#: frontend/src/components/Apps.vue:19
msgid "Apps"
msgstr "Appar"

#: frontend/src/components/Activities/AttachmentArea.vue:128
msgid "Are you sure you want to delete this attachment?"
msgstr "Är du säker på att du vill ta bort denna bilaga?"

#: frontend/src/pages/MobileContact.vue:260
msgid "Are you sure you want to delete this contact?"
msgstr "Är du säker på att du vill ta bort denna kontakt?"

#: frontend/src/components/Modals/EventModal.vue:408
#: frontend/src/pages/Calendar.vue:300
msgid "Are you sure you want to delete this event?"
msgstr "Är du säker på att du vill ta bort denna händelse?"

#: frontend/src/pages/MobileOrganization.vue:261
msgid "Are you sure you want to delete this organization?"
msgstr "Är du säker på att du vill ta bort denna organisation?"

#: frontend/src/components/Activities/TaskArea.vue:60
msgid "Are you sure you want to delete this task?"
msgstr "Är du säker på att du vill ta bort denna uppgift?"

#: frontend/src/components/DeleteLinkedDocModal.vue:231
msgid "Are you sure you want to delete {0} linked item(s)?"
msgstr "Är du säker att du vill ta bort {0} länkad artikel(ar)?"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:615
msgid "Are you sure you want to discard unsaved changes to this event?"
msgstr "Är du säker på att du vill ignorera ändringar som inte sparats i denna händelse?"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:576
msgid "Are you sure you want to go back? Unsaved changes will be lost."
msgstr "Är du säker på att du vill ångra? Osparade ändringar kommer att gå förlorade."

#: frontend/src/composables/frappecloud.js:24
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "Är du säker på att du vill logga in på din Översikt Panel i Frappe Cloud?"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:9
msgid "Are you sure you want to reset 'Create Quotation from CRM Deal' Form Script?"
msgstr "Är du säker på att du vill återställa 'Skapa Offert från Säljstöd Affär' Formulär Skript?"

#: frontend/src/components/Settings/CurrencySettings.vue:165
msgid "Are you sure you want to set the currency as {0}? This cannot be changed later."
msgstr "Är du säker på att du vill ange valuta som {0}? Detta kan inte ändras senare."

#: frontend/src/components/DeleteLinkedDocModal.vue:244
msgid "Are you sure you want to unlink {0} linked item(s)?"
msgstr "Är du säker på att du vill ta bort länk till {0} länkade artikel(ar)?"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:193
msgid "Ask your manager to set up the Exchange Rate Provider, as default provider does not support currency conversion for {0} to {1}."
msgstr "Be ansvarig att konfigurera Valuta Växling Leverantör, eftersom standard leverantör inte stöder valuta konvertering från {0} till {1}."

#: frontend/src/components/ListBulkActions.vue:186
#: frontend/src/components/Modals/AssignmentModal.vue:4
msgid "Assign To"
msgstr "Tilldela till"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:435
msgid "Assign condition is required"
msgstr "Tilldela villkor erfordras"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:438
msgid "Assign conditions are invalid"
msgstr "Tilldelade villkor är ogiltiga"

#: frontend/src/components/AssignTo.vue:11
#: frontend/src/components/AssignToBody.vue:5
msgid "Assign to"
msgstr "Tilldela till"

#: frontend/src/components/AssignToBody.vue:63
msgid "Assign to me"
msgstr "Självtilldela"

#. Label of the assigned_to (Link) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Assigned To"
msgstr "Tilldelad Till"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:5
msgid "Assignee Rules"
msgstr "Tilldelning Regler"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:81
msgid "Assignees"
msgstr "Tilldelade"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Assignment"
msgstr "Tilldelning"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Assignment Rule"
msgstr "Tilldelning Regel"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:302
msgid "Assignment Schedule"
msgstr "Tilldelning Schema"

#: frontend/src/components/ListBulkActions.vue:154
msgid "Assignment cleared successfully"
msgstr "Tilldelning Rensad"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:145
msgid "Assignment condition"
msgstr "Tilldelning Villkor"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:471
msgid "Assignment days are required"
msgstr "Tilldelningsdagar erfordras"

#: frontend/src/components/Layouts/AppSidebar.vue:582
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:19
msgid "Assignment rule"
msgstr "Tilldelningsregel"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:667
msgid "Assignment rule created"
msgstr "Tilldelning regel skapad"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:111
msgid "Assignment rule deleted"
msgstr "Tilldelning regel borttagen"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:156
msgid "Assignment rule duplicated"
msgstr "Tilldelning regel duplicerad"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:749
msgid "Assignment rule updated"
msgstr "Tilldelning regel uppdaterad"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:188
msgid "Assignment rule {0} updated"
msgstr "Tilldelning regel {0} uppdaterad"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:7
#: frontend/src/components/Settings/Settings.vue:164
msgid "Assignment rules"
msgstr "Tilldelning Regler"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:11
msgid "Assignment rules automatically assign lead/deal to the right sales user based on predefined conditions"
msgstr "Tilldelningsregler tilldelar automatiskt potentiell kund /affär till rätt försäljning användare baserat på fördefinierade villkor"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:173
msgid "At least one field is required"
msgstr "Minst ett fält erfordras"

#: frontend/src/components/FilesUploader/FilesUploader.vue:5
#: frontend/src/components/FilesUploader/FilesUploader.vue:73
msgid "Attach"
msgstr "Bifoga"

#: frontend/src/components/CommentBox.vue:65
#: frontend/src/components/EmailEditor.vue:146 frontend/src/pages/Deal.vue:105
#: frontend/src/pages/Lead.vue:151
msgid "Attach a file"
msgstr "Bifoga fil"

#: frontend/src/pages/Deal.vue:577 frontend/src/pages/Lead.vue:424
#: frontend/src/pages/MobileDeal.vue:466 frontend/src/pages/MobileLead.vue:372
msgid "Attachments"
msgstr "Bilagor"

#: frontend/src/components/Modals/EventModal.vue:120
msgid "Attendees"
msgstr "Deltagare"

#. Label of the auth_token (Password) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Auth Token"
msgstr "Auth Token"

#. Label of the auto_update_expected_deal_value (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Auto Update Expected Deal Value"
msgstr "Automatisk Uppdatering av Förväntad Affärsvärde"

#: frontend/src/components/Settings/ForecastingSettings.vue:42
msgid "Auto update expected deal value"
msgstr "Automatisk uppdatering av förväntad affärsvärde"

#: frontend/src/components/Settings/ForecastingSettings.vue:88
msgid "Auto update of expected deal value disabled"
msgstr "Automatisk uppdatering av förväntad affärsvärde inaktiverad"

#: frontend/src/components/Settings/ForecastingSettings.vue:87
msgid "Auto update of expected deal value enabled"
msgstr "Automatisk uppdatering av förväntad affärsvärde aktiverad"

#. Description of the 'Auto Update Expected Deal Value' (Check) field in
#. DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/ForecastingSettings.vue:46
msgid "Automatically update \"Expected Deal Value\" based on the total value of associated products in a deal"
msgstr "Automatiskt uppdatera \"Förväntad Affärsvärde\" baserad på totala värdet av associerade artiklar i affär"

#: frontend/src/components/Settings/Settings.vue:161
msgid "Automation & Rules"
msgstr "Automatisering & Regler"

#: crm/api/dashboard.py:250
msgid "Average deal value of non won/lost deals"
msgstr "Genomsnittlig affärsvärde för ej vunna/förlorade affärer"

#: crm/api/dashboard.py:429
msgid "Average deal value of ongoing & won deals"
msgstr "Genomsnittlig affärsvärde för pågående och vunna affärer"

#: crm/api/dashboard.py:370
msgid "Average deal value of won deals"
msgstr "Genomsnittlig affärsvärde för vunna affärer"

#: crm/api/dashboard.py:534
msgid "Average time taken from deal creation to deal closure"
msgstr "Genomsnittlig tid från det att affären skapades till att den avslutades"

#: crm/api/dashboard.py:481
msgid "Average time taken from lead creation to deal closure"
msgstr "Genomsnittlig tidsåtgång från skapande av potentiell kund till affär avslut"

#: frontend/src/components/Dashboard/AddChartModal.vue:81
msgid "Avg deal value"
msgstr "Genomsnittlig Affärsvärde"

#: frontend/src/components/Dashboard/AddChartModal.vue:78
msgid "Avg ongoing deal value"
msgstr "Genomsnittlig Pågående Affärsvärde"

#: frontend/src/components/Dashboard/AddChartModal.vue:87
msgid "Avg time to close a deal"
msgstr "Genomsnittlig tid för att slutföra affär"

#: frontend/src/components/Dashboard/AddChartModal.vue:83
msgid "Avg time to close a lead"
msgstr "Genomsnittlig tid för potentiell kund avslut"

#: frontend/src/components/Dashboard/AddChartModal.vue:80
msgid "Avg won deal value"
msgstr "Genomsnittlig värde för vunnen affär"

#: crm/api/dashboard.py:428
msgid "Avg. deal value"
msgstr "Genomsnittlig Affärsvärde"

#: crm/api/dashboard.py:249
msgid "Avg. ongoing deal value"
msgstr "Genomsnittlig värde för pågående affär"

#: crm/api/dashboard.py:533
msgid "Avg. time to close a deal"
msgstr "Genomsnittlig tid för att slutföra affär"

#: crm/api/dashboard.py:480
msgid "Avg. time to close a lead"
msgstr "Genomsnittlig tid för potentiell kund avslut"

#: crm/api/dashboard.py:369
msgid "Avg. won deal value"
msgstr "Genomsnittlig värde för vunnen affär"

#: frontend/src/components/Dashboard/AddChartModal.vue:26
#: frontend/src/components/Dashboard/AddChartModal.vue:70
msgid "Axis chart"
msgstr "Axel Diagram"

#: frontend/src/components/Activities/EmailArea.vue:62
#: frontend/src/components/EmailEditor.vue:45
#: frontend/src/components/EmailEditor.vue:71
msgid "BCC"
msgstr "Hemlig Kopia Till"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
#: frontend/src/components/Settings/EmailAdd.vue:79
#: frontend/src/components/Settings/EmailEdit.vue:67
msgid "Back"
msgstr "Tillbaka"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
msgid "Back to file upload"
msgstr "Tillbaka till filuppladdning"

#. Label of the background_sync_frequency (Select) field in DocType 'Lead Sync
#. Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:80
msgid "Background Sync Frequency"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Backlog"
msgstr "Eftersläpning"

#: frontend/src/components/Filter.vue:350
msgid "Between"
msgstr "Mellan"

#: frontend/src/components/Settings/Settings.vue:119
msgid "Brand Settings"
msgstr "Varumärke Inställningar"

#: frontend/src/components/Settings/BrandSettings.vue:52
msgid "Brand logo"
msgstr "Varumärke Logotyp"

#: frontend/src/components/Settings/BrandSettings.vue:32
msgid "Brand name"
msgstr "Varumärke"

#: frontend/src/components/Settings/BrandSettings.vue:7
msgid "Brand settings"
msgstr "Varumärke Inställningar"

#. Label of the branding_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Branding"
msgstr "Varumärkesprofilering"

#: frontend/src/components/Modals/EditValueModal.vue:2
msgid "Bulk Edit"
msgstr "Mass Redigera"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Busy"
msgstr "Upptagen"

#: frontend/src/components/Activities/EmailArea.vue:57
#: frontend/src/components/EmailEditor.vue:35
#: frontend/src/components/EmailEditor.vue:57
msgid "CC"
msgstr "Kopia Till"

#. Name of a DocType
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "CRM Call Log"
msgstr "Samtalslogg"

#. Name of a DocType
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
msgid "CRM Communication Status"
msgstr "Konversation Status"

#. Name of a DocType
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
msgid "CRM Contacts"
msgstr "Kontakter"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/pages/Dashboard.vue:304
msgid "CRM Dashboard"
msgstr "Översikt Panel"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "CRM Deal"
msgstr "Affär"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "CRM Deal Status"
msgstr "Affär Status"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "CRM Dropdown Item"
msgstr "Rullgardin Post"

#. Name of a DocType
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "CRM Exotel Settings"
msgstr "Exotel Inställningar"

#. Name of a DocType
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "CRM Fields Layout"
msgstr "Fält Upplägg"

#. Name of a DocType
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "CRM Form Script"
msgstr "Formulär Skript"

#. Name of a DocType
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "CRM Global Settings"
msgstr "Standard Inställningar"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "CRM Holiday"
msgstr "Helgdag"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "CRM Holiday List"
msgstr "Helgdag Lista"

#. Name of a DocType
#: crm/fcrm/doctype/crm_industry/crm_industry.json
msgid "CRM Industry"
msgstr "Bransch"

#. Name of a DocType
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "CRM Invitation"
msgstr "Inbjudan"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "CRM Lead"
msgstr "Potentiell Kund"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "CRM Lead Source"
msgstr "Potentiell Kund Källa"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "CRM Lead Status"
msgstr "Potentiell Kund Status"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "CRM Lost Reason"
msgstr "Förlorad Anledning"

#. Name of a DocType
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "CRM Notification"
msgstr "Aviseringar"

#. Name of a DocType
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "CRM Organization"
msgstr "Organisation"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "CRM Portal Page"
msgstr "Portal"

#. Name of a DocType
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "CRM Product"
msgstr "Artikel"

#. Name of a DocType
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "CRM Products"
msgstr "Artiklar"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "CRM Service Day"
msgstr "Service Dag"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "CRM Service Level Agreement"
msgstr "Service Nivå Avtal"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "CRM Service Level Priority"
msgstr "Service Nivå Prioritet"

#. Name of a DocType
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "CRM Status Change Log"
msgstr "Statusändring Logg"

#. Name of a DocType
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "CRM Task"
msgstr "Uppgift"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "CRM Telephony Agent"
msgstr "Telefoni Handläggare"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "CRM Telephony Phone"
msgstr "Telefoni Telefon"

#. Name of a DocType
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "CRM Territory"
msgstr "Distrikt"

#. Name of a DocType
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "CRM Twilio Settings"
msgstr "Twilio Inställningar"

#. Name of a DocType
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "CRM View Settings"
msgstr "Visa Inställningar"

#: frontend/src/components/Settings/CurrencySettings.vue:35
msgid "CRM currency for all monetary values. Once set, cannot be edited."
msgstr "Valuta för alla monetära värden. Kan inte redigeras när den väl är angiven."

#: frontend/src/components/ViewControls.vue:272
msgid "CSV"
msgstr "CSV"

#: frontend/src/components/Modals/CallLogDetailModal.vue:8
msgid "Call Details"
msgstr "Samtal Information"

#. Label of the receiver (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call Received By"
msgstr "Samtal Mottagen Av"

#. Description of the 'Duration' (Duration) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call duration in seconds"
msgstr "Samtalslängd i sekunder"

#: frontend/src/components/Layouts/AppSidebar.vue:556
msgid "Call log"
msgstr "Samtalslogg"

#: frontend/src/components/Telephony/CallUI.vue:10
msgid "Call using {0}"
msgstr "Ring via {0}"

#: frontend/src/components/Modals/EventModal.vue:63
#: frontend/src/components/Modals/NoteModal.vue:28
#: frontend/src/components/Modals/TaskModal.vue:30
msgid "Call with John Doe"
msgstr "Samtal med John Doe"

#. Label of the caller (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Caller"
msgstr "Uppringare"

#: frontend/src/components/Telephony/CallUI.vue:27
msgid "Calling Medium"
msgstr "Uppringning Medium"

#: frontend/src/components/Telephony/TwilioCallUI.vue:40
#: frontend/src/components/Telephony/TwilioCallUI.vue:140
msgid "Calling..."
msgstr "Ringer..."

#: frontend/src/pages/Deal.vue:562 frontend/src/pages/Lead.vue:409
#: frontend/src/pages/MobileDeal.vue:450 frontend/src/pages/MobileLead.vue:357
msgid "Calls"
msgstr "Samtal"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:51
msgid "Camera"
msgstr "Kamera"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:81
#: frontend/src/components/Calendar/CalendarEventPanel.vue:620
#: frontend/src/components/ColumnSettings.vue:123
#: frontend/src/components/Dashboard/AddChartModal.vue:40
#: frontend/src/components/DeleteLinkedDocModal.vue:115
#: frontend/src/components/Modals/AssignmentModal.vue:69
#: frontend/src/components/Modals/EventModal.vue:151
#: frontend/src/components/Modals/LostReasonModal.vue:43
#: frontend/src/components/Telephony/TwilioCallUI.vue:73
#: frontend/src/components/ViewControls.vue:57
#: frontend/src/components/ViewControls.vue:154
#: frontend/src/pages/Dashboard.vue:32
msgid "Cancel"
msgstr "Annullera"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Canceled"
msgstr "Annullerad"

#: frontend/src/components/Settings/Users.vue:124
msgid "Cannot change role of user with Admin access"
msgstr "Kan inte ändra roll för användare med Administratör behörighet"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:34
msgid "Cannot delete standard items {0}"
msgstr "Kan inte ta bort standard artikel {0}"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:171
msgid "Cannot enable rule without adding users in it"
msgstr "Kan inte aktivera regel utan att lägga till användare i den"

#: frontend/src/components/FilesUploader/FilesUploader.vue:91
msgid "Capture"
msgstr "Fånga"

#: frontend/src/components/Layouts/AppSidebar.vue:561
msgid "Capturing leads"
msgstr "Hämta in Potentiella Kunder"

#. Label of the category (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Category"
msgstr "Kategori"

#: frontend/src/components/Controls/ImageUploader.vue:19
#: frontend/src/components/Layouts/AppSidebar.vue:490
msgid "Change"
msgstr "Ändra"

#: frontend/src/components/Modals/ChangePasswordModal.vue:2
#: frontend/src/components/Settings/ProfileSettings.vue:65
msgid "Change Password"
msgstr "Ändra Lösenord"

#: frontend/src/components/Layouts/AppSidebar.vue:481
#: frontend/src/components/Layouts/AppSidebar.vue:489
msgid "Change deal status"
msgstr "Ändra affär status"

#: frontend/src/components/Settings/ProfileSettings.vue:26
#: frontend/src/pages/Contact.vue:47 frontend/src/pages/Lead.vue:88
#: frontend/src/pages/MobileContact.vue:37
#: frontend/src/pages/MobileOrganization.vue:37
#: frontend/src/pages/Organization.vue:47
msgid "Change image"
msgstr "Ändra Bild"

#: frontend/src/components/Activities/TaskArea.vue:45
msgid "Change status"
msgstr "Ändra status"

#: frontend/src/pages/Dashboard.vue:22
msgid "Chart"
msgstr "Diagram"

#: frontend/src/components/Dashboard/AddChartModal.vue:12
msgid "Chart Type"
msgstr "Diagram Typ"

#: frontend/src/components/Modals/ConvertToDealModal.vue:29
#: frontend/src/components/Modals/ConvertToDealModal.vue:55
#: frontend/src/pages/MobileLead.vue:122 frontend/src/pages/MobileLead.vue:149
msgid "Choose Existing"
msgstr "Välj Befintlig"

#: frontend/src/components/Modals/DealModal.vue:44
msgid "Choose Existing Contact"
msgstr "Välj Befintlig Kontakt"

#: frontend/src/components/Modals/DealModal.vue:37
msgid "Choose Existing Organization"
msgstr "Välj Befintlig Organisation"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:9
msgid "Choose how {0} are assigned among salespeople."
msgstr "Välj hur {0} fördelas bland försäljare."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:306
msgid "Choose the days of the week when this rule should be active."
msgstr "Välj veckodagar då denna regel ska vara aktiv."

#: frontend/src/components/Settings/EmailAdd.vue:9
msgid "Choose the email service provider you want to configure."
msgstr "Välj e-post tjänst leverantör som ska konfigureras."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:229
msgid "Choose which {0} are affected by this un-assignment rule."
msgstr "Välj vilka {0} som påverkas av denna regel för borttagning av tilldelning."

#: frontend/src/components/Controls/Link.vue:59
msgid "Clear"
msgstr "Rensa"

#: frontend/src/components/ListBulkActions.vue:136
#: frontend/src/components/ListBulkActions.vue:144
#: frontend/src/components/ListBulkActions.vue:190
msgid "Clear Assignment"
msgstr "Rensa Tilldelning"

#: frontend/src/components/SortBy.vue:153
msgid "Clear Sort"
msgstr "Rensa Sortering"

#. Label of the clear_table (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Clear Table"
msgstr "Rensa Tabell"

#: frontend/src/components/Filter.vue:21 frontend/src/components/Filter.vue:146
msgid "Clear all Filter"
msgstr "Rensa alla Filter"

#: crm/templates/emails/helpdesk_invitation.html:7
msgid "Click on the link below to complete your registration and set a new password"
msgstr "Klicka på länk nedan  att slutföra din registrering och ange ny lösenord"

#: frontend/src/components/Notifications.vue:26
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:54
msgid "Close"
msgstr "Stäng"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:38
msgid "Close panel"
msgstr "Stäng panel"

#. Label of the closed_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Closed Date"
msgstr "Avslutad Datum"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Collapse"
msgstr "Fäll In"

#: frontend/src/components/FieldLayoutEditor.vue:344
msgid "Collapsible"
msgstr "Infällbar"

#. Label of the color (Select) field in DocType 'CRM Deal Status'
#. Label of the color (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Color"
msgstr "Färg"

#: frontend/src/components/FieldLayoutEditor.vue:417
msgid "Column"
msgstr "Kolumn"

#. Label of the column_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:12
msgid "Column Field"
msgstr "Kolumnfält"

#. Label of the columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:4
msgid "Columns"
msgstr "Kolumner"

#. Label of the comment (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/CommentBox.vue:78
#: frontend/src/components/CommunicationArea.vue:16
#: frontend/src/components/Layouts/AppSidebar.vue:579
msgid "Comment"
msgstr "Kommentar"

#: frontend/src/pages/Deal.vue:547 frontend/src/pages/Lead.vue:394
#: frontend/src/pages/MobileDeal.vue:440 frontend/src/pages/MobileLead.vue:347
msgid "Comments"
msgstr "Kommentarer"

#: crm/api/dashboard.py:920
msgid "Common reasons for losing deals"
msgstr "Vanliga anledningar till att affärer går förlorade"

#. Label of the communication_status (Link) field in DocType 'CRM Deal'
#. Label of the communication_status (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Communication Status"
msgstr "Konversation Status"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Communication Statuses"
msgstr "Konversation Status"

#. Label of the erpnext_company (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Company in ERPNext Site"
msgstr "Bolag i Affärssystem Webbplats"

#: crm/templates/emails/helpdesk_invitation.html:10
msgid "Complete Registration"
msgstr "Slutför Registrering"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Completed"
msgstr "Klar"

#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Computer"
msgstr "Dator"

#. Label of the condition (Code) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Condition"
msgstr "Villkor"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:188
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:272
msgid "Conditions for this rule were created from"
msgstr "Villkoren för denna regel skapades från"

#: frontend/src/components/Settings/HomeActions.vue:10
msgid "Configure actions that appear on the home dropdown"
msgstr "Konfigurera åtgärder som visas i rullgardinsmenyn för startsida"

#: frontend/src/components/Settings/ForecastingSettings.vue:9
msgid "Configure forecasting feature to help predict sales performance and growth"
msgstr "Konfigurera prognosfunktion för att hjälpa till att förutsäga försäljning resultat och tillväxt"

#: frontend/src/components/Settings/TelephonySettings.vue:17
msgid "Configure telephony settings for your CRM"
msgstr "Konfigurera telefoni inställningar för Säljstöd"

#: frontend/src/components/Settings/CurrencySettings.vue:11
msgid "Configure the currency and exchange rate provider for your CRM"
msgstr "Konfigurera valuta och växelkurs leverantör för ditt Säljstöd"

#: frontend/src/components/Settings/CurrencySettings.vue:63
msgid "Configure the exchange rate provider for your CRM"
msgstr "Konfigurera Valuta Växling Leverantör för Säljstöd"

#: frontend/src/components/Settings/BrandSettings.vue:10
msgid "Configure your brand name, logo, and favicon"
msgstr "Konfigurera varumärke, logotyp och favicon"

#: frontend/src/composables/frappecloud.js:29
msgid "Confirm"
msgstr "Bekräfta"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:139
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:239
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:209
msgid "Confirm Delete"
msgstr "Bekräfta Borttagning"

#: frontend/src/components/Modals/ChangePasswordModal.vue:18
msgid "Confirm Password"
msgstr "Bekräfta Lösenord"

#: frontend/src/components/Settings/Users.vue:228
msgid "Confirm Remove"
msgstr "Bekräfta Borttagning"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:609
msgid "Confirm overwrite"
msgstr "Bekräfta överskrivning"

#: frontend/src/pages/Welcome.vue:35
msgid "Connect your email"
msgstr "Anslut din e-post"

#. Label of the contact (Link) field in DocType 'CRM Contacts'
#. Label of the contact (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:552
#: frontend/src/components/Modals/ConvertToDealModal.vue:51
#: frontend/src/pages/MobileLead.vue:145
msgid "Contact"
msgstr "Kontakt"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:259
msgid "Contact Already Exists"
msgstr "Kontakt Finns Redan"

#: frontend/src/components/Modals/AboutModal.vue:77
msgid "Contact Support"
msgstr "Kontakt Support"

#: frontend/src/components/Modals/EditValueModal.vue:20
msgid "Contact Us"
msgstr "Kontakta Oss"

#: frontend/src/pages/Deal.vue:657 frontend/src/pages/MobileDeal.vue:544
msgid "Contact added"
msgstr "Kontakt tillagd"

#: frontend/src/pages/Deal.vue:647 frontend/src/pages/MobileDeal.vue:534
msgid "Contact already added"
msgstr "Kontakt redan tillagd"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:258
msgid "Contact already exists with {0}"
msgstr "Kontakt finns redan med {0}"

#: frontend/src/pages/Contact.vue:287 frontend/src/pages/MobileContact.vue:252
msgid "Contact image updated"
msgstr "Kontakt bild uppdaterad"

#: frontend/src/pages/Deal.vue:668 frontend/src/pages/MobileDeal.vue:555
msgid "Contact removed"
msgstr "Kontakt borttagen"

#: frontend/src/pages/Contact.vue:432 frontend/src/pages/Contact.vue:445
#: frontend/src/pages/Contact.vue:458 frontend/src/pages/Contact.vue:468
#: frontend/src/pages/MobileContact.vue:432
#: frontend/src/pages/MobileContact.vue:445
#: frontend/src/pages/MobileContact.vue:458
#: frontend/src/pages/MobileContact.vue:468
msgid "Contact updated"
msgstr "Kontakt uppdaterad"

#. Label of the contacts_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the contacts (Table) field in DocType 'CRM Deal'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Contact.vue:242 frontend/src/pages/MobileContact.vue:212
#: frontend/src/pages/MobileOrganization.vue:331
msgid "Contacts"
msgstr "Kontakter"

#. Label of the content (Text Editor) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:33
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:89
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:102
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:89
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:102
msgid "Content"
msgstr "Innehåll "

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:78
msgid "Content Type"
msgstr "Innehållstyp"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:159
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:163
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:162
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:166
msgid "Content is required"
msgstr "Innehåll erfordras"

#: frontend/src/components/Layouts/AppSidebar.vue:380
#: frontend/src/components/ListBulkActions.vue:88
#: frontend/src/components/Modals/ConvertToDealModal.vue:83
#: frontend/src/pages/MobileLead.vue:54 frontend/src/pages/MobileLead.vue:108
msgid "Convert"
msgstr "Konvertera"

#: frontend/src/components/Layouts/AppSidebar.vue:371
#: frontend/src/components/Layouts/AppSidebar.vue:379
msgid "Convert lead to deal"
msgstr "Konvertera Potentiell Kund till Affär"

#: frontend/src/components/ListBulkActions.vue:80
#: frontend/src/components/ListBulkActions.vue:197
#: frontend/src/components/Modals/ConvertToDealModal.vue:7
#: frontend/src/pages/Lead.vue:38 frontend/src/pages/MobileLead.vue:104
msgid "Convert to Deal"
msgstr "Konvertera till Affär"

#. Label of the converted (Check) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Converted"
msgstr "Konverterad"

#: frontend/src/components/ListBulkActions.vue:96
msgid "Converted successfully"
msgstr "Konverterad"

#: frontend/src/utils/index.js:338
msgid "Copied to clipboard"
msgstr "Kopierad till urklipp"

#: crm/api/dashboard.py:626 crm/api/dashboard.py:763 crm/api/dashboard.py:824
#: crm/api/dashboard.py:927
msgid "Count"
msgstr "Antal"

#: frontend/src/components/Modals/AddressModal.vue:98
#: frontend/src/components/Modals/CallLogModal.vue:101
#: frontend/src/components/Modals/ContactModal.vue:40
#: frontend/src/components/Modals/CreateDocumentModal.vue:92
#: frontend/src/components/Modals/DealModal.vue:66
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:20
#: frontend/src/components/Modals/EventModal.vue:159
#: frontend/src/components/Modals/LeadModal.vue:37
#: frontend/src/components/Modals/NoteModal.vue:52
#: frontend/src/components/Modals/OrganizationModal.vue:41
#: frontend/src/components/Modals/TaskModal.vue:106
#: frontend/src/components/Modals/ViewModal.vue:43
#: frontend/src/components/Settings/EmailAdd.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:23
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:30
#: frontend/src/pages/Calendar.vue:10 frontend/src/pages/CallLogs.vue:13
#: frontend/src/pages/Contacts.vue:13 frontend/src/pages/Contacts.vue:60
#: frontend/src/pages/Deals.vue:13 frontend/src/pages/Deals.vue:236
#: frontend/src/pages/Leads.vue:13 frontend/src/pages/Leads.vue:262
#: frontend/src/pages/Notes.vue:9 frontend/src/pages/Notes.vue:96
#: frontend/src/pages/Organizations.vue:13
#: frontend/src/pages/Organizations.vue:60 frontend/src/pages/Tasks.vue:13
#: frontend/src/pages/Tasks.vue:186
msgid "Create"
msgstr "Skapa"

#: frontend/src/components/Modals/DealModal.vue:8
msgid "Create Deal"
msgstr "Skapa Affär"

#: frontend/src/components/Modals/LeadModal.vue:8
msgid "Create Lead"
msgstr "Skapa Potentiell Kund"

#: frontend/src/components/Controls/Link.vue:50
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:69
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:45
#: frontend/src/components/PrimaryDropdown.vue:41
msgid "Create New"
msgstr "Skapa Ny"

#: frontend/src/components/Activities/Activities.vue:388
#: frontend/src/components/Modals/NoteModal.vue:6
msgid "Create Note"
msgstr "Skapa Anteckning"

#: frontend/src/components/Activities/Activities.vue:403
#: frontend/src/components/Modals/TaskModal.vue:6
msgid "Create Task"
msgstr "Skapa Uppgift"

#: frontend/src/components/Modals/ViewModal.vue:9
#: frontend/src/components/ViewControls.vue:687
msgid "Create View"
msgstr "Skapa Vy"

#: frontend/src/components/Modals/EventModal.vue:12
msgid "Create an event"
msgstr "Skapa händelse"

#. Label of the create_customer_on_status_change (Check) field in DocType
#. 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Create customer on status change"
msgstr "Skapa kund vid statusändring"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:348
#: frontend/src/pages/Calendar.vue:7
msgid "Create event"
msgstr "Skapa händelse"

#: frontend/src/components/Modals/CallLogDetailModal.vue:151
msgid "Create lead"
msgstr "Skapa Potentiell Kund"

#: frontend/src/components/Layouts/AppSidebar.vue:349
msgid "Create your first lead"
msgstr "Skapa Potentiell Kund"

#: frontend/src/components/Layouts/AppSidebar.vue:419
msgid "Create your first note"
msgstr "Skapa första anteckning"

#: frontend/src/components/Layouts/AppSidebar.vue:399
msgid "Create your first task"
msgstr "Skapa första uppgift"

#. Label of the currency (Link) field in DocType 'CRM Deal'
#. Label of the currency (Link) field in DocType 'CRM Organization'
#. Label of the currency (Link) field in DocType 'FCRM Settings'
#. Label of the currency_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/CurrencySettings.vue:31
msgid "Currency"
msgstr "Valuta"

#: frontend/src/components/Settings/Settings.vue:114
msgid "Currency & Exchange Rate"
msgstr "Valuta & Växelkurs"

#: frontend/src/components/Settings/CurrencySettings.vue:7
msgid "Currency & Exchange rate provider"
msgstr "Valuta och Växelkurs Leverantör"

#: frontend/src/components/Settings/CurrencySettings.vue:179
msgid "Currency set as {0} successfully"
msgstr "Valuta angiven som {0}"

#: crm/api/dashboard.py:872
msgid "Current pipeline distribution"
msgstr "Nuvarande pipeline distribution"

#: frontend/src/components/Layouts/AppSidebar.vue:591
msgid "Custom actions"
msgstr "Anpassade åtgärder"

#: frontend/src/components/Layouts/AppSidebar.vue:541
msgid "Custom branding"
msgstr "Anpassad varumärkesprofilering"

#: frontend/src/components/Layouts/AppSidebar.vue:590
msgid "Custom fields"
msgstr "Anpassade fält"

#: frontend/src/components/Layouts/AppSidebar.vue:593
msgid "Custom list actions"
msgstr "Anpassad lista åtgärder"

#: frontend/src/components/Layouts/AppSidebar.vue:592
msgid "Custom statuses"
msgstr "Anpassade status"

#: frontend/src/pages/Deal.vue:476
msgid "Customer created successfully"
msgstr "Kund skapad"

#: frontend/src/components/Layouts/AppSidebar.vue:587
#: frontend/src/components/Settings/Settings.vue:171
msgid "Customization"
msgstr "Anpassning"

#: frontend/src/components/ViewControls.vue:211
msgid "Customize quick filters"
msgstr "Anpassa snabbfilter"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Daily"
msgstr "Dagsvis"

#: crm/api/dashboard.py:618
msgid "Daily performance of leads, deals, and wins"
msgstr "Daglig resultat för potentiella kunder, affärer och vinster"

#: frontend/src/components/Activities/DataFields.vue:6
#: frontend/src/components/Layouts/AppSidebar.vue:580
#: frontend/src/pages/Deal.vue:552 frontend/src/pages/Lead.vue:399
#: frontend/src/pages/MobileDeal.vue:445 frontend/src/pages/MobileLead.vue:352
msgid "Data"
msgstr "Data"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Data Fields"
msgstr "Datafält"

#. Label of the date (Date) field in DocType 'CRM Holiday'
#: crm/api/dashboard.py:620 crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:202
msgid "Date"
msgstr "Datum"

#: frontend/src/components/Modals/EventModal.vue:78
msgid "Date & Time"
msgstr "Datum & Tid"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:267
#: frontend/src/components/Calendar/CalendarEventPanel.vue:288
#: frontend/src/components/Layouts/AppSidebar.vue:551
#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:20
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:59
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:59
#: frontend/src/components/Telephony/ExotelCallUI.vue:200
#: frontend/src/pages/Tasks.vue:132
msgid "Deal"
msgstr "Affär"

#. Label of the deal_owner (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Owner"
msgstr "Ansvarig"

#. Label of the deal_status (Link) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Deal Status"
msgstr "Affär Status"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Deal Statuses"
msgstr "Affär Status"

#. Label of the deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Value"
msgstr "Affärsvärde"

#: crm/api/dashboard.py:1019
msgid "Deal generation channel analysis"
msgstr "Affär generering kanal analys"

#: frontend/src/pages/Contact.vue:528 frontend/src/pages/MobileContact.vue:528
#: frontend/src/pages/MobileOrganization.vue:472
#: frontend/src/pages/Organization.vue:502
msgid "Deal owner"
msgstr "Affär Ansvarig"

#: crm/api/dashboard.py:1075 crm/api/dashboard.py:1135
msgid "Deal value"
msgstr "Affärsvärde"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Deal.vue:490 frontend/src/pages/MobileContact.vue:288
#: frontend/src/pages/MobileDeal.vue:384
#: frontend/src/pages/MobileOrganization.vue:325
msgid "Deals"
msgstr "Affärer"

#: crm/api/dashboard.py:818
#: frontend/src/components/Dashboard/AddChartModal.vue:97
msgid "Deals by ongoing & won stage"
msgstr "Affärer per pågående och vunna steg"

#: crm/api/dashboard.py:1124
#: frontend/src/components/Dashboard/AddChartModal.vue:100
msgid "Deals by salesperson"
msgstr "Affärer per säljare"

#: crm/api/dashboard.py:1018
#: frontend/src/components/Dashboard/AddChartModal.vue:107
msgid "Deals by source"
msgstr "Affärer per källa"

#: crm/api/dashboard.py:871
#: frontend/src/components/Dashboard/AddChartModal.vue:105
msgid "Deals by stage"
msgstr "Affärer per steg"

#: crm/api/dashboard.py:1064
#: frontend/src/components/Dashboard/AddChartModal.vue:99
msgid "Deals by territory"
msgstr "Affärer per distrikt"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:112
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:112
msgid "Dear {{ lead_name }}, \\n\\nThis is a reminder for the payment of {{ grand_total }}. \\n\\nThanks, \\nFrappé"
msgstr "Bästa {{ lead_name }}, \\n\\nDetta är en påminnelse om betalningen av {{ grand_total }}. \\n\\nTack, \\n."

#. Label of the default (Check) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Default"
msgstr "Standard"

#: frontend/src/components/Settings/EmailAccountCard.vue:41
msgid "Default Inbox"
msgstr "Standard Inkorg"

#: frontend/src/components/Settings/emailConfig.js:44
msgid "Default Incoming"
msgstr "Standard Inkommande"

#. Label of the default_medium (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Default Medium"
msgstr "Standard Medium"

#: frontend/src/components/Settings/emailConfig.js:52
msgid "Default Outgoing"
msgstr "Standard Utgående"

#. Label of the default_priority (Check) field in DocType 'CRM Service Level
#. Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "Default Priority"
msgstr "Standard Prioritet"

#: frontend/src/components/Settings/EmailAccountCard.vue:43
msgid "Default Sending"
msgstr "Standard Skicka"

#: frontend/src/components/Settings/EmailAccountCard.vue:39
msgid "Default Sending and Inbox"
msgstr "Standard Utgående/Inkommande E-post Konto"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:33
msgid "Default Service Level Agreement already exists for {0}"
msgstr "Standard Service Nivå Avtal för {0} finns redan."

#: frontend/src/components/Settings/TelephonySettings.vue:44
msgid "Default calling medium for logged in user"
msgstr "Standard samtal medium för inloggad användare"

#: frontend/src/components/Telephony/CallUI.vue:112
msgid "Default calling medium set successfully to {0}"
msgstr "Standard Samtal medium angiven till {0}"

#: frontend/src/components/Settings/TelephonySettings.vue:280
msgid "Default calling medium updated successfully"
msgstr "Standard samtal medium uppdaterad"

#: frontend/src/components/Settings/TelephonySettings.vue:37
msgid "Default medium"
msgstr "Standard Medium"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:18
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:30
msgid "Default statuses, custom fields and layouts restored successfully."
msgstr "Standard status, anpassade fält och upplägg återställdes."

#: frontend/src/components/Activities/AttachmentArea.vue:131
#: frontend/src/components/Activities/NoteArea.vue:12
#: frontend/src/components/Activities/TaskArea.vue:55
#: frontend/src/components/Activities/TaskArea.vue:63
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:8
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:48
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:121
#: frontend/src/components/Controls/Grid.vue:329
#: frontend/src/components/DeleteLinkedDocModal.vue:10
#: frontend/src/components/DeleteLinkedDocModal.vue:90
#: frontend/src/components/Kanban/KanbanView.vue:222
#: frontend/src/components/ListBulkActions.vue:179
#: frontend/src/components/Modals/EventModal.vue:407
#: frontend/src/components/Modals/EventModal.vue:411
#: frontend/src/components/PrimaryDropdownItem.vue:42
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:129
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:229
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:199
#: frontend/src/components/ViewControls.vue:1116
#: frontend/src/components/ViewControls.vue:1127
#: frontend/src/pages/Calendar.vue:299 frontend/src/pages/Calendar.vue:303
#: frontend/src/pages/Contact.vue:106 frontend/src/pages/Deal.vue:111
#: frontend/src/pages/Lead.vue:157 frontend/src/pages/MobileContact.vue:79
#: frontend/src/pages/MobileContact.vue:263
#: frontend/src/pages/MobileDeal.vue:515
#: frontend/src/pages/MobileOrganization.vue:72
#: frontend/src/pages/MobileOrganization.vue:264
#: frontend/src/pages/Notes.vue:43 frontend/src/pages/Organization.vue:89
#: frontend/src/pages/Tasks.vue:371
msgid "Delete"
msgstr "Ta bort"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:26
msgid "Delete & Restore"
msgstr "Ta bort och Återställ"

#: frontend/src/components/Activities/TaskArea.vue:59
msgid "Delete Task"
msgstr "Ta bort Uppgift"

#: frontend/src/components/ViewControls.vue:1112
#: frontend/src/components/ViewControls.vue:1120
msgid "Delete View"
msgstr "Ta bort Vy"

#: frontend/src/components/DeleteLinkedDocModal.vue:66
msgid "Delete all"
msgstr "Ta bort alla"

#: frontend/src/components/Activities/AttachmentArea.vue:58
#: frontend/src/components/Activities/AttachmentArea.vue:127
msgid "Delete attachment"
msgstr "Ta bort bilaga"

#: frontend/src/pages/MobileContact.vue:259
msgid "Delete contact"
msgstr "Ta bort kontakt"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:25
msgid "Delete event"
msgstr "Ta bort händelse"

#: frontend/src/components/Settings/InviteUserPage.vue:79
msgid "Delete invitation"
msgstr "Ta bort inbjudan"

#: frontend/src/components/DeleteLinkedDocModal.vue:230
msgid "Delete linked item"
msgstr "Ta bort länkad artikel"

#: frontend/src/components/DeleteLinkedDocModal.vue:11
msgid "Delete or unlink linked documents"
msgstr "Ta bort eller ta bort länkade dokument"

#: frontend/src/components/DeleteLinkedDocModal.vue:23
msgid "Delete or unlink these linked documents before deleting this document"
msgstr "Ta bort eller ta bort länk till länkade dokument innan dokument tas bort"

#: frontend/src/pages/MobileOrganization.vue:260
msgid "Delete organization"
msgstr "Ta bort organisation"

#: frontend/src/components/DeleteLinkedDocModal.vue:67
msgid "Delete {0} item(s)"
msgstr "Ta bort {0} artikel(ar)"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:28
msgid "Delete {0} items"
msgstr "Ta bort {0} artikel"

#. Label of the description (Text Editor) field in DocType 'CRM Holiday'
#. Label of the description (Text Editor) field in DocType 'CRM Lost Reason'
#. Label of the description (Text Editor) field in DocType 'CRM Product'
#. Label of the description (Text Editor) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:152
#: frontend/src/components/Modals/EventModal.vue:134
#: frontend/src/components/Modals/TaskModal.vue:36
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:112
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:113
msgid "Description"
msgstr "Beskrivning"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:426
msgid "Description is required"
msgstr "Beskrivning erfordras"

#: frontend/src/components/Apps.vue:63
msgid "Desk"
msgstr "Skrivbord"

#. Label of the details (Tab Break) field in DocType 'CRM Lead'
#. Label of the details (Text Editor) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/pages/MobileContact.vue:283
#: frontend/src/pages/MobileDeal.vue:424 frontend/src/pages/MobileLead.vue:331
#: frontend/src/pages/MobileOrganization.vue:320
msgid "Details"
msgstr "Detaljer"

#. Label of the call_receiving_device (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:39
msgid "Device"
msgstr "Enhet"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Disable"
msgstr "Inaktivera"

#. Label of the disabled (Check) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Disabled"
msgstr "Inaktiverad"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:626
#: frontend/src/components/CommentBox.vue:74
#: frontend/src/components/EmailEditor.vue:161
msgid "Discard"
msgstr "Ångra"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:614
msgid "Discard unsaved changes?"
msgstr "Förkasta osparade ändringar?"

#. Label of the discount_percentage (Percent) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount %"
msgstr "Rabatt %"

#. Label of the discount_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount Amount"
msgstr "Rabatt Belopp"

#. Label of the dt (Link) field in DocType 'CRM Form Script'
#. Label of the dt (Link) field in DocType 'CRM Global Settings'
#. Label of the dt (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "DocType"
msgstr "DocType"

#. Label of the dt (Link) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Document Type"
msgstr "DocType"

#: frontend/src/data/document.js:30
msgid "Document does not exist"
msgstr "Dokument finns inte"

#: crm/api/activities.py:19
msgid "Document not found"
msgstr "Dokument hittades inte"

#: frontend/src/data/document.js:45
msgid "Document updated successfully"
msgstr "Dokument uppdaterad"

#: frontend/src/components/Modals/AboutModal.vue:62
msgid "Documentation"
msgstr "Dokumentation"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Done"
msgstr "Klar"

#: frontend/src/components/Dashboard/AddChartModal.vue:33
#: frontend/src/components/Dashboard/AddChartModal.vue:71
msgid "Donut chart"
msgstr "Ring Diagram"

#: frontend/src/components/Activities/AudioPlayer.vue:166
#: frontend/src/components/ViewControls.vue:254
msgid "Download"
msgstr "Ladda ner"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:24
msgid "Drag and drop files here or upload from"
msgstr "Dra och släpp filer här eller ladda upp från"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:56
msgid "Drop files here"
msgstr "Släpp filer här"

#. Label of the dropdown_items_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Dropdown Items"
msgstr "Rullgardin Post"

#. Label of the due_date (Datetime) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Due Date"
msgstr "Förfallo Datum"

#. Option for the 'Type' (Select) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: frontend/src/components/Modals/EventModal.vue:158
#: frontend/src/components/Modals/ViewModal.vue:42
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:57
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:119
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:224
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:23
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:194
#: frontend/src/components/ViewControls.vue:1068
msgid "Duplicate"
msgstr "Kopiera"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:38
msgid "Duplicate Assignment Rule"
msgstr "Duplicera Tilldelning Regel"

#: frontend/src/components/Modals/ViewModal.vue:8
msgid "Duplicate View"
msgstr "Duplicera Vy"

#: frontend/src/components/Modals/EventModal.vue:11
msgid "Duplicate an event"
msgstr "Duplicera händelse"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:33
#: frontend/src/components/Calendar/CalendarEventPanel.vue:347
#: frontend/src/components/Calendar/CalendarEventPanel.vue:432
msgid "Duplicate event"
msgstr "Duplicera händelse"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "Duplicate template"
msgstr "Duplicera mall"

#. Label of the duration (Duration) field in DocType 'CRM Call Log'
#. Label of the duration (Duration) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Duration"
msgstr "Varaktighet"

#: frontend/src/components/Layouts/AppSidebar.vue:604
#: frontend/src/components/Settings/Settings.vue:197
msgid "ERPNext"
msgstr "Affärssystem"

#. Name of a DocType
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext CRM Settings"
msgstr "Affärssystem Säljstöd Inställningar"

#. Label of the section_break_oubd (Section Break) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site API's"
msgstr "Affärssystem Webbplats API'er"

#. Label of the erpnext_site_url (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site URL"
msgstr "Affärssystem Webbplats URL"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:25
msgid "ERPNext is not installed in the current site"
msgstr "Afförsystem är inte installerat på aktuell webbplats"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:98
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:124
msgid "ERPNext is not integrated with the CRM"
msgstr "Affärssystem är inte integrerat med Säljstöd"

#: frontend/src/components/Settings/ERPNextSettings.vue:4
msgid "ERPNext settings"
msgstr "Affärssystem Inställningar"

#: frontend/src/components/Settings/ERPNextSettings.vue:5
msgid "ERPNext settings updated"
msgstr "Affärssystem Inställningar uppdaterade"

#: frontend/src/components/FieldLayout/Field.vue:91
#: frontend/src/components/FieldLayoutEditor.vue:313
#: frontend/src/components/FieldLayoutEditor.vue:339
#: frontend/src/components/ListBulkActions.vue:172
#: frontend/src/components/PrimaryDropdownItem.vue:35
#: frontend/src/components/ViewControls.vue:1086
#: frontend/src/pages/Dashboard.vue:16
msgid "Edit"
msgstr "Redigera"

#: frontend/src/components/Modals/CallLogModal.vue:97
msgid "Edit Call Log"
msgstr "Redigera Samtalslogg"

#: frontend/src/components/Modals/DataFieldsModal.vue:7
msgid "Edit Data Fields Layout"
msgstr "Redigera Datafält Upplägg"

#: frontend/src/components/Settings/EmailEdit.vue:6
msgid "Edit Email"
msgstr "Redigera E-post"

#: frontend/src/components/Modals/SidePanelModal.vue:7
msgid "Edit Field Layout"
msgstr "Redigera Fält Upplägg"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:7
msgid "Edit Grid Fields Layout"
msgstr "Redigera Rutnät Fält Upplägg"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:7
msgid "Edit Grid Row Fields Layout"
msgstr "Redigera Rutnätsrad Fält Upplägg"

#: frontend/src/components/Modals/NoteModal.vue:6
msgid "Edit Note"
msgstr "Redigera Anteckning"

#: frontend/src/components/Modals/QuickEntryModal.vue:7
msgid "Edit Quick Entry Layout"
msgstr "Redigera Snabbinmatning Upplägg"

#: frontend/src/components/Modals/TaskModal.vue:6
msgid "Edit Task"
msgstr "Redigera Uppgift"

#: frontend/src/components/Modals/ViewModal.vue:6
msgid "Edit View"
msgstr "Redigera Vy"

#: frontend/src/components/Modals/EventModal.vue:9
msgid "Edit an event"
msgstr "Redigera händelse"

#: frontend/src/components/Modals/CallLogDetailModal.vue:39
msgid "Edit call log"
msgstr "Redigera samtalslogg"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:18
msgid "Edit event"
msgstr "Redigera händelse"

#: frontend/src/components/Activities/DataFields.vue:17
#: frontend/src/components/Controls/GridRowModal.vue:14
#: frontend/src/components/Modals/AddressModal.vue:14
#: frontend/src/components/Modals/CallLogModal.vue:16
#: frontend/src/components/Modals/ContactModal.vue:16
#: frontend/src/components/Modals/CreateDocumentModal.vue:16
#: frontend/src/components/Modals/DealModal.vue:16
#: frontend/src/components/Modals/LeadModal.vue:16
#: frontend/src/components/Modals/OrganizationModal.vue:16
msgid "Edit fields layout"
msgstr "Redigera fältlayout"

#: frontend/src/components/Controls/Grid.vue:57
msgid "Edit grid fields"
msgstr "Redigera rutnätsfält"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Edit note"
msgstr "Redigera Anteckning"

#: frontend/src/components/Controls/Grid.vue:297
msgid "Edit row"
msgstr "Redigera rad"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Edit task"
msgstr "Redigera uppgift"

#: frontend/src/components/Controls/GridRowModal.vue:8
msgid "Editing Row {0}"
msgstr "Redigering av Rad {0}"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:430
msgid "Editing event"
msgstr "Redigerar händelse"

#. Label of the email (Data) field in DocType 'CRM Contacts'
#. Label of the email (Data) field in DocType 'CRM Invitation'
#. Label of the email (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json frontend/src/pages/Contact.vue:518
#: frontend/src/pages/MobileContact.vue:518
#: frontend/src/pages/MobileOrganization.vue:462
#: frontend/src/pages/MobileOrganization.vue:490
#: frontend/src/pages/Organization.vue:492
#: frontend/src/pages/Organization.vue:520
msgid "Email"
msgstr "E-post"

#: frontend/src/components/Settings/Settings.vue:148
msgid "Email Accounts"
msgstr "E-post Konton"

#: frontend/src/components/Settings/emailConfig.js:168
msgid "Email ID is required"
msgstr "E-post erfordras"

#. Label of the email_sent_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Email Sent At"
msgstr "E-post skickad Kl"

#: frontend/src/components/Settings/Settings.vue:145
msgid "Email Settings"
msgstr "E-post Inställningar"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:4
#: frontend/src/components/Settings/Settings.vue:154
msgid "Email Templates"
msgstr "E-Post Mallar"

#: frontend/src/components/Settings/EmailAdd.vue:141
msgid "Email account created successfully"
msgstr "E-post konto skapad"

#: frontend/src/components/Settings/EmailEdit.vue:208
msgid "Email account updated successfully"
msgstr "E-post konto uppdaterad"

#: frontend/src/components/Settings/EmailAccountList.vue:7
msgid "Email accounts"
msgstr "E-post Konton"

#: frontend/src/components/Layouts/AppSidebar.vue:578
msgid "Email communication"
msgstr "E-post kommunikation"

#: frontend/src/components/EmailEditor.vue:209
msgid "Email from Lead"
msgstr "E-post från Potentiell Kund"

#: frontend/src/components/Layouts/AppSidebar.vue:557
msgid "Email template"
msgstr "E-post mall"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:7
msgid "Email templates"
msgstr "E-post mallar"

#: frontend/src/pages/Deal.vue:542 frontend/src/pages/Lead.vue:389
#: frontend/src/pages/MobileDeal.vue:435 frontend/src/pages/MobileLead.vue:342
msgid "Emails"
msgstr "E-post "

#: frontend/src/components/ListViews/ListRows.vue:12
msgid "Empty"
msgstr "Tom"

#: frontend/src/components/Filter.vue:123
msgid "Empty - Choose a field to filter by"
msgstr "Tom – Välj fält att filtrera efter"

#: frontend/src/components/SortBy.vue:130
msgid "Empty - Choose a field to sort by"
msgstr "Tom – Välj fält att sortera efter"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Enable"
msgstr "Aktivera"

#. Label of the enable_forecasting (Check) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Enable Forecasting"
msgstr "Aktivera Prognoser"

#: frontend/src/components/Settings/emailConfig.js:28
msgid "Enable Incoming"
msgstr "Aktivera Inkommande"

#: frontend/src/components/Settings/emailConfig.js:36
msgid "Enable Outgoing"
msgstr "Aktivera Utgående"

#: frontend/src/components/Settings/ForecastingSettings.vue:20
msgid "Enable forecasting"
msgstr "Aktivera Prognoser"

#. Label of the enabled (Check) field in DocType 'CRM Exotel Settings'
#. Label of the enabled (Check) field in DocType 'CRM Form Script'
#. Label of the enabled (Check) field in DocType 'CRM Service Level Agreement'
#. Label of the enabled (Check) field in DocType 'CRM Twilio Settings'
#. Label of the enabled (Check) field in DocType 'ERPNext CRM Settings'
#. Label of the enabled (Check) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:32
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:21
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:18
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:20
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:18
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:61
msgid "Enabled"
msgstr "Aktiverad"

#. Label of the enabled (Check) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Enabled?"
msgstr "Aktiverad?"

#. Label of the end_date (Date) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "End Date"
msgstr "Slut Datum"

#. Label of the end_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the end_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:242
#: frontend/src/components/Modals/EventModal.vue:112
msgid "End Time"
msgstr "Slut Tid"

#: frontend/src/composables/event.js:206
msgid "End time should be after start time"
msgstr "Sluttiden ska vara efter starttiden"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:88
msgid "Enter Access Token"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:70
msgid "Enter Source Name"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:115
msgid "Enter access key"
msgstr "Ange åtkomstnyckel"

#: frontend/src/components/Settings/BrandSettings.vue:33
msgid "Enter brand name"
msgstr "Ange varumärke namn"

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:30
msgid "Enter your Facebook Access Token"
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:347
msgid "Enter {0}"
msgstr "Ange {0}"

#: frontend/src/components/Filter.vue:66 frontend/src/components/Filter.vue:99
#: frontend/src/components/Filter.vue:267
#: frontend/src/components/Filter.vue:288
#: frontend/src/components/Filter.vue:305
#: frontend/src/components/Filter.vue:316
#: frontend/src/components/Filter.vue:327
#: frontend/src/components/Filter.vue:343
msgid "Equals"
msgstr "Lika"

#: frontend/src/components/Modals/ConvertToDealModal.vue:176
msgid "Error converting to deal: {0}"
msgstr "Fel vid konvertering till affär: {0}"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:245
msgid "Error creating Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:362
msgid "Error syncing leads"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:226
msgid "Error updating Lead Sync Source"
msgstr ""

#: frontend/src/pages/Deal.vue:741 frontend/src/pages/Lead.vue:469
#: frontend/src/pages/MobileDeal.vue:612 frontend/src/pages/MobileLead.vue:412
msgid "Error updating field"
msgstr "Fel vid uppdatering av fält"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:279
msgid "Error while creating customer in ERPNext, check error log for more details"
msgstr "Fel vid skapande av kund i System, kontrollera fellogg för mer information"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:191
msgid "Error while creating prospect in ERPNext, check error log for more details"
msgstr "Fel vid skapande av prospekt i System, kontrollera fellogg för mer information"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:117
msgid "Error while fetching customer in ERPNext, check error log for more details"
msgstr "Fel vid hämtning av kund i System, kontrollera fellogg för mer information"

#: frontend/src/components/Modals/EventModal.vue:368
msgid "Event ID is required"
msgstr "Händelse ID erfordras"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:429
msgid "Event details"
msgstr "Händelse detaljer"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:182
msgid "Event title"
msgstr "Händelse Benämning"

#: frontend/src/pages/Deal.vue:557 frontend/src/pages/Lead.vue:404
msgid "Events"
msgstr "Händelser"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 10 Minutes"
msgstr ""

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 15 Minutes"
msgstr ""

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 5 Minutes"
msgstr ""

#: frontend/src/components/ViewControls.vue:268
#: frontend/src/components/ViewControls.vue:277
msgid "Excel"
msgstr "Excel"

#. Label of the exchange_rate (Float) field in DocType 'CRM Deal'
#. Label of the exchange_rate (Float) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Exchange Rate"
msgstr "Växel Kurs"

#. Label of the exchange_rate_provider_section (Section Break) field in DocType
#. 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Exchange Rate Provider"
msgstr "Växelkurs Leverantör"

#: frontend/src/components/Settings/CurrencySettings.vue:60
msgid "Exchange rate provider"
msgstr "Växelkurs Leverantör"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the exotel (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:602
#: frontend/src/components/Settings/TelephonySettings.vue:41
#: frontend/src/components/Settings/TelephonySettings.vue:63
msgid "Exotel"
msgstr "Exotel"

#: crm/integrations/exotel/handler.py:114
msgid "Exotel Exception"
msgstr "Exotel Undantag"

#. Label of the exotel_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Exotel Number"
msgstr "Exotel Nummer"

#: crm/integrations/exotel/handler.py:85
msgid "Exotel Number Missing"
msgstr "Exotel Nummer Saknas"

#: crm/integrations/exotel/handler.py:89
msgid "Exotel Number {0} is not valid"
msgstr "Exotel Nummer {0} är inte giltigt"

#: frontend/src/components/Settings/TelephonySettings.vue:293
msgid "Exotel is not enabled"
msgstr "Exotel är inte aktiverat"

#: frontend/src/components/Settings/TelephonySettings.vue:140
msgid "Exotel settings updated successfully"
msgstr "Exotel Inställningar uppdaterade"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Expand"
msgstr "Expandera"

#. Label of the expected_closure_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Closure Date"
msgstr "Förväntad Avslutning Datum"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:173
msgid "Expected Closure Date is required."
msgstr "Förväntad Avslutning Datum erfordras."

#. Label of the expected_deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Deal Value"
msgstr "Förväntad Affärsvärde"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:171
msgid "Expected Deal Value is required."
msgstr "Förväntad Affärsvärde erfordras."

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Expired"
msgstr "Utgången"

#: frontend/src/components/ViewControls.vue:203
#: frontend/src/components/ViewControls.vue:251
msgid "Export"
msgstr "Export"

#: frontend/src/components/ViewControls.vue:282
msgid "Export All {0} Record(s)"
msgstr "Exportera Alla {0} Post(er)"

#: frontend/src/components/ViewControls.vue:264
msgid "Export Type"
msgstr "Export Typ"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "FCRM Note"
msgstr "Anteckning"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "FCRM Settings"
msgstr "Inställningar"

#. Option for the 'Type' (Select) field in DocType 'Lead Sync Source'
#. Label of the facebook_section (Section Break) field in DocType 'Lead Sync
#. Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook"
msgstr "Facebook"

#. Label of the facebook_form_id (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Facebook Form ID"
msgstr ""

#. Name of a DocType
#. Label of the facebook_lead_form (Link) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook Lead Form"
msgstr ""

#. Name of a DocType
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Facebook Lead Form Question"
msgstr ""

#. Label of the facebook_lead_id (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Facebook Lead ID"
msgstr ""

#. Name of a DocType
#. Label of the facebook_page (Link) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook Page"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Failed"
msgstr "Misslyckad"

#. Name of a DocType
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Failed Lead Sync Log"
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:111
msgid "Failed to add users"
msgstr "Misslyckades med att lägga till användare"

#: crm/integrations/twilio/api.py:130
msgid "Failed to capture Twilio recording"
msgstr "Misslyckades med att spara Twilio Inspelning"

#: frontend/src/components/Settings/EmailAdd.vue:145
msgid "Failed to create email account, Invalid credentials"
msgstr "Misslyckades med att skapa e-post konto, ogiltiga inloggningsuppgifter"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:179
msgid "Failed to create template"
msgstr "Misslyckades med att skapa mall"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:186
msgid "Failed to delete Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:216
msgid "Failed to delete template"
msgstr "Misslyckades med att ta bort mall"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:205
msgid "Failed to fetch exchange rate from {0} to {1} on {2}. Please check your internet connection or try again later."
msgstr "Misslyckades med att hämta växelkurs från {0} till {1} {2}. Kontrollera internetanslutning eller försök igen senare."

#: frontend/src/data/script.js:106
msgid "Failed to load form controller: {0}"
msgstr "Misslyckades med att ladda formulär kontroll: {0}"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:242
msgid "Failed to rename template"
msgstr "Misslyckades med att ändra namn på mall"

#: crm/integrations/twilio/api.py:152
msgid "Failed to update Twilio call status"
msgstr "Misslyckades med att uppdatera Twilio Samtal Status"

#: frontend/src/components/Settings/EmailEdit.vue:213
msgid "Failed to update email account, Invalid credentials"
msgstr "Misslyckades med att uppdatera e-post konto, ogiltiga inloggningsuppgifter"

#: frontend/src/components/Modals/ChangePasswordModal.vue:95
msgid "Failed to update password"
msgstr "Misslyckades med att uppdatera lösenord"

#: frontend/src/components/Settings/ProfileSettings.vue:151
msgid "Failed to update profile"
msgstr "Misslyckades med att uppdatera profil"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:171
msgid "Failed to update source"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:208
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:201
msgid "Failed to update template"
msgstr "Misslyckades med att uppdatera mall"

#. Option for the 'Type' (Select) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Failure"
msgstr "Fel"

#. Label of the favicon (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/BrandSettings.vue:87
msgid "Favicon"
msgstr "Favicon"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:38
#: frontend/src/components/Modals/EditValueModal.vue:5
msgid "Field"
msgstr "Fält"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:19
#: frontend/src/components/Kanban/KanbanSettings.vue:48
msgid "Fields Order"
msgstr "Fältordning"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:333
msgid "File \"{0}\" was skipped because of invalid file type"
msgstr "Fil \"{0}\" hoppades över på grund av ogiltig filtyp"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:354
msgid "File \"{0}\" was skipped because only {1} uploads are allowed"
msgstr "Fil \"{0}\" hoppades över eftersom endast {1} uppladdningar är tillåtna"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:359
msgid "File \"{0}\" was skipped because only {1} uploads are allowed for DocType \"{2}\""
msgstr "Fil \"{0}\" hoppades över eftersom endast {1} uppladdningar är tillåtna för DocType \"{2}\""

#: frontend/src/components/Filter.vue:6
msgid "Filter"
msgstr "Filter"

#. Label of the filters (Code) field in DocType 'CRM View Settings'
#. Label of the filters_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Filters"
msgstr "Filter"

#. Label of the first_name (Data) field in DocType 'CRM Deal'
#. Label of the first_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/ColumnSettings.vue:103
#: frontend/src/components/Filter.vue:57 frontend/src/components/Filter.vue:88
#: frontend/src/components/SortBy.vue:6 frontend/src/components/SortBy.vue:103
#: frontend/src/components/SortBy.vue:136
msgid "First Name"
msgstr "Förnamn"

#: frontend/src/components/Modals/LeadModal.vue:134
msgid "First Name is mandatory"
msgstr "Förnamn erfordras"

#. Label of the first_responded_on (Datetime) field in DocType 'CRM Deal'
#. Label of the first_responded_on (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Responded On"
msgstr "Första Svar"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Due"
msgstr "Första Svar inom"

#. Label of the first_response_time (Duration) field in DocType 'CRM Service
#. Level Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "First Response TIme"
msgstr "Första Svarstid"

#. Label of the first_response_time (Duration) field in DocType 'CRM Deal'
#. Label of the first_response_time (Duration) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Time"
msgstr "Första Svarstid"

#: frontend/src/components/Filter.vue:130
#: frontend/src/components/Settings/ProfileSettings.vue:78
msgid "First name"
msgstr "Förnamn"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:48
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:48
msgid "For"
msgstr "För"

#: crm/api/dashboard.py:688
#: frontend/src/components/Dashboard/AddChartModal.vue:95
msgid "Forecasted revenue"
msgstr "Prognostiserad intäkt"

#: frontend/src/components/Settings/ForecastingSettings.vue:5
#: frontend/src/components/Settings/Settings.vue:109
msgid "Forecasting"
msgstr "Prognos"

#: frontend/src/components/Settings/ForecastingSettings.vue:76
msgid "Forecasting disabled successfully"
msgstr "Prognostisering inaktiverad"

#: frontend/src/components/Settings/ForecastingSettings.vue:75
msgid "Forecasting enabled successfully"
msgstr "Prognostisering aktiverad"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Form"
msgstr "Formulär"

#. Label of the form_name (Data) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Form Name"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:19
msgid "Form Script updated successfully"
msgstr "Formulär Skript uppdaterad"

#. Name of a Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Frappe CRM"
msgstr "Säljstöd"

#: frontend/src/components/Layouts/AppSidebar.vue:608
msgid "Frappe CRM mobile"
msgstr "Mobil App"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Friday"
msgstr "Fredag"

#. Label of the from (Data) field in DocType 'CRM Call Log'
#. Label of the from (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From"
msgstr "Från"

#. Label of the from_date (Date) field in DocType 'CRM Holiday List'
#. Label of the from_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Date"
msgstr "Från Datum"

#. Label of the from_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Type"
msgstr "Från Typ"

#. Label of the from_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "From User"
msgstr "Från Användare"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Fulfilled"
msgstr "Uppfylld"

#. Label of the full_name (Data) field in DocType 'CRM Contacts'
#. Label of the lead_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Full Name"
msgstr "Fullständig Namn"

#: crm/api/dashboard.py:755
#: frontend/src/components/Dashboard/AddChartModal.vue:96
msgid "Funnel conversion"
msgstr "Säljtratt konvertering"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:196
msgid "GMT+5:30"
msgstr "GMT+5:30"

#. Label of the gender (Link) field in DocType 'CRM Contacts'
#. Label of the gender (Link) field in DocType 'CRM Deal'
#. Label of the gender (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Gender"
msgstr "Kön"

#: crm/api/dashboard.py:1065
msgid "Geographic distribution of deals and revenue"
msgstr "Geografisk spridning av affärer och intäkter"

#: frontend/src/components/Modals/AboutModal.vue:57
msgid "GitHub Repository"
msgstr "GitHub Arkiv"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:582
msgid "Go back"
msgstr "Tillbaka"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:156
msgid "Go to invite page"
msgstr "Gå till inbjudningssidan"

#: frontend/src/pages/Deal.vue:95 frontend/src/pages/Lead.vue:141
msgid "Go to website"
msgstr "Gå till webbplats"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Grid Row"
msgstr "Rutnätsrad"

#. Label of the group_by_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:379
#: frontend/src/components/ViewControls.vue:611 frontend/src/utils/view.js:16
msgid "Group By"
msgstr "Gruppera efter"

#. Label of the group_by_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Group By Field"
msgstr "Gruppera efter Fält"

#: frontend/src/components/GroupBy.vue:8
msgid "Group By: "
msgstr "Gruppera efter: "

#: frontend/src/components/Telephony/TwilioCallUI.vue:63
msgid "Hang up"
msgstr "Lägg på"

#: crm/templates/emails/helpdesk_invitation.html:2
msgid "Hello"
msgstr "Hej"

#: frontend/src/components/Layouts/AppSidebar.vue:93
msgid "Help"
msgstr "Hjälp"

#: frontend/src/components/Settings/Settings.vue:203
msgid "Helpdesk"
msgstr "Helpdesk"

#. Name of a DocType
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk CRM Settings"
msgstr "Helpdesk Säljstöd Inställningar"

#. Label of the helpdesk_site_apis_section (Section Break) field in DocType
#. 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk Site API's"
msgstr "Helpdesk Webbplats API"

#. Label of the helpdesk_site_url (Data) field in DocType 'Helpdesk CRM
#. Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk Site URL"
msgstr "Helpdesk Webbplats URL"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:18
msgid "Helpdesk is not installed in the current site"
msgstr "Helpdesk är inte installerad på aktuell webbplats"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:40
msgid "Helpdesk is not integrated with the CRM"
msgstr "Helpdesk är inte integrerad med Säljstöd"

#: frontend/src/components/Settings/HelpdeskSettings.vue:4
msgid "Helpdesk settings"
msgstr "Helpdesk inställningar"

#: frontend/src/components/Settings/HelpdeskSettings.vue:5
msgid "Helpdesk settings updated"
msgstr "Helpdesk inställningar uppdaterade"

#: frontend/src/components/CommunicationArea.vue:56
msgid "Hi John, \\n\\nCan you please provide more details on this..."
msgstr "Hej John, \\n\\nKan du ge mer information om detta..."

#. Label of the hidden (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Hidden"
msgstr "Dold "

#: frontend/src/components/Activities/Activities.vue:237
msgid "Hide"
msgstr "Dölj"

#: frontend/src/components/Controls/Password.vue:19
msgid "Hide Password"
msgstr "Dölj Lösenord"

#: frontend/src/components/Activities/CallArea.vue:75
msgid "Hide Recording"
msgstr "Dölj Inspelning"

#: frontend/src/components/FieldLayoutEditor.vue:354
msgid "Hide border"
msgstr "Dölj kant"

#: frontend/src/components/FieldLayoutEditor.vue:349
msgid "Hide label"
msgstr "Dölj etikett"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Hide preview"
msgstr "Dölj förhandsvisning"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "High"
msgstr "Hög"

#. Label of the holiday_list (Link) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Holiday List"
msgstr "Helg Lista"

#. Label of the holiday_list_name (Data) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holiday List Name"
msgstr "Helg Lista Namn"

#. Label of the holidays_section (Section Break) field in DocType 'CRM Holiday
#. List'
#. Label of the holidays (Table) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holidays"
msgstr "Helg Dagar"

#: frontend/src/components/Settings/Settings.vue:174
msgid "Home Actions"
msgstr "Hem Åtgärder"

#: frontend/src/components/Layouts/AppSidebar.vue:542
#: frontend/src/components/Settings/HomeActions.vue:7
msgid "Home actions"
msgstr "Hem åtgärder"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Hourly"
msgstr "Varje Timme"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:199
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:283
msgid "I understand, add conditions"
msgstr "Jag förstår, lägg till villkor"

#. Label of the id (Data) field in DocType 'CRM Call Log'
#. Label of the id (Data) field in DocType 'Facebook Lead Form'
#. Label of the id (Data) field in DocType 'Facebook Lead Form Question'
#. Label of the id (Data) field in DocType 'Facebook Page'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "ID"
msgstr "ID"

#. Label of the icon (Code) field in DocType 'CRM Dropdown Item'
#. Label of the icon (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Icon"
msgstr "Ikon"

#: frontend/src/components/Settings/emailConfig.js:55
msgid "If enabled, all outgoing emails will be sent from this account. Note: Only one account can be default outgoing."
msgstr "Om aktiverad skickas alla utgående e-post meddelande från detta konto. Obs! Endast ett konto kan vara standard utgående."

#: frontend/src/components/Settings/emailConfig.js:47
msgid "If enabled, all replies to your company (eg: <EMAIL>) will come to this account. Note: Only one account can be default incoming."
msgstr "Om aktiverad kommer alla svar till bolag (t.ex.: <EMAIL>) att skickas till detta konto. Obs: Endast ett konto kan vara standard inkommande."

#: frontend/src/components/Settings/emailConfig.js:39
msgid "If enabled, outgoing emails can be sent from this account."
msgstr "Om aktiverad kan utgående e-post meddelande skickas från detta konto."

#: frontend/src/components/Settings/emailConfig.js:31
msgid "If enabled, records can be created from the incoming emails on this account."
msgstr "Om aktiverad kan poster skapas från inkommande e-post meddelande på detta konto."

#. Label of the image (Attach Image) field in DocType 'CRM Lead'
#. Label of the image (Attach Image) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Image"
msgstr "Bild"

#: frontend/src/components/Filter.vue:271
#: frontend/src/components/Filter.vue:292
#: frontend/src/components/Filter.vue:307
#: frontend/src/components/Filter.vue:320
#: frontend/src/components/Filter.vue:334
msgid "In"
msgstr "I"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "In Progress"
msgstr "Pågående"

#: frontend/src/components/SLASection.vue:68
msgid "In less than a minute"
msgstr "På mindre än en minut"

#: frontend/src/components/Activities/CallArea.vue:36
msgid "Inbound Call"
msgstr "Inkommande samtal"

#: frontend/src/components/Settings/EmailAccountCard.vue:45
msgid "Inbox"
msgstr "Inkorg"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Incoming"
msgstr "Inkommande"

#: frontend/src/components/Telephony/TwilioCallUI.vue:41
msgid "Incoming call..."
msgstr "Inkommande samtal..."

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Industries"
msgstr "Branscher"

#. Label of the industry (Link) field in DocType 'CRM Deal'
#. Label of the industry (Data) field in DocType 'CRM Industry'
#. Label of the industry (Link) field in DocType 'CRM Lead'
#. Label of the industry (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Industry"
msgstr "Industri"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Initiated"
msgstr "Initierad"

#: frontend/src/components/Telephony/TwilioCallUI.vue:36
msgid "Initiating call..."
msgstr "Inleder samtal..."

#: frontend/src/components/EmailEditor.vue:154
msgid "Insert Email Template"
msgstr "Infoga e-post mall"

#: frontend/src/components/CommentBox.vue:49
#: frontend/src/components/EmailEditor.vue:130
msgid "Insert Emoji"
msgstr "Infoga emoji"

#: frontend/src/components/Layouts/AppSidebar.vue:598
msgid "Integration"
msgstr "Integration"

#: crm/integrations/exotel/handler.py:73
msgid "Integration Not Enabled"
msgstr "Integration inte aktiverad"

#: frontend/src/components/Settings/Settings.vue:182
msgctxt "FCRM"
msgid "Integrations"
msgstr "Integrationer"

#: frontend/src/components/Layouts/AppSidebar.vue:529
#: frontend/src/components/Layouts/AppSidebar.vue:532
msgid "Introduction"
msgstr "Introduktion"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:33
msgid "Invalid Account SID or Auth Token."
msgstr "Ogiltigt Konto SID eller Auth Token."

#: frontend/src/components/Modals/DealModal.vue:212
#: frontend/src/components/Modals/LeadModal.vue:153
msgid "Invalid Email"
msgstr "Ogiltig e-post"

#: crm/integrations/exotel/handler.py:89
msgid "Invalid Exotel Number"
msgstr "Ogiltigt Exotel Nummer"

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:137
msgid "Invalid access token provided for Facebook."
msgstr ""

#: crm/api/dashboard.py:77
msgid "Invalid chart name"
msgstr "Ogiltig diagram namn"

#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.py:25
msgid "Invalid credentials"
msgstr "Ogiltiga inloggningsuppgifter"

#: frontend/src/components/Settings/emailConfig.js:172
msgid "Invalid email ID"
msgstr "Ogiltig e-postadress"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:601
msgid "Invalid fields, check if all are filled in and values are correct."
msgstr "Ogiltiga fält, kontrollera att alla är ifyllda och att värdena är korrekta."

#: frontend/src/pages/InvalidPage.vue:6
msgid "Invalid page or not permitted to access"
msgstr "Ogiltig sida eller ingen åtkomstbehörighet"

#: frontend/src/composables/event.js:203
msgid "Invalid start or end time"
msgstr "Ogiltig start eller sluttid"

#: frontend/src/components/Settings/Users.vue:25
msgid "Invite New User"
msgstr "Bjud in Ny Användare"

#: frontend/src/components/Settings/Settings.vue:136
msgid "Invite User"
msgstr "Bjud in Användare"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:78
#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:149
msgid "Invite agent"
msgstr "Bjud in handläggare"

#: frontend/src/components/Settings/InviteUserPage.vue:51
msgid "Invite as"
msgstr "Bjud in som"

#: frontend/src/components/Layouts/AppSidebar.vue:543
msgid "Invite users"
msgstr "Bjud in Användare"

#: frontend/src/components/Settings/InviteUserPage.vue:10
msgid "Invite users to access CRM. Specify their roles to control access and permissions"
msgstr "Bjud in användare att få åtkomst till Säljstöd. Ange deras roller för att kontrollera åtkomst och behörigheter"

#: frontend/src/components/Layouts/AppSidebar.vue:359
msgid "Invite your team"
msgstr "Bjud in Team"

#. Label of the invited_by (Link) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Invited By"
msgstr "Inbjuden av"

#: frontend/src/components/Filter.vue:273
#: frontend/src/components/Filter.vue:282
#: frontend/src/components/Filter.vue:294
#: frontend/src/components/Filter.vue:309
#: frontend/src/components/Filter.vue:322
#: frontend/src/components/Filter.vue:336
#: frontend/src/components/Filter.vue:345
msgid "Is"
msgstr "Är"

#. Label of the is_default (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Default"
msgstr "Är Standard"

#. Label of the is_erpnext_in_different_site (Check) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Is ERPNext installed on a different site?"
msgstr "Är Affärssystem installerad på annan webbplats?"

#. Label of the is_group (Check) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Is Group"
msgstr "Är Grupp"

#. Label of the is_helpdesk_in_different_site (Check) field in DocType
#. 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Is Helpdesk installed on a different site?"
msgstr "Är Helpdesk installerat på annan webbplats?"

#. Label of the is_primary (Check) field in DocType 'CRM Contacts'
#. Label of the is_primary (Check) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Is Primary"
msgstr "Är Primär"

#. Label of the is_standard (Check) field in DocType 'CRM Dropdown Item'
#. Label of the is_standard (Check) field in DocType 'CRM Form Script'
#. Label of the is_standard (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Standard"
msgstr "Är Standard"

#. Description of the 'Enable Forecasting' (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "It will make deal's \"Expected Closure Date\" & \"Expected Deal Value\" mandatory to get accurate forecasting insights"
msgstr "Det kommer att göra affärens \"Förväntad Avslutning Datum\" och \"Förväntad Affärsvärde\" erfordrade för att få korrekta prognos insikter"

#. Label of the json (JSON) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "JSON"
msgstr "JSON"

#. Label of the job_title (Data) field in DocType 'CRM Deal'
#. Label of the job_title (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Job Title"
msgstr "Jobb Benämning"

#: frontend/src/components/AssignToBody.vue:11
#: frontend/src/components/Filter.vue:74 frontend/src/components/Filter.vue:107
#: frontend/src/components/Modals/AssignmentModal.vue:13
#: frontend/src/components/Modals/TaskModal.vue:63
#: frontend/src/components/Telephony/TaskPanel.vue:47
msgid "John Doe"
msgstr "John Doe"

#. Label of the kanban_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:384
#: frontend/src/components/ViewControls.vue:600 frontend/src/utils/view.js:20
msgid "Kanban"
msgstr "Anslag Tavla"

#. Label of the kanban_columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Columns"
msgstr "Anslagstavla Kolumner"

#. Label of the kanban_fields (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Fields"
msgstr "Anslagstavla Fält"

#: frontend/src/components/Kanban/KanbanSettings.vue:3
#: frontend/src/components/Kanban/KanbanSettings.vue:8
msgid "Kanban Settings"
msgstr "Anslagstavla Inställningar"

#. Label of the key (Data) field in DocType 'CRM Invitation'
#. Label of the key (Data) field in DocType 'Facebook Lead Form Question'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Key"
msgstr "Nyckel"

#. Label of the label (Data) field in DocType 'CRM Dropdown Item'
#. Label of the label (Data) field in DocType 'CRM View Settings'
#. Label of the label (Data) field in DocType 'Facebook Lead Form Question'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: frontend/src/components/ColumnSettings.vue:100
msgid "Label"
msgstr "Etikett"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:108
msgid "Last"
msgstr "Senaste"

#: frontend/src/components/Filter.vue:615
msgid "Last 6 Months"
msgstr "Senaste 6 Månader"

#: frontend/src/components/Filter.vue:607
msgid "Last Month"
msgstr "Förra Månad"

#. Label of the last_name (Data) field in DocType 'CRM Deal'
#. Label of the last_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Last Name"
msgstr "Efternamn"

#: frontend/src/components/Filter.vue:611
msgid "Last Quarter"
msgstr "Förra Kvartal"

#. Label of the last_status_change_log (Link) field in DocType 'CRM Status
#. Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Last Status Change Log"
msgstr "Senaste Statusändring Logg"

#. Label of the last_synced_at (Datetime) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:102
msgid "Last Synced At"
msgstr "Senast Synkroniserad"

#: frontend/src/components/Filter.vue:603
msgid "Last Week"
msgstr "Förra Vecka"

#: frontend/src/components/Filter.vue:619
msgid "Last Year"
msgstr "Förra Året"

#: frontend/src/pages/Contact.vue:533 frontend/src/pages/MobileContact.vue:533
#: frontend/src/pages/MobileOrganization.vue:477
#: frontend/src/pages/MobileOrganization.vue:505
#: frontend/src/pages/Organization.vue:507
#: frontend/src/pages/Organization.vue:535
msgid "Last modified"
msgstr "Senast ändrad"

#: frontend/src/components/Settings/ProfileSettings.vue:83
msgid "Last name"
msgstr "Efternamn"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:101
msgid "Last user assigned by this rule"
msgstr "Senaste användare tilldelad av denna regel"

#. Label of the layout (Code) field in DocType 'CRM Dashboard'
#. Label of the layout (Code) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Layout"
msgstr "Upplägg"

#. Label of the lead (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:263
#: frontend/src/components/Calendar/CalendarEventPanel.vue:288
#: frontend/src/components/Layouts/AppSidebar.vue:550
#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:19
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:55
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:77
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:55
#: frontend/src/components/Telephony/ExotelCallUI.vue:200
#: frontend/src/pages/Tasks.vue:133
msgid "Lead"
msgstr "Potentiell Kund"

#. Label of the lead_data (Code) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Lead Data"
msgstr ""

#. Label of the lead_details_tab (Tab Break) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Details"
msgstr "Potentiell Kund Detaljer"

#. Label of the lead_name (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Name"
msgstr "Potentiell Kund Namn"

#. Label of the lead_owner (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Lead Owner"
msgstr "Potentiell Kund Ansvarig"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:120
msgid "Lead Owner cannot be same as the Lead Email Address"
msgstr "Potentiell Kund Ansvarig kan inte vara samma som Potentiell Kund E-post Adress"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Sources"
msgstr "Potentiell Kund Källor"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Statuses"
msgstr "Potentiell Kund Status"

#. Name of a DocType
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:240
msgid "Lead Sync Source created successfully"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:183
msgid "Lead Sync Source deleted successfully"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:209
msgid "Lead Syncing"
msgstr ""

#: crm/api/dashboard.py:974
msgid "Lead generation channel analysis"
msgstr "Potentiell Kund Generering Kanal Analys"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:7
msgid "Lead sources"
msgstr ""

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.js:8
msgid "Lead sync initiated."
msgstr ""

#: crm/api/dashboard.py:756
msgid "Lead to deal conversion pipeline"
msgstr "Potentiell Kund till Affär Konvertering Pipeline"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/InvalidPage.vue:9 frontend/src/pages/Lead.vue:340
#: frontend/src/pages/MobileLead.vue:291
msgid "Leads"
msgstr "Potentiella Kunder"

#: crm/api/dashboard.py:973
#: frontend/src/components/Dashboard/AddChartModal.vue:106
msgid "Leads by source"
msgstr "Potentiell Kund per källa"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:158
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:238
msgid "Learn about conditions"
msgstr "Lär dig om villkoren"

#. Label of the lft (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Left"
msgstr "Vänster"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:43
msgid "Library"
msgstr "Bibliotek"

#: frontend/src/components/Filter.vue:269
#: frontend/src/components/Filter.vue:280
#: frontend/src/components/Filter.vue:290
#: frontend/src/components/Filter.vue:318
#: frontend/src/components/Filter.vue:332
msgid "Like"
msgstr "Gillar"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:252
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:47
msgid "Link"
msgstr "Länk"

#. Label of the links (Table) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Links"
msgstr "Länkar"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#. Label of the list_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:374
#: frontend/src/components/ViewControls.vue:589 frontend/src/utils/view.js:12
msgid "List"
msgstr "Lista"

#: frontend/src/components/Activities/CallArea.vue:75
msgid "Listen"
msgstr "Lyssna"

#. Label of the load_default_columns (Check) field in DocType 'CRM View
#. Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Load Default Columns"
msgstr "Ladda Standard Kolumner"

#: frontend/src/components/Kanban/KanbanView.vue:140
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:142
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:120
#: frontend/src/components/Settings/Users.vue:155
msgid "Load More"
msgstr "Ladda Mer"

#: frontend/src/components/Activities/Activities.vue:22
#: frontend/src/components/Activities/DataFields.vue:35
#: frontend/src/pages/Deal.vue:176 frontend/src/pages/MobileDeal.vue:117
msgid "Loading..."
msgstr "Laddar..."

#. Label of the log_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the log_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Log"
msgstr "Logg"

#: frontend/src/components/Activities/Activities.vue:814
#: frontend/src/components/Activities/ActivityHeader.vue:133
#: frontend/src/components/Activities/ActivityHeader.vue:176
msgid "Log a Call"
msgstr "Logga Samtal"

#: frontend/src/composables/frappecloud.js:23
msgid "Login to Frappe Cloud?"
msgstr "Logga in på Frappe Cloud?"

#. Label of the brand_logo (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Logo"
msgstr "Logotyp"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Lost"
msgstr "Förlorad"

#. Label of the lost_notes (Text) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lost Notes"
msgstr "Förlorad Affär Anteckningar"

#. Label of the lost_reason (Link) field in DocType 'CRM Deal'
#. Label of the lost_reason (Data) field in DocType 'CRM Lost Reason'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "Lost Reason"
msgstr "Förlorad Anledning"

#: crm/api/dashboard.py:919
#: frontend/src/components/Dashboard/AddChartModal.vue:98
msgid "Lost deal reasons"
msgstr "Förlorad Affär anledningar"

#: frontend/src/components/Modals/LostReasonModal.vue:27
msgid "Lost notes"
msgstr "Förlorad Affär Anteckningar"

#: frontend/src/components/Modals/LostReasonModal.vue:83
msgid "Lost notes are required when lost reason is \"Other\""
msgstr "Förlorad Affär Anteckningar erfordras när förlust orsaken är \"Annan\""

#: frontend/src/components/Modals/LostReasonModal.vue:4
#: frontend/src/components/Modals/LostReasonModal.vue:14
msgid "Lost reason"
msgstr "Förlorad Affär anledning"

#: frontend/src/components/Modals/LostReasonModal.vue:79
msgid "Lost reason is required"
msgstr "Förlorad Affär anledning erfordras"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Low"
msgstr "Låg"

#: frontend/src/pages/Contact.vue:100 frontend/src/pages/MobileContact.vue:73
msgid "Make Call"
msgstr "Ring Samtal"

#: frontend/src/components/ViewControls.vue:1101
msgid "Make Private"
msgstr "Gör Privat"

#: frontend/src/components/ViewControls.vue:1101
msgid "Make Public"
msgstr "Gör Offentligt"

#: frontend/src/components/Activities/Activities.vue:818
#: frontend/src/components/Activities/ActivityHeader.vue:138
#: frontend/src/components/Activities/ActivityHeader.vue:181
#: frontend/src/pages/Deals.vue:505 frontend/src/pages/Leads.vue:532
msgid "Make a Call"
msgstr "Ring Samtal"

#: frontend/src/pages/Deal.vue:81 frontend/src/pages/Lead.vue:123
msgid "Make a call"
msgstr "Ring Samtal"

#: frontend/src/components/Activities/AttachmentArea.vue:98
msgid "Make attachment {0}"
msgstr "Gör bilaga {0}"

#: frontend/src/components/Telephony/CallUI.vue:7
msgid "Make call"
msgstr "Ring Samtal"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make private"
msgstr "Gör Privat"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make public"
msgstr "Gör Offentligt"

#: frontend/src/components/Activities/AttachmentArea.vue:107
msgid "Make {0}"
msgstr "Skapa {0}"

#: frontend/src/components/Telephony/CallUI.vue:34
msgid "Make {0} as default calling medium"
msgstr "Gör {0} till ditt standard medium för samtal"

#: frontend/src/components/Settings/ForecastingSettings.vue:24
msgid "Makes \"Expected Closure Date\" and \"Expected Deal Value\" mandatory for deal value forecasting"
msgstr "Erfordrar \"Förväntad Avslutning Datum\" och \"Förväntad Affärsvärde\" för prognos av affärsvärde"

#: frontend/src/components/Settings/Users.vue:11
msgid "Manage CRM users by adding or inviting them, and assign roles to control their access and permissions"
msgstr "Hantera Säljstöd användare genom att lägga till eller bjuda in dem och tilldela roller för att styra deras åtkomst och behörigheter"

#: frontend/src/components/Settings/EmailAccountList.vue:11
msgid "Manage your email accounts to send and receive emails directly from CRM. You can add multiple accounts and set one as default for incoming and outgoing emails."
msgstr "Hantera e-post konton för att skicka och ta emot e-post direkt från Säljstöd. Du kan lägga till flera konton och ange standard konto för inkommande och utgående e-post."

#: frontend/src/components/Modals/AddExistingUserModal.vue:93
#: frontend/src/components/Settings/InviteUserPage.vue:157
#: frontend/src/components/Settings/InviteUserPage.vue:164
#: frontend/src/components/Settings/Users.vue:87
#: frontend/src/components/Settings/Users.vue:193
#: frontend/src/components/Settings/Users.vue:253
#: frontend/src/components/Settings/Users.vue:256
msgid "Manager"
msgstr "Ansvarig"

#: frontend/src/data/document.js:57
msgid "Mandatory field error: {0}"
msgstr "Erfordrad fält fel: {0}"

#: frontend/src/data/document.js:187 frontend/src/data/document.js:190
msgid "Mandatory fields required: {0}"
msgstr "Erfordrade Fält saknas {0}"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Manual"
msgstr "Manuell"

#. Label of the mapped_to_crm_field (Autocomplete) field in DocType 'Facebook
#. Lead Form Question'
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Mapped to CRM Field"
msgstr ""

#: frontend/src/components/Notifications.vue:20
#: frontend/src/pages/MobileNotification.vue:12
#: frontend/src/pages/MobileNotification.vue:13
msgid "Mark all as read"
msgstr "Markera alla som lästa"

#: frontend/src/components/Layouts/AppSidebar.vue:547
msgid "Masters"
msgstr "Inställningar"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:209
#: frontend/src/components/Modals/EventModal.vue:86
msgid "May 1, 2025"
msgstr "1 maj 2025"

#. Label of the medium (Data) field in DocType 'CRM Call Log'
#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Medium"
msgstr "Medium"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Mention"
msgstr "Hänvisa"

#. Label of the message (HTML Editor) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Message"
msgstr "Meddelande"

#. Label of the middle_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Middle Name"
msgstr "Mellannamn"

#: frontend/src/components/Telephony/ExotelCallUI.vue:127
msgid "Minimize"
msgstr "Minimera"

#. Label of the mobile_no (Data) field in DocType 'CRM Contacts'
#. Label of the mobile_no (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Mobile No"
msgstr "Mobil Nummer"

#: frontend/src/components/Modals/DealModal.vue:208
#: frontend/src/components/Modals/LeadModal.vue:149
msgid "Mobile No should be a number"
msgstr "Mobilnummer ska vara ett nummer"

#. Label of the mobile_no (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Mobile No."
msgstr "Mobil Nummer."

#: frontend/src/components/Telephony/CallUI.vue:22
msgid "Mobile Number"
msgstr "Mobilnummer"

#: crm/integrations/exotel/handler.py:93
msgid "Mobile Number Missing"
msgstr "Mobilnummer Saknas"

#: frontend/src/components/Layouts/AppSidebar.vue:611
msgid "Mobile app installation"
msgstr "Mobil App Installation"

#: frontend/src/pages/Contact.vue:523 frontend/src/pages/MobileContact.vue:523
#: frontend/src/pages/MobileOrganization.vue:467
#: frontend/src/pages/Organization.vue:497
msgid "Mobile no"
msgstr "Mobilnummer"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Monday"
msgstr "Måndag"

#: crm/api/dashboard.py:691
msgid "Month"
msgstr "Månad"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Monthly"
msgstr "Månadsvis"

#: frontend/src/components/ViewControls.vue:221
msgid "More Options"
msgstr "Fler Alternativ"

#: frontend/src/components/FieldLayoutEditor.vue:448
msgid "Move to next section"
msgstr "Gå till nästa sektion"

#: frontend/src/components/FieldLayoutEditor.vue:401
msgid "Move to next tab"
msgstr "Gå till nästa flik"

#: frontend/src/components/FieldLayoutEditor.vue:458
msgid "Move to previous section"
msgstr "Gå till föregående sektion"

#: frontend/src/components/FieldLayoutEditor.vue:387
msgid "Move to previous tab"
msgstr "Gå till föregående flik"

#: frontend/src/components/Modals/ViewModal.vue:29
msgid "My Open Deals"
msgstr "Mina Öppna Affärer"

#. Label of the title (Data) field in DocType 'CRM Dashboard'
#. Label of the name1 (Data) field in DocType 'CRM Dropdown Item'
#. Label of the brand_name (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:51
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:52
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:39
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:39
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:59
#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:22
#: frontend/src/components/ViewControls.vue:779
#: frontend/src/pages/MobileOrganization.vue:485
#: frontend/src/pages/Organization.vue:515
msgid "Name"
msgstr "Namn"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:417
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:151
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:154
msgid "Name is required"
msgstr "Namn erfordras"

#. Label of the naming_series (Select) field in DocType 'CRM Deal'
#. Label of the naming_series (Select) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Naming Series"
msgstr "Namngivning Serie"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:99
msgid "Nested conditions"
msgstr "Nästlade villkor"

#. Label of the net_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Net Amount"
msgstr "Netto Belopp"

#. Label of the net_total (Currency) field in DocType 'CRM Deal'
#. Label of the net_total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Net Total"
msgstr "Netto Totalt"

#: frontend/src/components/Activities/ActivityHeader.vue:76
#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:19
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:19
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:20
#: frontend/src/components/Settings/Users.vue:30
msgid "New"
msgstr "Ny"

#: frontend/src/components/Modals/AddressModal.vue:93
msgid "New Address"
msgstr "Ny Adress"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:12
msgid "New Assignment Rule"
msgstr "Ny Tilldelning Regel"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:44
msgid "New Assignment Rule Name"
msgstr "Ny Tilldelning Regel Namn"

#: frontend/src/components/Modals/CallLogModal.vue:97
msgid "New Call Log"
msgstr "Ny Samtalslogg"

#: frontend/src/components/Activities/Activities.vue:398
#: frontend/src/components/Activities/ActivityHeader.vue:19
#: frontend/src/components/Activities/ActivityHeader.vue:123
msgid "New Comment"
msgstr "Ny Kommentar"

#: frontend/src/components/Modals/ContactModal.vue:8
msgid "New Contact"
msgstr "Ny Kontakt"

#: frontend/src/components/Activities/Activities.vue:393
#: frontend/src/components/Activities/ActivityHeader.vue:12
#: frontend/src/components/Activities/ActivityHeader.vue:118
msgid "New Email"
msgstr "Ny E-post"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:9
msgid "New Lead Sync Source"
msgstr ""

#: frontend/src/components/Activities/ActivityHeader.vue:66
msgid "New Message"
msgstr "Nytt Meddelande"

#: frontend/src/components/Activities/ActivityHeader.vue:41
#: frontend/src/components/Activities/ActivityHeader.vue:144
#: frontend/src/pages/Deals.vue:511 frontend/src/pages/Leads.vue:538
msgid "New Note"
msgstr "Ny Anteckning"

#: frontend/src/components/Modals/OrganizationModal.vue:8
msgid "New Organization"
msgstr "Ny Organisation"

#: frontend/src/components/Modals/ChangePasswordModal.vue:6
msgid "New Password"
msgstr "Ny Lösenord"

#: frontend/src/components/FieldLayoutEditor.vue:201
#: frontend/src/components/SidePanelLayoutEditor.vue:131
msgid "New Section"
msgstr "Ny Sektion"

#: frontend/src/components/FieldLayoutEditor.vue:293
#: frontend/src/components/FieldLayoutEditor.vue:298
msgid "New Tab"
msgstr "Ny Flik"

#: frontend/src/components/Activities/ActivityHeader.vue:48
#: frontend/src/components/Activities/ActivityHeader.vue:149
#: frontend/src/pages/Deals.vue:516 frontend/src/pages/Leads.vue:543
msgid "New Task"
msgstr "Ny Uppgift"

#: frontend/src/components/Activities/ActivityHeader.vue:159
msgid "New WhatsApp Message"
msgstr "Nytt WhatsApp Meddelande"

#: frontend/src/components/Modals/ConvertToDealModal.vue:67
#: frontend/src/pages/MobileLead.vue:162
msgid "New contact will be created based on the person's details"
msgstr "Ny kontakt kommer att skapas baserat på personens uppgifter"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:431
msgid "New event"
msgstr "Ny händelse"

#: frontend/src/components/Modals/ConvertToDealModal.vue:42
#: frontend/src/pages/MobileLead.vue:136
msgid "New organization will be created based on the data in details section"
msgstr "Ny organisation kommer att skapas baserat på information i detalj sektionen"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "New template"
msgstr "Ny mall"

#: frontend/src/components/Modals/CreateDocumentModal.vue:88
msgid "New {0}"
msgstr "Ny {0}"

#: frontend/src/components/Filter.vue:663
msgid "Next 6 Months"
msgstr "Nästa 6 Månader"

#: frontend/src/components/Filter.vue:655
msgid "Next Month"
msgstr "Nästa Månad"

#: frontend/src/components/Filter.vue:659
msgid "Next Quarter"
msgstr "Nästa Kvartal"

#. Label of the next_step (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Next Step"
msgstr "Nästa Steg"

#: frontend/src/components/Filter.vue:651
msgid "Next Week"
msgstr "Nästa Vecka"

#: frontend/src/components/Filter.vue:667
msgid "Next Year"
msgstr "Nästa År"

#: frontend/src/components/Controls/Grid.vue:27
msgid "No"
msgstr "Nej"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "No Answer"
msgstr "Ingen Svar"

#: frontend/src/components/Controls/Grid.vue:322
msgid "No Data"
msgstr "Ingen Data"

#: frontend/src/components/Activities/EventArea.vue:78
msgid "No Events Scheduled"
msgstr "Inga Schemalagda Händelser"

#: frontend/src/components/Kanban/KanbanView.vue:103
#: frontend/src/pages/Deals.vue:105 frontend/src/pages/Leads.vue:121
#: frontend/src/pages/Tasks.vue:71
msgid "No Title"
msgstr "Ingen Benämning"

#: frontend/src/components/Settings/EmailEdit.vue:150
msgid "No changes made"
msgstr "Inga ändringar gjorda"

#: frontend/src/components/Modals/SidePanelModal.vue:51
#: frontend/src/pages/Deal.vue:271 frontend/src/pages/MobileDeal.vue:205
msgid "No contacts added"
msgstr "Inga kontakter tillagda"

#: frontend/src/pages/Deal.vue:257
msgid "No details added"
msgstr ""

#: frontend/src/pages/Deal.vue:90 frontend/src/pages/Lead.vue:137
msgid "No email set"
msgstr "Ingen e-post adress angiven"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:48
msgid "No email templates found"
msgstr "Inga e-postmallar hittades"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:14
msgid "No items in the list"
msgstr "Inga ärenden i listan"

#: frontend/src/components/FieldLayoutEditor.vue:92
msgid "No label"
msgstr "Ingen etikett"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:49
msgid "No lead sources found"
msgstr ""

#: frontend/src/pages/Deal.vue:707
msgid "No mobile number set"
msgstr "Inget mobilnummer angivet"

#: frontend/src/components/Notifications.vue:77
#: frontend/src/pages/MobileNotification.vue:61
msgid "No new notifications"
msgstr "Inga nya aviseringar"

#: frontend/src/pages/Lead.vue:129
msgid "No phone number set"
msgstr "Inget telefonnummer angivet"

#: frontend/src/pages/Deal.vue:702
msgid "No primary contact set"
msgstr "Ingen primär kontakt angiven"

#: frontend/src/components/Calendar/Attendee.vue:232
#: frontend/src/components/Controls/EmailMultiSelect.vue:133
#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:70
msgid "No results found"
msgstr "Inga resultat hittades"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:66
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:42
msgid "No templates found"
msgstr "Inga mallar hittades"

#: frontend/src/components/Modals/AddExistingUserModal.vue:39
#: frontend/src/components/Settings/Users.vue:57
msgid "No users found"
msgstr "Inga användare hittades"

#: frontend/src/pages/MobileOrganization.vue:281
#: frontend/src/pages/Organization.vue:318
msgid "No website found"
msgstr "Ingen webbplats hittades"

#: frontend/src/pages/Deal.vue:100 frontend/src/pages/Lead.vue:146
msgid "No website set"
msgstr "Ingen webbplats angiven"

#: frontend/src/components/PrimaryDropdown.vue:33
msgid "No {0} Available"
msgstr "Ingen {0} Tillgänglig"

#: frontend/src/pages/CallLogs.vue:59 frontend/src/pages/Contact.vue:162
#: frontend/src/pages/Contacts.vue:58 frontend/src/pages/Deals.vue:234
#: frontend/src/pages/Leads.vue:260 frontend/src/pages/MobileContact.vue:147
#: frontend/src/pages/MobileOrganization.vue:139
#: frontend/src/pages/Notes.vue:95 frontend/src/pages/Organization.vue:158
#: frontend/src/pages/Organizations.vue:58 frontend/src/pages/Tasks.vue:184
msgid "No {0} Found"
msgstr "{0}"

#. Label of the no_of_employees (Select) field in DocType 'CRM Deal'
#. Label of the no_of_employees (Select) field in DocType 'CRM Lead'
#. Label of the no_of_employees (Select) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "No. of Employees"
msgstr "Personal Antal"

#: frontend/src/components/Activities/AudioPlayer.vue:148
msgid "Normal"
msgstr "Normal"

#: crm/utils/__init__.py:263
msgid "Not Allowed"
msgstr "Ej Tillåtet"

#: frontend/src/components/Filter.vue:268
#: frontend/src/components/Filter.vue:289
#: frontend/src/components/Filter.vue:306
#: frontend/src/components/Filter.vue:317
#: frontend/src/components/Filter.vue:344
msgid "Not Equals"
msgstr "Inte Lika"

#: frontend/src/components/Filter.vue:272
#: frontend/src/components/Filter.vue:293
#: frontend/src/components/Filter.vue:308
#: frontend/src/components/Filter.vue:321
#: frontend/src/components/Filter.vue:335
msgid "Not In"
msgstr "Ej I"

#: frontend/src/components/Filter.vue:270
#: frontend/src/components/Filter.vue:281
#: frontend/src/components/Filter.vue:291
#: frontend/src/components/Filter.vue:319
#: frontend/src/components/Filter.vue:333
msgid "Not Like"
msgstr "Inte Som"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:10
#: frontend/src/components/Controls/GridRowFieldsModal.vue:10
#: frontend/src/components/Modals/DataFieldsModal.vue:10
#: frontend/src/components/Modals/QuickEntryModal.vue:10
#: frontend/src/components/Modals/SidePanelModal.vue:10
#: frontend/src/components/Settings/SettingsPage.vue:11
#: frontend/src/components/Settings/TelephonySettings.vue:11
msgid "Not Saved"
msgstr "Ej Sparad"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:133
msgid "Not Synced"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:272
msgid "Not allowed to add contact to Deal"
msgstr "Inte tillåtet att lägga till kontakt i Affär"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:455
msgid "Not allowed to convert Lead to Deal"
msgstr "Inte tillåtet att konvertera Potentiell Kund till Affär"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:283
msgid "Not allowed to remove contact from Deal"
msgstr "Inte tillåtet att ta bort kontakt från Affär"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:294
msgid "Not allowed to set primary contact for Deal"
msgstr "Inte tillåtet att ange primär kontakt för Affär"

#. Label of the note (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: frontend/src/components/Layouts/AppSidebar.vue:554
msgid "Note"
msgstr "Anteckning"

#: frontend/src/pages/Deal.vue:572 frontend/src/pages/Lead.vue:419
#: frontend/src/pages/MobileDeal.vue:461 frontend/src/pages/MobileLead.vue:367
msgid "Notes"
msgstr "Anteckningar"

#: frontend/src/pages/Notes.vue:23
msgid "Notes View"
msgstr "Anteckning Vy"

#: frontend/src/components/Activities/EmailArea.vue:15
#: frontend/src/components/Layouts/AppSidebar.vue:583
msgid "Notification"
msgstr "Aviseringar"

#. Label of the notification_text (Text) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Text"
msgstr "Avisering Text"

#. Label of the notification_type_doc (Dynamic Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doc"
msgstr "Avisering DocType"

#. Label of the notification_type_doctype (Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doctype"
msgstr "Avisering Typ DocType"

#: frontend/src/components/Layouts/AppSidebar.vue:13
#: frontend/src/components/Mobile/MobileSidebar.vue:23
#: frontend/src/components/Notifications.vue:17
#: frontend/src/pages/MobileNotification.vue:6
msgid "Notifications"
msgstr "Aviseringar"

#. Label of the number (Data) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Number"
msgstr "Nummer"

#: frontend/src/components/Dashboard/AddChartModal.vue:19
#: frontend/src/components/Dashboard/AddChartModal.vue:69
msgid "Number chart"
msgstr "Nummer Diagram"

#: crm/api/dashboard.py:1072 crm/api/dashboard.py:1132
msgid "Number of deals"
msgstr "Antal affärer"

#: crm/api/dashboard.py:1125
msgid "Number of deals and total value per salesperson"
msgstr "Antal affärer och totalt värde per säljare"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:167
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:251
msgid "Old Condition"
msgstr "Gamla Villkor"

#. Label of the old_parent (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Old Parent"
msgstr "Tidigare Överordnad"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "On Hold"
msgstr "Spärrad"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Ongoing"
msgstr "Pågående"

#: crm/api/dashboard.py:190
#: frontend/src/components/Dashboard/AddChartModal.vue:77
msgid "Ongoing deals"
msgstr "Pågående Affärer"

#: frontend/src/utils/index.js:474
msgid "Only image files are allowed"
msgstr "Endast bildfiler är tillåtna"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:60
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.py:23
msgid "Only one {0} can be set as primary."
msgstr "Endast en {0} kan anges som primär."

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Open"
msgstr "Öppen"

#: frontend/src/components/Modals/NoteModal.vue:13
#: frontend/src/components/Modals/TaskModal.vue:13
msgid "Open Deal"
msgstr "Öppna Affär"

#: frontend/src/components/Modals/NoteModal.vue:14
#: frontend/src/components/Modals/TaskModal.vue:14
msgid "Open Lead"
msgstr "Öppna Potentiell Kund"

#: crm/fcrm/doctype/crm_deal/crm_deal.js:6
#: crm/fcrm/doctype/crm_lead/crm_lead.js:6
msgid "Open in Portal"
msgstr "Öppna i Portalen"

#. Label of the open_in_new_window (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Open in new window"
msgstr "Öppna i nytt fönster"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:88
msgid "Open nested conditions"
msgstr "Öppna nästlade villkor"

#: frontend/src/pages/Organization.vue:96
msgid "Open website"
msgstr "Öppna webbplats"

#: frontend/src/components/Kanban/KanbanView.vue:218
#: frontend/src/components/Modals/CallLogDetailModal.vue:15
#: frontend/src/components/ViewControls.vue:199
msgid "Options"
msgstr "Alternativ "

#: frontend/src/pages/Welcome.vue:40
msgid "Or create leads manually"
msgstr "Eller skapa potentiella kunder manuellt"

#. Label of the order_by_tab (Tab Break) field in DocType 'CRM View Settings'
#. Label of the order_by (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Order By"
msgstr "Sortera Efter"

#. Label of the organization (Link) field in DocType 'CRM Deal'
#. Label of the organization_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the organization (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Layouts/AppSidebar.vue:553
#: frontend/src/components/Modals/ConvertToDealModal.vue:25
#: frontend/src/pages/Contact.vue:502 frontend/src/pages/MobileContact.vue:502
#: frontend/src/pages/MobileLead.vue:118
#: frontend/src/pages/MobileOrganization.vue:446
#: frontend/src/pages/MobileOrganization.vue:500
#: frontend/src/pages/Organization.vue:476
#: frontend/src/pages/Organization.vue:530
msgid "Organization"
msgstr "Organisation"

#. Label of the organization_details_section (Section Break) field in DocType
#. 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Organization Details"
msgstr "Organisation Detaljer"

#. Label of the organization_logo (Attach Image) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Logo"
msgstr "Organisation Logotyp"

#. Label of the organization_name (Data) field in DocType 'CRM Deal'
#. Label of the organization_name (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Name"
msgstr "Organisation Namn"

#: frontend/src/pages/Deal.vue:62
msgid "Organization logo"
msgstr "Organisation Logotyp"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/MobileOrganization.vue:205
#: frontend/src/pages/Organization.vue:243
msgid "Organizations"
msgstr "Organisationer"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:107
msgid "Organizer"
msgstr "Organisatör"

#: frontend/src/components/Layouts/AppSidebar.vue:575
msgid "Other features"
msgstr "Övriga funktioner"

#. Label of the organization_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Others"
msgstr "Övriga"

#: frontend/src/components/Activities/CallArea.vue:37
msgid "Outbound Call"
msgstr "Utgående Samtal"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Outgoing"
msgstr "Utgående"

#. Label of the log_owner (Link) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Owner"
msgstr "Ansvarig"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:102
msgid "Owner: {0}"
msgstr "Ägare: {0}"

#. Label of the page (Link) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Page"
msgstr "Sida"

#. Label of the page_name (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Page Name"
msgstr "Sida Namn"

#. Label of the parent_crm_territory (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Parent CRM Territory"
msgstr "Överordnat Distrikt"

#: frontend/src/components/Settings/emailConfig.js:64
msgid "Password"
msgstr "Lösenord"

#: crm/api/demo.py:21 crm/api/demo.py:29
msgid "Password cannot be reset by Demo User {}"
msgstr "Lösenord kan inte återställas av Demo Användare {}"

#: frontend/src/components/Settings/emailConfig.js:175
msgid "Password is required"
msgstr "Lösenord erfordras"

#: frontend/src/components/Modals/ChangePasswordModal.vue:88
msgid "Password updated successfully"
msgstr "Lösenord uppdaterad"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:13
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:38
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:38
msgid "Payment Reminder"
msgstr "Betalningspåminnelse"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:69
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:69
msgid "Payment Reminder from Frappé - (#{{ name }})"
msgstr "Betalningspåminnelse från Frappé - (#{{ name }})"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Pending"
msgstr "Pågående"

#: frontend/src/components/Settings/InviteUserPage.vue:61
msgid "Pending Invites"
msgstr "Väntande Inbjudningar"

#: frontend/src/pages/Dashboard.vue:66
msgid "Period"
msgstr "Period"

#. Label of the person_section (Section Break) field in DocType 'CRM Deal'
#. Label of the person_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Person"
msgstr "Person"

#: frontend/src/components/Settings/Settings.vue:90
msgid "Personal Settings"
msgstr "Personliga Inställningar"

#. Label of the phone (Data) field in DocType 'CRM Contacts'
#. Label of the phone (Data) field in DocType 'CRM Lead'
#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/pages/MobileOrganization.vue:495
#: frontend/src/pages/Organization.vue:525
msgid "Phone"
msgstr "Telefon"

#. Label of the phone_nos (Table) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Phone Numbers"
msgstr "Telefonnummer"

#: frontend/src/components/ViewControls.vue:1093
msgid "Pin View"
msgstr "Fäst Vy"

#. Label of the pinned (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Pinned"
msgstr "Fäst"

#: frontend/src/components/ViewControls.vue:677
msgid "Pinned Views"
msgstr "Fästa Vyer"

#: frontend/src/components/Layouts/AppSidebar.vue:571
msgid "Pinned view"
msgstr "Fäst vy"

#: frontend/src/components/Activities/AudioPlayer.vue:176
msgid "Playback speed"
msgstr "Uppspelningshastighet"

#: frontend/src/components/Settings/EmailAccountList.vue:49
msgid "Please add an email account to continue."
msgstr "Lägg till e-post konto för att fortsätta."

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:157
msgid "Please check your access token"
msgstr ""

#: crm/integrations/twilio/twilio_handler.py:119
msgid "Please enable twilio settings before making a call."
msgstr "Aktivera Twilio Inställningar innan du ringer ett samtal."

#: frontend/src/components/FilesUploader/FilesUploader.vue:165
msgid "Please enter a valid URL"
msgstr "Ange giltig URL"

#: frontend/src/components/Settings/CurrencySettings.vue:150
msgid "Please enter the Exchangerate Host access key."
msgstr "Ange åtkomst nyckel för Valuta Växling Leverantör."

#: frontend/src/components/Modals/LostReasonModal.vue:9
msgid "Please provide a reason for marking this deal as lost"
msgstr "Ange anledning till att du anger denna affär som förlorad"

#: frontend/src/components/Settings/CurrencySettings.vue:143
msgid "Please select a currency before saving."
msgstr "Välj valuta innan du sparar."

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.py:65
msgid "Please select a lead gen form before syncing!"
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:136
#: frontend/src/pages/MobileLead.vue:431
msgid "Please select an existing contact"
msgstr "Välj befintlig kontakt"

#: frontend/src/components/Modals/ConvertToDealModal.vue:141
#: frontend/src/pages/MobileLead.vue:436
msgid "Please select an existing organization"
msgstr "Välj befintlig organisation"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:126
msgid "Please set Email Address"
msgstr "Ange E-postadress"

#: crm/integrations/exotel/handler.py:73
msgid "Please setup Exotel intergration"
msgstr "Konfigurera Exotel Integration"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:181
msgid "Please specify a reason for losing the deal."
msgstr "Ange anledning till att affären förlorades."

#: crm/fcrm/doctype/crm_deal/crm_deal.py:183
msgid "Please specify the reason for losing the deal."
msgstr "Ange anledning till att affären förlorades."

#. Label of the position (Int) field in DocType 'CRM Deal Status'
#. Label of the position (Int) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Position"
msgstr "Position"

#: frontend/src/pages/Deal.vue:205 frontend/src/pages/MobileDeal.vue:149
msgid "Primary"
msgstr "Primär"

#. Label of the email (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Email"
msgstr "Primär E-post"

#. Label of the mobile_no (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Mobile No"
msgstr "Primär Mobil Nummer"

#. Label of the phone (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Phone"
msgstr "Primär Telefon"

#: frontend/src/pages/Deal.vue:679 frontend/src/pages/MobileDeal.vue:566
msgid "Primary contact set"
msgstr "Primär kontakt angiven"

#. Label of the priorities (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Priorities"
msgstr "Prioriteringar"

#. Label of the priority (Link) field in DocType 'CRM Service Level Priority'
#. Label of the priority (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:64
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:20
msgid "Priority"
msgstr "Prioritet"

#. Label of the private (Check) field in DocType 'CRM Dashboard'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:89
msgid "Private"
msgstr "Privat"

#. Label of the probability (Percent) field in DocType 'CRM Deal'
#. Label of the probability (Percent) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Probability"
msgstr "Sannolikhet"

#. Label of the product_code (Link) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product"
msgstr "Artikel"

#. Label of the product_code (Data) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Product Code"
msgstr "Artikelkod"

#. Label of the product_name (Data) field in DocType 'CRM Product'
#. Label of the product_name (Data) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product Name"
msgstr "Artikelnamn"

#. Label of the products_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the products (Table) field in DocType 'CRM Deal'
#. Label of the products_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the products (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Products"
msgstr "Artiklar"

#: frontend/src/components/Layouts/AppSidebar.vue:540
#: frontend/src/components/Settings/Settings.vue:94
msgid "Profile"
msgstr "Profil"

#: frontend/src/components/Settings/ProfileSettings.vue:147
msgid "Profile updated successfully"
msgstr "Profil uppdaterad"

#: crm/api/dashboard.py:689
msgid "Projected vs actual revenue based on deal probability"
msgstr "Prognostiserade kontra faktiska intäkter baserat på affärssannolikhet"

#. Label of the public (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Public"
msgstr "Allmän"

#: frontend/src/components/ViewControls.vue:672
msgid "Public Views"
msgstr "Offentliga Vyer"

#: frontend/src/components/Layouts/AppSidebar.vue:570
msgid "Public view"
msgstr "Allmän Vy"

#. Label of the qty (Float) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Quantity"
msgstr "Kvantitet"

#. Label of the questions (Table) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Questions"
msgstr "Frågor"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Queued"
msgstr "I Kö"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Quick Entry"
msgstr "Snabb Post"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Quick Filters"
msgstr "Snabb Filter"

#: frontend/src/components/ViewControls.vue:731
msgid "Quick Filters updated successfully"
msgstr "Snabbfilter uppdaterade"

#: frontend/src/components/Layouts/AppSidebar.vue:594
msgid "Quick entry layout"
msgstr "Snabb Post Utseende"

#. Label of the rate (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Rate"
msgstr "Pris"

#. Label of the read (Check) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Read"
msgstr "Läsa"

#: crm/api/dashboard.py:922
msgid "Reason"
msgstr "Anledning"

#. Label of the record_calls (Check) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Record Calls"
msgstr "Spela in Samtal"

#. Label of the record_call (Check) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Record Outgoing Calls"
msgstr "Spela in Utgående Samtal"

#. Label of the recording_url (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Recording URL"
msgstr "Inspelning URL"

#. Label of the reference_name (Dynamic Link) field in DocType 'CRM
#. Notification'
#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Task'
#. Label of the reference_docname (Dynamic Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Doc"
msgstr "Referens Dokument"

#. Label of the reference_doctype (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Reference Doctype"
msgstr "Referens DocType"

#. Label of the reference_doctype (Link) field in DocType 'CRM Call Log'
#. Label of the reference_doctype (Link) field in DocType 'CRM Task'
#. Label of the reference_doctype (Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Document Type"
msgstr "Referens Dokument Typ"

#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Call
#. Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Reference Name"
msgstr "Referens Namn"

#: frontend/src/components/ViewControls.vue:26
#: frontend/src/components/ViewControls.vue:159
#: frontend/src/pages/Dashboard.vue:10
msgid "Refresh"
msgstr "Uppdatera"

#: frontend/src/components/Telephony/TwilioCallUI.vue:97
msgid "Reject"
msgstr "Avvisa"

#: frontend/src/components/Telephony/TwilioCallUI.vue:163
msgid "Reject call"
msgstr "Avvisa samtal"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:192
#: frontend/src/components/Controls/ImageUploader.vue:25
#: frontend/src/components/Settings/Users.vue:218
#: frontend/src/pages/Deal.vue:628
msgid "Remove"
msgstr "Ta bort"

#: frontend/src/components/FilesUploader/FilesUploader.vue:23
msgid "Remove all"
msgstr "Ta bort alla"

#: frontend/src/components/FieldLayoutEditor.vue:438
msgid "Remove and move fields to previous column"
msgstr "Ta bort och flytta fält till föregående kolumn"

#: frontend/src/components/FieldLayoutEditor.vue:432
msgid "Remove column"
msgstr "Ta Bort Kolumn"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:200
msgid "Remove group"
msgstr "Ta bort grupp"

#: frontend/src/components/Settings/ProfileSettings.vue:32
#: frontend/src/pages/Contact.vue:53 frontend/src/pages/Lead.vue:94
#: frontend/src/pages/MobileContact.vue:43
#: frontend/src/pages/MobileOrganization.vue:43
#: frontend/src/pages/Organization.vue:53
msgid "Remove image"
msgstr "Ta bort bild"

#: frontend/src/components/FieldLayoutEditor.vue:359
msgid "Remove section"
msgstr "Ta bort sektion"

#: frontend/src/components/FieldLayoutEditor.vue:318
msgid "Remove tab"
msgstr "Ta bort flik"

#: frontend/src/components/Activities/EmailArea.vue:34
#: frontend/src/components/CommunicationArea.vue:10
msgid "Reply"
msgstr "Svara"

#: frontend/src/components/Activities/EmailArea.vue:41
msgid "Reply All"
msgstr "Svara Alla"

#: frontend/src/components/Modals/AboutModal.vue:72
msgid "Report an Issue"
msgstr "Rapportera Ärende"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Required Fields"
msgstr "Erfordrade Fält"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:79
#: frontend/src/components/Controls/GridRowFieldsModal.vue:30
#: frontend/src/components/Modals/DataFieldsModal.vue:30
#: frontend/src/components/Modals/QuickEntryModal.vue:30
#: frontend/src/components/Modals/SidePanelModal.vue:30
msgid "Reset"
msgstr "Återställ"

#: frontend/src/components/ColumnSettings.vue:78
msgid "Reset Changes"
msgstr "Återställ ändringar"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:7
msgid "Reset ERPNext Form Script"
msgstr "Återställ Affärssystem Formulär Script"

#: frontend/src/components/ColumnSettings.vue:86
msgid "Reset to Default"
msgstr "Återställ till Standard"

#: frontend/src/pages/Dashboard.vue:28
msgid "Reset to default"
msgstr "Återställ Standard"

#. Label of the response_by (Datetime) field in DocType 'CRM Deal'
#. Label of the response_by (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response By"
msgstr "Svar Efter"

#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Deal'
#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response Details"
msgstr "Svar Information"

#. Label of the section_break_ufaf (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Response and Follow Up"
msgstr "Svar och Uppföljning"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:14
msgid "Restore"
msgstr "Återställ"

#. Label of the restore_defaults (Button) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:13
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Restore Defaults"
msgstr "Återställ Standardvärden"

#: frontend/src/components/FilesUploader/FilesUploader.vue:51
msgid "Retake"
msgstr "Gör om"

#: crm/api/dashboard.py:697
msgid "Revenue"
msgstr "Intäkt"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:81
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:81
msgid "Rich Text"
msgstr "Rich Text "

#. Label of the rgt (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Right"
msgstr "Höger"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Ringing"
msgstr "Ringer"

#: frontend/src/components/Telephony/TwilioCallUI.vue:38
#: frontend/src/components/Telephony/TwilioCallUI.vue:140
msgid "Ringing..."
msgstr "Ringer..."

#. Label of the role (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:46
msgid "Role"
msgstr "Roll"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#. Label of the route (Data) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Route"
msgstr "Sökväg"

#. Label of the route_name (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Route Name"
msgstr "Ruttnamn"

#. Label of the rows (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Rows"
msgstr "Rader"

#. Label of the sla_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the sla (Link) field in DocType 'CRM Deal'
#. Label of the sla_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the sla (Link) field in DocType 'CRM Lead'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "SLA"
msgstr "Service Nivå Avtal"

#. Label of the sla_creation (Datetime) field in DocType 'CRM Deal'
#. Label of the sla_creation (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Creation"
msgstr "Skapande av Service Nivå Avtal"

#. Label of the sla_name (Data) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "SLA Name"
msgstr "Service Nivå Avtal Benämning"

#. Label of the sla_status (Select) field in DocType 'CRM Deal'
#. Label of the sla_status (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Status"
msgstr "Service Nivå Avtal Status"

#: frontend/src/components/EmailEditor.vue:85
msgid "SUBJECT"
msgstr "ÄMNE"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Sales Manager"
msgstr "Försäljning Ansvarig"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:92
#: frontend/src/components/Settings/InviteUserPage.vue:156
#: frontend/src/components/Settings/InviteUserPage.vue:163
#: frontend/src/components/Settings/Users.vue:88
#: frontend/src/components/Settings/Users.vue:194
#: frontend/src/components/Settings/Users.vue:264
#: frontend/src/components/Settings/Users.vue:267
msgid "Sales User"
msgstr "Försäljning Användare"

#: crm/api/dashboard.py:617
#: frontend/src/components/Dashboard/AddChartModal.vue:94
msgid "Sales trend"
msgstr "Försäljningstrend"

#: frontend/src/pages/Dashboard.vue:93
msgid "Sales user"
msgstr "Försäljning Användare"

#: crm/api/dashboard.py:1127
msgid "Salesperson"
msgstr "Säljare"

#. Label of the salutation (Link) field in DocType 'CRM Deal'
#. Label of the salutation (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Salutation"
msgstr "Tilltal"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Saturday"
msgstr "Lördag"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:345
#: frontend/src/components/Controls/GridFieldsEditorModal.vue:84
#: frontend/src/components/Controls/GridRowFieldsModal.vue:26
#: frontend/src/components/Modals/AddressModal.vue:98
#: frontend/src/components/Modals/CallLogModal.vue:101
#: frontend/src/components/Modals/DataFieldsModal.vue:26
#: frontend/src/components/Modals/LostReasonModal.vue:44
#: frontend/src/components/Modals/QuickEntryModal.vue:26
#: frontend/src/components/Modals/SidePanelModal.vue:26
#: frontend/src/components/PrimaryDropdownItem.vue:21
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:36
#: frontend/src/components/Settings/CurrencySettings.vue:173
#: frontend/src/components/Telephony/ExotelCallUI.vue:222
#: frontend/src/components/ViewControls.vue:125
#: frontend/src/pages/Dashboard.vue:36
msgid "Save"
msgstr "Spara"

#: frontend/src/components/Modals/ViewModal.vue:40
#: frontend/src/components/ViewControls.vue:58
#: frontend/src/components/ViewControls.vue:155
msgid "Save Changes"
msgstr "Spara Ändringar"

#: frontend/src/components/ViewControls.vue:667
msgid "Saved Views"
msgstr "Sparade Vyer"

#: frontend/src/components/Layouts/AppSidebar.vue:569
msgid "Saved view"
msgstr "Sparad vy"

#: frontend/src/components/Telephony/TaskPanel.vue:8
msgid "Schedule a task..."
msgstr "Schemalägg uppgift..."

#: frontend/src/components/Activities/EventArea.vue:79
msgid "Schedule an Event"
msgstr "Schemalägg Händelse"

#: frontend/src/components/Activities/ActivityHeader.vue:36
#: frontend/src/components/Activities/ActivityHeader.vue:128
msgid "Schedule an event"
msgstr "Schemalägg händelse"

#. Label of the script (Code) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Script"
msgstr "Skript"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:22
msgid "Search"
msgstr "Sök"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:64
msgid "Search template"
msgstr "Sök mall"

#: frontend/src/components/Settings/Users.vue:73
msgid "Search user"
msgstr "Sök användare"

#: frontend/src/components/FieldLayoutEditor.vue:336
msgid "Section"
msgstr "Sektion"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:130
msgid "See all participants"
msgstr "Se alla deltagare"

#: frontend/src/pages/Dashboard.vue:50
msgid "Select Range"
msgstr "Välj Intervall"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:47
msgid "Select Source Type"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:51
msgid "Select currency"
msgstr "Välj valuta"

#: frontend/src/components/Settings/CurrencySettings.vue:75
msgid "Select provider"
msgstr "Välj leverantör"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:84
msgid "Select the assignees for {0}."
msgstr "Välj de tilldelade för {0}."

#: frontend/src/components/FieldLayout/Field.vue:345
msgid "Select {0}"
msgstr "Välj {0}"

#: frontend/src/components/EmailEditor.vue:165
msgid "Send"
msgstr "Skicka"

#: frontend/src/components/Settings/InviteUserPage.vue:18
msgid "Send Invites"
msgstr "Skicka Inbjudningar"

#: frontend/src/components/Activities/ActivityHeader.vue:61
msgid "Send Template"
msgstr "Skicka Mall"

#: frontend/src/pages/Deal.vue:87 frontend/src/pages/Lead.vue:134
msgid "Send an email"
msgstr "Skicka e-post"

#: frontend/src/components/Layouts/AppSidebar.vue:460
msgid "Send email"
msgstr "Skicka e-post"

#: frontend/src/components/Settings/InviteUserPage.vue:6
msgid "Send invites to"
msgstr "Skicka inbjudan till"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Separator"
msgstr "Separator"

#. Label of the naming_series (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Series"
msgstr "Namngivning Serie"

#. Label of the service_provider (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Service Provider"
msgstr "Service Leverantör"

#: frontend/src/components/Layouts/AppSidebar.vue:581
msgid "Service level agreement"
msgstr "Service Nivå Avtal"

#: frontend/src/components/PrimaryDropdownItem.vue:27
msgid "Set As Primary"
msgstr "Ange som Primär"

#: frontend/src/components/FilesUploader/FilesUploader.vue:66
msgid "Set all as private"
msgstr "Ange alla som privata"

#: frontend/src/components/FilesUploader/FilesUploader.vue:59
msgid "Set all as public"
msgstr "Ange alla som offentliga"

#: frontend/src/pages/Deal.vue:73
msgid "Set an organization"
msgstr "Ange Organisation"

#: frontend/src/pages/Deal.vue:636 frontend/src/pages/MobileDeal.vue:523
msgid "Set as Primary Contact"
msgstr "Ange som Primär Kontakt"

#: frontend/src/components/ViewControls.vue:1078
msgid "Set as default"
msgstr "Ange som standard"

#: frontend/src/components/Settings/CurrencySettings.vue:164
msgid "Set currency"
msgstr "Ange valuta"

#: frontend/src/pages/Lead.vue:115
msgid "Set first name"
msgstr "Ange förnamn"

#: frontend/src/components/Layouts/AppSidebar.vue:533
msgid "Setting up"
msgstr "Konfigurera"

#: frontend/src/components/Settings/emailConfig.js:145
msgid "Setting up Frappe Mail requires you to have an API key and API Secret of your email account. Read more "
msgstr "För att konfigurera Frappe Mail erfordras API Nyckel och API Hemlighet för e-postkonto. Läs mer om Frappe Mail "

#: frontend/src/components/Settings/emailConfig.js:97
msgid "Setting up GMail requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "För att konfigurera GMail erfordras aktivering av tvåfaktorsautentisering\n"
"\t\t  och appspecifika lösenord. Läs mer om GMail"

#: frontend/src/components/Settings/emailConfig.js:105
msgid "Setting up Outlook requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "För att konfigurera Outlook erfordras aktivering av tvåfaktorsautentisering\n"
"\t\t  och appspecifika lösenord. Läs mer"

#: frontend/src/components/Settings/emailConfig.js:113
msgid "Setting up Sendgrid requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "För att konfigurera Sendgrid erfordras aktivering av tvåfaktorsautentisering\n"
"\t\t  och appspecifika lösenord. Läs mer "

#: frontend/src/components/Settings/emailConfig.js:121
msgid "Setting up SparkPost requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "För att konfigurera SparkPost erfordras aktivering av tvåfaktorsautentisering\n"
"\t\t  och appspecifika lösenord. Läs mer "

#: frontend/src/components/Settings/emailConfig.js:129
msgid "Setting up Yahoo requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "För att konfigurera Yahoo erfordras aktivering av tvåfaktorsautentisering\n"
"\t\t  och appspecifika lösenord. Läs mer "

#: frontend/src/components/Settings/emailConfig.js:137
msgid "Setting up Yandex requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "För att konfigurera Yandex erfordras aktivering av tvåfaktorsautentisering\n"
"\t\t  och appspecifika lösenord. Läs mer "

#. Label of the defaults_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Layouts/AppSidebar.vue:537
#: frontend/src/components/Settings/Settings.vue:12
msgid "Settings"
msgstr "Inställningar"

#: frontend/src/components/Settings/EmailAdd.vue:6
msgid "Setup Email"
msgstr "Konfigurera e-post"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:199
msgid "Setup the Exchange Rate Provider as 'Exchangerate Host' in settings, as default provider does not support currency conversion for {0} to {1}."
msgstr "Ange Växelkurs Leverantör som \"Valuta Växling Leverantör\" i inställningar, eftersom standard leverantör inte stöder valutakonvertering från {0} till {1}."

#: frontend/src/components/Layouts/AppSidebar.vue:339
msgid "Setup your password"
msgstr "Ange Lösenord"

#: frontend/src/components/Activities/Activities.vue:237
msgid "Show"
msgstr "Visa"

#: frontend/src/components/Controls/Password.vue:19
msgid "Show Password"
msgstr "Visa Lösenord"

#: frontend/src/components/FieldLayoutEditor.vue:354
msgid "Show border"
msgstr "Visa kant"

#: frontend/src/components/FieldLayoutEditor.vue:349
msgid "Show label"
msgstr "Visa etikett"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:138
msgid "Show less"
msgstr "Visa mindre"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Show preview"
msgstr "Visa förhandsvisning"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Side Panel"
msgstr "Sidopanel"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Sidebar Items"
msgstr "Sidofält Element"

#. Description of the 'Condition' (Code) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Simple Python Expression, Example: doc.status == 'Open' and doc.lead_source == 'Ads'"
msgstr "Enkelt Python Uttryck, Exempel: doc.status == 'Open' och doc.lead_source == 'Ads'"

#: frontend/src/components/AssignTo.vue:83
msgid "Since you removed {0} from the assignee, the {0} has also been removed."
msgstr "Eftersom du tog bort {0} från tilldelad har även {0} tagits bort."

#: frontend/src/components/AssignTo.vue:76
msgid "Since you removed {0} from the assignee, the {0} has been changed to the next available assignee {1}."
msgstr "Sedan du tog bort {0} från tilldelad har {0} ändrats till näst tillgänglig tilldelad {1}."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:735
msgid "Some error occurred while renaming assignment rule"
msgstr "Ett fel uppstod när tilldelningsregeln bytte namn"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:720
msgid "Some error occurred while updating assignment rule"
msgstr "Ett fel uppstod när tilldelningsregeln uppdaterades"

#: frontend/src/components/SortBy.vue:10 frontend/src/components/SortBy.vue:24
#: frontend/src/components/SortBy.vue:232
msgid "Sort"
msgstr "Sortera"

#. Label of the source (Link) field in DocType 'CRM Deal'
#. Label of the source (Link) field in DocType 'CRM Lead'
#. Label of the source (Link) field in DocType 'Failed Lead Sync Log'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: frontend/src/components/Modals/EditValueModal.vue:10
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:60
msgid "Source"
msgstr "Från"

#. Label of the source_name (Data) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:69
msgid "Source Name"
msgstr "Käll Namn"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:46
msgid "Source Type"
msgstr "Käll Typ"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:167
msgid "Source disabled successfully"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:166
msgid "Source enabled successfully"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:68
#: frontend/src/components/Dashboard/DashboardItem.vue:21
msgid "Spacer"
msgstr "Mellanrum"

#: crm/api/dashboard.py:758 crm/api/dashboard.py:820
msgid "Stage"
msgstr "Fas"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:15
msgid "Standard Form Scripts can not be modified, duplicate the Form Script instead."
msgstr "Standard Formulär Skript kan inte ändras, kopiera Formulär Skript istället."

#. Label of the standard_rate (Currency) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Standard Selling Rate"
msgstr "Standard Försäljning Pris"

#: frontend/src/components/ViewControls.vue:634
msgid "Standard Views"
msgstr "Standard Vyer"

#. Label of the start_date (Date) field in DocType 'CRM Service Level
#. Agreement'
#. Label of the start_date (Date) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Start Date"
msgstr "Start Datum"

#. Label of the start_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the start_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:234
#: frontend/src/components/Modals/EventModal.vue:103
msgid "Start Time"
msgstr "Start Tid"

#: frontend/src/composables/event.js:198
msgid "Start and end time are required"
msgstr "Start och sluttid erfordras"

#: frontend/src/pages/Welcome.vue:21
msgid "Start with sample 10 leads"
msgstr "Börja med exempel på 10 potentiella kunder"

#. Label of the status (Select) field in DocType 'CRM Call Log'
#. Label of the status (Data) field in DocType 'CRM Communication Status'
#. Label of the status (Link) field in DocType 'CRM Deal'
#. Label of the deal_status (Data) field in DocType 'CRM Deal Status'
#. Label of the status (Select) field in DocType 'CRM Invitation'
#. Label of the status (Link) field in DocType 'CRM Lead'
#. Label of the lead_status (Data) field in DocType 'CRM Lead Status'
#. Label of the status (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_task/crm_task.json frontend/src/pages/Contact.vue:513
#: frontend/src/pages/MobileContact.vue:513
#: frontend/src/pages/MobileOrganization.vue:457
#: frontend/src/pages/Organization.vue:487
msgid "Status"
msgstr "Status"

#. Label of the status_change_log (Table) field in DocType 'CRM Deal'
#. Label of the status_change_log (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Status Change Log"
msgstr "Statusändring Logg"

#: frontend/src/components/Modals/DealModal.vue:216
#: frontend/src/components/Modals/LeadModal.vue:157
msgid "Status is required"
msgstr "Status erfordras"

#. Label of the subdomain (Data) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Subdomain"
msgstr "Underdomän"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:68
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:68
msgid "Subject"
msgstr "Ämne"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:155
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:158
msgid "Subject is required"
msgstr "Ämne erfordras"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:45
msgid "Subject: {0}"
msgstr "Ämne: {0}"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Sunday"
msgstr "Söndag"

#: frontend/src/components/Settings/emailConfig.js:16
msgid "Support / Sales"
msgstr "Support / Försäljning"

#: frontend/src/components/FilesUploader/FilesUploader.vue:46
msgid "Switch camera"
msgstr "Byt kamera"

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.js:6
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:23
msgid "Sync Now"
msgstr "Synkronisera Nu"

#: frontend/src/pages/Welcome.vue:32
msgid "Sync your contacts,email and calenders"
msgstr "Synkronisera dina kontakter, e-post och kalendrar"

#. Label of the syncing_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Syncing"
msgstr "Synkroniserar"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:359
msgid "Syncing started in background"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:106
msgid "System Configuration"
msgstr "System Konfiguration"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "System Manager"
msgstr "System Ansvarig"

#: frontend/src/components/EmailEditor.vue:22
msgid "TO"
msgstr "TILL"

#: frontend/src/components/Telephony/ExotelCallUI.vue:149
msgid "Take a note..."
msgstr "Ta anteckning..."

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:555
msgid "Task"
msgstr "Uppgift"

#: frontend/src/pages/Deal.vue:567 frontend/src/pages/Lead.vue:414
#: frontend/src/pages/MobileDeal.vue:456 frontend/src/pages/MobileLead.vue:362
msgid "Tasks"
msgstr "Uppgifter"

#: frontend/src/components/Modals/AboutModal.vue:67
msgid "Telegram Channel"
msgstr "Telegram Kanal"

#: frontend/src/components/Settings/Settings.vue:185
msgid "Telephony"
msgstr "Telefoni"

#. Label of the telephony_medium (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Telephony Medium"
msgstr "Telefoni Medium"

#: frontend/src/components/Settings/TelephonySettings.vue:8
msgid "Telephony settings"
msgstr "Telefoni Inställningar"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:175
msgid "Template created successfully"
msgstr "Mall skapad"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:213
msgid "Template deleted successfully"
msgstr "Mall borttagen"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:197
msgid "Template disabled successfully"
msgstr "Mall inaktiverad"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:196
msgid "Template enabled successfully"
msgstr "Mall aktiverad"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:83
msgid "Template name"
msgstr "Mall Namn"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:239
msgid "Template renamed successfully"
msgstr "Mall namn ändrad"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:204
msgid "Template updated successfully"
msgstr "Mall uppdaterad"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Territories"
msgstr "Distrikt"

#. Label of the territory (Link) field in DocType 'CRM Deal'
#. Label of the territory (Link) field in DocType 'CRM Lead'
#. Label of the territory (Link) field in DocType 'CRM Organization'
#: crm/api/dashboard.py:1067 crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Territory"
msgstr "Distrikt"

#. Label of the territory_manager (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Manager"
msgstr "Distrikt Ansvarig"

#. Label of the territory_name (Data) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Name"
msgstr "Distrikt Namn"

#: crm/templates/emails/helpdesk_invitation.html:16
msgid "Thanks"
msgstr "Tack"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:46
msgid "The Condition '{0}' is invalid: {1}"
msgstr "Villkor '{0}' är ogiltigt: {1}"

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "The rate used to convert the deal’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "Kurs som används för att konvertera affärens valuta till Säljstöd basvaluta (angiven i Säljstöd Inställningar). Anges en gång när valuta läggs till första gången och ändras inte automatiskt."

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "The rate used to convert the organization’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "Kurs som används för att konvertera bolag valuta till Säljstöd bas valuta (angiven i Säljstöd Inställningar). Anges en gång när valuta läggs till första gången och ändras inte automatiskt."

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.js:14
msgid "There can only be one default priority in Priorities table"
msgstr "Det kan bara finnas en standard prioritet i Prioriteter tabellen "

#: frontend/src/components/Modals/AddressModal.vue:128
#: frontend/src/components/Modals/CallLogModal.vue:131
msgid "These fields are required: {0}"
msgstr "Dessa fält erfordras: {0}"

#: frontend/src/components/Filter.vue:639
msgid "This Month"
msgstr "Denna Månad"

#: frontend/src/components/Filter.vue:643
msgid "This Quarter"
msgstr "Detta Kvartal"

#: frontend/src/components/Filter.vue:635
msgid "This Week"
msgstr "Denna Vecka"

#: frontend/src/components/Filter.vue:647
msgid "This Year"
msgstr "I År"

#: frontend/src/components/SidePanelLayoutEditor.vue:116
msgid "This section is not editable"
msgstr "Detta avsnitt är inte redigerbar"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:59
msgid "This will delete selected items and items linked to it, are you sure?"
msgstr "Detta kommer att ta bort valda artiklar och artiklar som är länkade till dem, är du säker?"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:62
msgid "This will delete selected items and unlink linked items to it, are you sure?"
msgstr "Detta kommer att ta bort valda artiklar och ta bort länk till länkade artiklar, är du säker?"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:9
msgid "This will restore (if not exist) all the default statuses, custom fields and layouts. Delete & Restore will delete default layouts and then restore them."
msgstr "Detta kommer att återställa (om det inte finns) alla standard status, anpassade fält och upplägg. Ta bort & Återställ kommer att ta bort standard upplägg och sedan återställa."

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Thursday"
msgstr "Torsdag"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:227
msgid "Time"
msgstr "Tid"

#: frontend/src/components/Filter.vue:351
msgid "Timespan"
msgstr "Tid Intervall"

#. Label of the title (Data) field in DocType 'CRM Task'
#. Label of the title (Data) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/EventModal.vue:43
#: frontend/src/components/Modals/NoteModal.vue:26
#: frontend/src/components/Modals/TaskModal.vue:25
msgid "Title"
msgstr "Benämning"

#. Label of the title_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:29
msgid "Title Field"
msgstr "Titel Fält"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:545
#: frontend/src/components/Modals/EventModal.vue:320
msgid "Title is required"
msgstr "Benämning erfordras"

#. Label of the to (Data) field in DocType 'CRM Call Log'
#. Label of the to (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
#: frontend/src/components/Activities/EmailArea.vue:53
msgid "To"
msgstr "Till"

#. Label of the to_date (Date) field in DocType 'CRM Holiday List'
#. Label of the to_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Date"
msgstr "Till Datum"

#. Label of the to_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Type"
msgstr "Till Typ"

#. Label of the to_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "To User"
msgstr "Till Användare"

#: frontend/src/components/Settings/EmailEdit.vue:118
msgid "To know more about setting up email accounts, click"
msgstr "För att veta mer om hur du konfigurerar e-post konton, klicka här"

#: frontend/src/components/Filter.vue:627 frontend/src/pages/Calendar.vue:79
msgid "Today"
msgstr "Idag"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Todo"
msgstr "Att Göra"

#: frontend/src/components/Modals/SidePanelModal.vue:59
msgid "Toggle on for preview"
msgstr "Slå på för förhandsgranskning"

#: frontend/src/components/Filter.vue:631
msgid "Tomorrow"
msgstr "I morgon"

#: frontend/src/components/Modals/NoteModal.vue:42
#: frontend/src/components/Modals/TaskModal.vue:46
msgid "Took a call with John Doe and discussed the new project."
msgstr "Tog samtal med John Doe och diskuterade ny projekt."

#. Label of the total (Currency) field in DocType 'CRM Deal'
#. Label of the total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total"
msgstr "Totalt"

#. Label of the total_holidays (Int) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Total Holidays"
msgstr "Totalt Helg Dagar"

#. Description of the 'Net Total' (Currency) field in DocType 'CRM Deal'
#. Description of the 'Net Total' (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total after discount"
msgstr "Totalt efter rabatt"

#: crm/api/dashboard.py:129
#: frontend/src/components/Dashboard/AddChartModal.vue:76
msgid "Total leads"
msgstr "Total antal potentiella kunder"

#: crm/api/dashboard.py:130
msgid "Total number of leads"
msgstr "Total antal potentiella kunder"

#: crm/api/dashboard.py:191
msgid "Total number of non won/lost deals"
msgstr "Totalt antal förlorade affärer"

#: crm/api/dashboard.py:311
msgid "Total number of won deals based on its closure date"
msgstr "Totalt antal vunna affärer baserat på avslutning datum"

#. Label of the traceback (Code) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Traceback"
msgstr "Spåra Tillbaka"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Tuesday"
msgstr "Tisdag"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:173
msgid "Turn into a group"
msgstr "Gör om till grupp"

#. Label of the twiml_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "TwiML SID"
msgstr "TwiML SID"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the twilio (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:601
#: frontend/src/components/Settings/TelephonySettings.vue:40
#: frontend/src/components/Settings/TelephonySettings.vue:50
msgid "Twilio"
msgstr "Twilio"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:59
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:60
msgid "Twilio API credential creation error."
msgstr "Fel vid skapande av autentiseringsuppgifter för Twilio API."

#. Label of the twilio_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Twilio Number"
msgstr "Twilio Nummer"

#: frontend/src/components/Settings/TelephonySettings.vue:289
msgid "Twilio is not enabled"
msgstr "Twilio är inte aktiverat"

#: frontend/src/components/Settings/TelephonySettings.vue:125
msgid "Twilio settings updated successfully"
msgstr "Twilio Inställningarna uppdaterade"

#. Label of the type (Select) field in DocType 'CRM Call Log'
#. Label of the type (Select) field in DocType 'CRM Deal Status'
#. Label of the type (Select) field in DocType 'CRM Dropdown Item'
#. Label of the type (Select) field in DocType 'CRM Fields Layout'
#. Label of the type (Select) field in DocType 'CRM Global Settings'
#. Label of the type (Select) field in DocType 'CRM Notification'
#. Label of the type (Select) field in DocType 'CRM View Settings'
#. Label of the type (Data) field in DocType 'Facebook Lead Form Question'
#. Label of the type (Select) field in DocType 'Failed Lead Sync Log'
#. Label of the type (Select) field in DocType 'Lead Sync Source'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Type"
msgstr "Typ"

#: frontend/src/components/Calendar/Attendee.vue:233
msgid "Type an email address to add attendee"
msgstr "Skriv in e-postadress för att lägga till deltagare"

#: frontend/src/components/Activities/WhatsAppBox.vue:85
msgid "Type your message here..."
msgstr "Skriv ditt meddelande här..."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:454
msgid "Unassign conditions are invalid"
msgstr "Villkoren för att ta bort tilldelning är ogiltiga"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:224
msgid "Unassignment condition"
msgstr "Tilldelning Återkallelse Villkor"

#: crm/integrations/exotel/handler.py:170
msgid "Unauthorized request"
msgstr "Obehörig begäran"

#: frontend/src/components/FieldLayoutEditor.vue:344
msgid "Uncollapsible"
msgstr "Ej Hopfällbar"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:183
msgid "Ungroup conditions"
msgstr "Avgruppera villkor"

#: frontend/src/components/Telephony/TwilioCallUI.vue:24
#: frontend/src/components/Telephony/TwilioCallUI.vue:123
msgid "Unknown"
msgstr "Okänd"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:132
msgid "Unlink"
msgstr "Ta bort länk"

#: frontend/src/components/DeleteLinkedDocModal.vue:78
msgid "Unlink all"
msgstr "Ta bort länk från alla"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
msgid "Unlink and delete"
msgstr "Ta bort länk och radera"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:35
msgid "Unlink and delete {0} items"
msgstr "Ta bort länkar och radera {0} artiklar"

#: frontend/src/components/DeleteLinkedDocModal.vue:243
msgid "Unlink linked item"
msgstr "Ta bort länk till länkad artikel"

#: frontend/src/components/DeleteLinkedDocModal.vue:79
msgid "Unlink {0} item(s)"
msgstr "Ta bort länk till {0} artikel(ar)"

#: frontend/src/components/ViewControls.vue:1093
msgid "Unpin View"
msgstr "Lossa Vy"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:22
msgid "Unsaved"
msgstr "Osparad"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:575
msgid "Unsaved changes"
msgstr "Osparade ändringar"

#: frontend/src/components/FieldLayoutEditor.vue:26
#: frontend/src/components/Modals/AddressModal.vue:8
#: frontend/src/components/Modals/CallLogModal.vue:8
#: frontend/src/components/Modals/CreateDocumentModal.vue:8
#: frontend/src/components/Section.vue:21
#: frontend/src/components/SidePanelLayoutEditor.vue:19
msgid "Untitled"
msgstr "Namnlös"

#: frontend/src/components/ColumnSettings.vue:129
#: frontend/src/components/Modals/AssignmentModal.vue:79
#: frontend/src/components/Modals/ChangePasswordModal.vue:45
#: frontend/src/components/Modals/EventModal.vue:156
#: frontend/src/components/Modals/NoteModal.vue:52
#: frontend/src/components/Modals/TaskModal.vue:106
#: frontend/src/components/Settings/BrandSettings.vue:15
#: frontend/src/components/Settings/CurrencySettings.vue:17
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:21
#: frontend/src/components/Settings/HomeActions.vue:15
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:30
#: frontend/src/components/Settings/ProfileSettings.vue:95
#: frontend/src/components/Settings/SettingsPage.vue:20
#: frontend/src/components/Settings/TelephonySettings.vue:23
#: frontend/src/components/Telephony/ExotelCallUI.vue:210
msgid "Update"
msgstr "Uppdatera"

#: frontend/src/components/Settings/EmailEdit.vue:74
msgid "Update Account"
msgstr "Uppdatera Konto"

#: frontend/src/components/Modals/EditValueModal.vue:30
msgid "Update {0} Records"
msgstr "Uppdatera {0} Poster"

#: frontend/src/components/Controls/ImageUploader.vue:20
#: frontend/src/components/FilesUploader/FilesUploader.vue:83
msgid "Upload"
msgstr "Ladda upp"

#: frontend/src/components/Activities/Activities.vue:408
#: frontend/src/components/Activities/ActivityHeader.vue:55
#: frontend/src/components/Activities/ActivityHeader.vue:154
msgid "Upload Attachment"
msgstr "Ladda upp Bilaga"

#: frontend/src/components/Activities/WhatsAppBox.vue:132
msgid "Upload Document"
msgstr "Ladda upp Dokument"

#: frontend/src/components/Activities/WhatsAppBox.vue:140
msgid "Upload Image"
msgstr "Ladda upp Bild"

#: frontend/src/components/Activities/WhatsAppBox.vue:148
msgid "Upload Video"
msgstr "Ladda upp Video"

#: frontend/src/components/Settings/ProfileSettings.vue:27
#: frontend/src/pages/Contact.vue:48 frontend/src/pages/Lead.vue:89
#: frontend/src/pages/MobileContact.vue:38
#: frontend/src/pages/MobileOrganization.vue:38
#: frontend/src/pages/Organization.vue:48
msgid "Upload image"
msgstr "Ladda upp Bild"

#: frontend/src/components/Controls/ImageUploader.vue:17
msgid "Uploading {0}%"
msgstr "Ladda Upp {0}%"

#. Label of the user (Link) field in DocType 'CRM Dashboard'
#. Label of the user (Link) field in DocType 'CRM Telephony Agent'
#. Label of the user (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "User"
msgstr "Användare"

#: frontend/src/components/Settings/Settings.vue:127
msgid "User Management"
msgstr "Användare Hantering"

#. Label of the user_name (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "User Name"
msgstr "Användare Namn"

#: frontend/src/components/Settings/Users.vue:296
msgid "User {0} has been removed"
msgstr "Användare {0} borttagen"

#: frontend/src/components/Modals/AddExistingUserModal.vue:20
#: frontend/src/components/Settings/Settings.vue:130
#: frontend/src/components/Settings/Users.vue:7
msgid "Users"
msgstr "Användare"

#: frontend/src/components/Modals/AddExistingUserModal.vue:105
msgid "Users added successfully"
msgstr "Användare tillagd"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:465
msgid "Users are required"
msgstr "Användare erfordras"

#. Label of the section_break_nevd (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Validity"
msgstr "Giltighet"

#: frontend/src/components/Modals/EditValueModal.vue:14
msgid "Value"
msgstr "Värde"

#: frontend/src/components/Modals/ViewModal.vue:14
msgid "View Name"
msgstr "Visa Namn"

#: frontend/src/pages/Deal.vue:219
msgid "View contact"
msgstr "Visa kontakt"

#: frontend/src/components/Layouts/AppSidebar.vue:566
msgid "Views"
msgstr "Vyer"

#: frontend/src/components/Layouts/AppSidebar.vue:563
msgid "Web form"
msgstr "Webbformulär"

#. Label of the webhook_verify_token (Data) field in DocType 'CRM Exotel
#. Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Webhook Verify Token"
msgstr "Webhook Verifiering Token"

#. Label of the website (Data) field in DocType 'CRM Deal'
#. Label of the website (Data) field in DocType 'CRM Lead'
#. Label of the website (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: frontend/src/components/Modals/AboutModal.vue:52
msgid "Website"
msgstr "Webbplats"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Wednesday"
msgstr "Onsdag"

#. Label of the weekly_off (Check) field in DocType 'CRM Holiday'
#. Label of the weekly_off (Select) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Weekly Off"
msgstr "Helg"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:11
msgid "Welcome Message"
msgstr "Välkomstmeddelande"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:155
msgid "Welcome to Helpdesk"
msgstr "Välkommen till Helpdesk"

#: frontend/src/pages/Welcome.vue:4
msgid "Welcome {0}, lets add your first lead"
msgstr "Välkommen {0}, låt oss lägga till första potentiella kund"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:603
#: frontend/src/components/Settings/Settings.vue:191
#: frontend/src/pages/Deal.vue:582 frontend/src/pages/Lead.vue:429
#: frontend/src/pages/MobileDeal.vue:471 frontend/src/pages/MobileLead.vue:377
msgid "WhatsApp"
msgstr "WhatsApp"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:4
msgid "WhatsApp Templates"
msgstr "WhatsApp Mallar"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:20
#: frontend/src/components/Filter.vue:43 frontend/src/components/Filter.vue:81
msgid "Where"
msgstr "Var"

#: frontend/src/components/ColumnSettings.vue:108
msgid "Width"
msgstr "Bredd"

#: frontend/src/components/ColumnSettings.vue:113
msgid "Width can be in number, pixel or rem (eg. 3, 30px, 10rem)"
msgstr "Bredd kan anges i antal, pixel eller rem (t.ex. 3, 30px, 10rem)"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Won"
msgstr "Vunnen"

#: crm/api/dashboard.py:310
#: frontend/src/components/Dashboard/AddChartModal.vue:79
msgid "Won deals"
msgstr "Vunna affärer"

#. Label of the workday (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Workday"
msgstr "Arbetsdag"

#. Label of the section_break_rmgo (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#. Label of the working_hours (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Working Hours"
msgstr "Arbets Timmar"

#: frontend/src/components/Filter.vue:623
msgid "Yesterday"
msgstr "Igår"

#: crm/api/whatsapp.py:43 crm/api/whatsapp.py:223 crm/api/whatsapp.py:237
#: frontend/src/components/Activities/WhatsAppArea.vue:34
#: frontend/src/components/Activities/WhatsAppBox.vue:14
msgid "You"
msgstr "Du"

#: crm/utils/__init__.py:262
msgid "You are not permitted to access this resource."
msgstr "Du har inte behörighet att komma åt denna resurs."

#: crm/templates/emails/helpdesk_invitation.html:22
msgid "You can also copy-paste following link in your browser"
msgstr "Du kan också kopiera och klistra in följande länk i din webbläsare"

#: frontend/src/components/Telephony/CallUI.vue:39
msgid "You can change the default calling medium from the settings"
msgstr "Du kan ändra standarduppringning medium från inställningarna"

#: frontend/src/components/Settings/CurrencySettings.vue:100
msgid "You can get your access key from "
msgstr "Hämta Åtkomst Nyckel från "

#: frontend/src/components/Settings/InviteUserPage.vue:36
msgid "You can invite multiple users by comma separating their email addresses"
msgstr "Du kan bjuda in flera användare genom att separera deras e-postadresser med kommatecken"

#: crm/integrations/exotel/handler.py:85
msgid "You do not have Exotel Number set in your Telephony Agent"
msgstr "Du har inget Exotel Nummer angiven i din Telefoni Agent"

#: crm/integrations/exotel/handler.py:93
msgid "You do not have mobile number set in your Telephony Agent"
msgstr "Du har inget Mobil Nummer angiven i din Telefoni Agent"

#: frontend/src/data/document.js:34
msgid "You do not have permission to access this document"
msgstr "Du har inte behörighet att komma åt detta dokument"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.py:24
msgid "You need to be in developer mode to edit a Standard Form Script"
msgstr "Du måste vara i Utvecklarläge att redigera Standard Formulär Skript"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:150
msgid "You will be redirected to invite user page, unsaved changes will be lost."
msgstr "Du kommer att omdirigeras till inbjudning sida för användare, osparade ändringar kommer att gå förlorade."

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:9
msgid "You will need a Meta developer account and an access token to sync leads from Facebook. Read more "
msgstr ""

#: crm/api/todo.py:100
msgid "Your assignment on task {0} has been removed by {1}"
msgstr "Din tilldelning för uppgift {0} togs bort av {1}"

#: crm/api/todo.py:37 crm/api/todo.py:78
msgid "Your assignment on {0} {1} has been removed by {2}"
msgstr "Din tilldelning {0} {1} togs bort av {2}"

#: crm/templates/emails/helpdesk_invitation.html:6
msgid "Your login id is"
msgstr "Ditt Inlogging ID är"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:610
msgid "Your old condition will be overwritten. Are you sure you want to save?"
msgstr "Gamla villkor kommer att skrivas över. Är du säker på att du vill spara?"

#: frontend/src/components/Activities/CommentArea.vue:9
msgid "added a"
msgstr "la till "

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "amber"
msgstr "bärnsten"

#: crm/api/todo.py:109
msgid "assigned a new task {0} to you"
msgstr "tilldelade dig en ny uppgift {0}"

#: crm/api/todo.py:89
msgid "assigned a {0} {1} to you"
msgstr "tilldelade {0} {1} till dig"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "black"
msgstr "svart"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "blue"
msgstr "blå"

#: frontend/src/components/Activities/Activities.vue:239
msgid "changes from"
msgstr "ändras från"

#: frontend/src/components/Activities/CommentArea.vue:11
msgid "comment"
msgstr "kommentar"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:65
#: frontend/src/components/ConditionsFilter/CFCondition.vue:73
msgid "condition"
msgstr "villkor"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "cyan"
msgstr "cyan"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:131
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:380
msgid "deals"
msgstr "affärer"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:190
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:274
msgid "desk"
msgstr "skrivbord"

#: frontend/src/components/Calendar/Attendee.vue:295
#: frontend/src/components/Controls/EmailMultiSelect.vue:254
msgid "email already exists"
msgstr "e-post adress finns redan"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/CurrencySettings.vue:106
msgid "exchangerate.host"
msgstr "exchangerate.host"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "frankfurter.app"
msgstr "frankfurter.app"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "gray"
msgstr "grå"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "green"
msgstr "grön"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "group_by"
msgstr "grupp_efter"

#: frontend/src/components/Activities/CallArea.vue:16
msgid "has made a call"
msgstr "har ringt samtal"

#: frontend/src/components/Activities/CallArea.vue:15
msgid "has reached out"
msgstr "har kontaktat"

#: frontend/src/components/Settings/EmailAdd.vue:36
#: frontend/src/components/Settings/EmailEdit.vue:25
msgid "here"
msgstr "här"

#: frontend/src/utils/index.js:146
msgid "in 1 hour"
msgstr "om 1 timme"

#: frontend/src/utils/index.js:142
msgid "in 1 minute"
msgstr "om 1 minut"

#: frontend/src/utils/index.js:160
msgid "in 1 year"
msgstr "om 1 år"

#: frontend/src/utils/index.js:111
msgid "in {0} M"
msgstr "om {0} M"

#: frontend/src/utils/index.js:107
msgid "in {0} d"
msgstr "om {0} d"

#: frontend/src/utils/index.js:154
msgid "in {0} days"
msgstr "om {0} dagar"

#: frontend/src/utils/index.js:101
msgid "in {0} h"
msgstr "om {0} t"

#: frontend/src/utils/index.js:148
msgid "in {0} hours"
msgstr "om {0} timmar"

#: frontend/src/utils/index.js:99
msgid "in {0} m"
msgstr "om {0} m"

#: frontend/src/utils/index.js:144
msgid "in {0} minutes"
msgstr "om {0} minuter"

#: frontend/src/utils/index.js:158
msgid "in {0} months"
msgstr "om {0} månader"

#: frontend/src/utils/index.js:109
msgid "in {0} w"
msgstr "om {0} v"

#: frontend/src/utils/index.js:156
msgid "in {0} weeks"
msgstr "om {0} veckor"

#: frontend/src/utils/index.js:113
msgid "in {0} y"
msgstr "om {0} år"

#: frontend/src/utils/index.js:162
msgid "in {0} years"
msgstr "i {0} år"

#: frontend/src/components/Modals/AddExistingUserModal.vue:28
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: frontend/src/utils/index.js:140 frontend/src/utils/index.js:166
msgid "just now"
msgstr "just nu"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "kanban"
msgstr "anslagstavla"

#: crm/api/doc.py:40 crm/api/doc.py:158 crm/api/doc.py:503
msgid "label"
msgstr "etikett"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:130
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:379
msgid "leads"
msgstr "potentiella Kunder"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "list"
msgstr "lista"

#: crm/api/comment.py:36 frontend/src/components/Notifications.vue:59
#: frontend/src/pages/MobileNotification.vue:46
msgid "mentioned you in {0}"
msgstr "nämnde dig i {0}"

#: frontend/src/components/FieldLayoutEditor.vue:368
msgid "next"
msgstr "nästa"

#: frontend/src/utils/index.js:97 frontend/src/utils/index.js:117
msgid "now"
msgstr "nu"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:47
msgid "operator"
msgstr "personal"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "orange"
msgstr "orange"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "pink"
msgstr "rosa"

#: frontend/src/components/FieldLayoutEditor.vue:368
msgid "previous"
msgstr "föregående"

#: frontend/src/components/Activities/AttachmentArea.vue:97
msgid "private"
msgstr "privat"

#: frontend/src/components/Activities/AttachmentArea.vue:97
msgid "public"
msgstr "publik"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "purple"
msgstr "lila"

#: crm/api/whatsapp.py:44
msgid "received a whatsapp message in {0}"
msgstr "fick whatsapp meddelande i {0}"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "red"
msgstr "röd"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "teal"
msgstr "blågrön"

#: frontend/src/components/Activities/Activities.vue:278
#: frontend/src/components/Activities/Activities.vue:341
msgid "to"
msgstr "till"

#: frontend/src/utils/index.js:105 frontend/src/utils/index.js:152
msgid "tomorrow"
msgstr "i morgon"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "violet"
msgstr "violett"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:193
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:277
msgid "which are not compatible with this UI, you will need to recreate the conditions here if you want to manage and add new conditions from this UI."
msgstr "som inte är kompatibla med detta användargränssnitt, återskapa villkoren här om du vill hantera och lägga till nya villkor från detta användargränssnitt."

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "yellow"
msgstr "gul"

#: frontend/src/utils/index.js:179
msgid "yesterday"
msgstr "igår"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:94
msgid "{0} Attendees"
msgstr "{0} Deltagare"

#: frontend/src/utils/index.js:130
msgid "{0} M"
msgstr "{0} M"

#: crm/api/todo.py:41
msgid "{0} assigned a {1} {2} to you"
msgstr "{0} tilldelade {1} {2} till dig"

#: frontend/src/utils/index.js:126
msgid "{0} d"
msgstr "{0} d"

#: frontend/src/utils/index.js:181
msgid "{0} days ago"
msgstr "{0} dagar sedan"

#: frontend/src/utils/index.js:121
msgid "{0} h"
msgstr "{0} h"

#: frontend/src/components/Settings/Users.vue:286
msgid "{0} has been granted {1} access"
msgstr "{0} har beviljats {1} åtkomst"

#: frontend/src/utils/index.js:174
msgid "{0} hours ago"
msgstr "{0} timmar sedan"

#: frontend/src/composables/event.js:163
msgid "{0} hrs"
msgstr "{0} timmar"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:309
#: frontend/src/components/EmailEditor.vue:30
#: frontend/src/components/EmailEditor.vue:66
#: frontend/src/components/EmailEditor.vue:80
#: frontend/src/components/Modals/AddExistingUserModal.vue:37
#: frontend/src/components/Modals/EventModal.vue:127
msgid "{0} is an invalid email address"
msgstr "{0} är ogiltig e-post adress"

#: frontend/src/components/Modals/ConvertToDealModal.vue:172
msgid "{0} is required"
msgstr "{0} är erfodrad"

#: frontend/src/utils/index.js:119
msgid "{0} m"
msgstr "{0} m"

#: frontend/src/composables/event.js:157
msgid "{0} mins"
msgstr "{0} minuter"

#: frontend/src/utils/index.js:170
msgid "{0} minutes ago"
msgstr "{0} minuter sedan"

#: frontend/src/utils/index.js:189
msgid "{0} months ago"
msgstr "{0} månader sedan"

#: frontend/src/utils/index.js:128
msgid "{0} w"
msgstr "{0} w"

#: frontend/src/utils/index.js:185
msgid "{0} weeks ago"
msgstr "{0} veckor sedan"

#: frontend/src/utils/index.js:132
msgid "{0} y"
msgstr "{0} y"

#: frontend/src/utils/index.js:193
msgid "{0} years ago"
msgstr "{0} år sedan"

#: frontend/src/data/script.js:326
msgid "⚠️ Avoid using \"trigger\" as a field name — it conflicts with the built-in trigger() method."
msgstr "⚠️ Undvik att använda \"trigger\" som fältnamn - det krockar med den inbyggd trigger() metod."

#: frontend/src/data/script.js:338
msgid "⚠️ Method \"{0}\" not found in class."
msgstr "⚠️ Metod \"{0}\" hittades inte i klassen."

#: frontend/src/data/script.js:83
msgid "⚠️ No class found for doctype: {0}, it is mandatory to have a class for the parent doctype. it can be empty, but it should be present."
msgstr "⚠️ Ingen klass hittades för doctype: {0}, det erfordras att ha klass för överordnad doctype. Kan vara tom, men den ska finnas."

#: frontend/src/data/script.js:180
msgid "⚠️ No data found for parent field: {0}"
msgstr "⚠️ Ingen data hittades för överordnad fält: {0}"

#: frontend/src/data/script.js:188
msgid "⚠️ No row found for idx: {0} in parent field: {1}"
msgstr "⚠️ Ingen rad hittades för idx: {0} i överordnad fält: {1}"

