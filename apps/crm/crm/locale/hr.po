msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-11-09 09:35+0000\n"
"PO-Revision-Date: 2025-11-10 10:46\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Croatian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: hr\n"
"X-Crowdin-File: /[frappe.crm] develop/crm/locale/main.pot\n"
"X-Crowdin-File-ID: 97\n"
"Language: hr_HR\n"

#: frontend/src/components/ViewControls.vue:1172
msgid " (New)"
msgstr " (Novi)"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:58
msgid "(No title)"
msgstr "(Bez naslova)"

#: frontend/src/components/Modals/TaskModal.vue:87
#: frontend/src/components/Telephony/TaskPanel.vue:70
msgid "01/04/2024 11:30 PM"
msgstr "01.04.2024 23:30"

#: frontend/src/utils/index.js:172
msgid "1 hour ago"
msgstr "prije 1 sat"

#: frontend/src/composables/event.js:163
msgid "1 hr"
msgstr "1 sat"

#: frontend/src/utils/index.js:168
msgid "1 minute ago"
msgstr "prije 1 minutu"

#: frontend/src/utils/index.js:187
msgid "1 month ago"
msgstr "prije 1 mjesec"

#: frontend/src/utils/index.js:183
msgid "1 week ago"
msgstr "prije 1 tjedan"

#: frontend/src/utils/index.js:191
msgid "1 year ago"
msgstr "prije 1 godinu"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1-10"
msgstr "1-10"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1000+"
msgstr "1000+"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "11-50"
msgstr "11-50"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "201-500"
msgstr "201-500"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "501-1000"
msgstr "501-1000"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "51-200"
msgstr "51-200"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>META</b>"
msgstr "<b>META</b>"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>SHORTCUTS</b>"
msgstr "<b>PREČACI</b>"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:95
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:95
msgid "<p>Dear {{ lead_name }},</p>\\n\\n<p>This is a reminder for the payment of {{ grand_total }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappé</p>"
msgstr "<p>Poštovani {{ lead_name }},</p>\\n\\n<p>Ovo je podsjetnik za plaćanje {{ grand_total }}.</p>\\n\\n<p>Hvala,</p>\\n<p>Frappé</p>"

#. Header text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<span class=\"h5\"><b>PORTAL</b></span>"
msgstr "<span class=\"h5\"><b>PORTAL</b></span>"

#: frontend/src/components/CommunicationArea.vue:79
msgid "@John, can you please check this?"
msgstr "@John, možeš li molim te provjeriti ovo?"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:103
msgid "A Lead requires either a person's name or an organization's name"
msgstr "Potencijalni Klijent zahtijeva ili ime osobe ili ime organizacije"

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.py:47
msgid "A lead sync source is already enabled for this Facebook Lead Form!"
msgstr ""

#: crm/templates/emails/helpdesk_invitation.html:5
msgid "A new account has been created for you at {0}"
msgstr "Za vas je stvoren novi račun na {0}"

#. Label of the api_key (Data) field in DocType 'CRM Exotel Settings'
#. Label of the api_key (Data) field in DocType 'CRM Twilio Settings'
#. Label of the api_key (Data) field in DocType 'ERPNext CRM Settings'
#. Label of the api_key (Data) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "API Key"
msgstr "API Ključ"

#: frontend/src/components/Settings/emailConfig.js:179
msgid "API Key is required"
msgstr "API Ključ je obavezan"

#. Label of the api_secret (Password) field in DocType 'CRM Twilio Settings'
#. Label of the api_secret (Password) field in DocType 'ERPNext CRM Settings'
#. Label of the api_secret (Password) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "API Secret"
msgstr "API Tajna"

#. Label of the api_token (Password) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "API Token"
msgstr "API Token"

#: frontend/src/components/Telephony/TwilioCallUI.vue:88
msgid "Accept"
msgstr "Prihvati"

#: crm/fcrm/doctype/crm_invitation/crm_invitation.js:7
msgid "Accept Invitation"
msgstr "Prihvati Pozivnicu"

#: frontend/src/components/Telephony/TwilioCallUI.vue:155
msgid "Accept call"
msgstr "Prihvati poziv"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted"
msgstr "Prihvaćeno"

#. Label of the accepted_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted At"
msgstr "Prihvaćeno"

#. Label of the access_key (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Access Key"
msgstr "Pristupni Ključ"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:156
msgid "Access Key is required for Service Provider: {0}"
msgstr "Pristupni ključ je potreban za davaoca usluga: {0}"

#. Label of the access_token (Small Text) field in DocType 'Facebook Page'
#. Label of the access_token (Password) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:87
#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:28
msgid "Access Token"
msgstr "Pristupni Token"

#: frontend/src/components/Settings/CurrencySettings.vue:90
msgid "Access key"
msgstr "Pristupni ključ"

#: frontend/src/components/Settings/CurrencySettings.vue:94
msgid "Access key for Exchangerate Host. Required for fetching exchange rates."
msgstr "Pristupni ključ za Pružatelja Usluge Tečaja. Potreban za preuzimnje tečajnih lista."

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:133
msgid "Access token is required"
msgstr ""

#. Label of the account_id (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Account ID"
msgstr "Račun"

#: frontend/src/components/Settings/emailConfig.js:13
msgid "Account Name"
msgstr "Naziv Računa"

#. Label of the account_sid (Data) field in DocType 'CRM Exotel Settings'
#. Label of the account_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Account SID"
msgstr "SID Računa"

#: frontend/src/components/Settings/emailConfig.js:165
msgid "Account name is required"
msgstr "Naziv računa je obavezan"

#: frontend/src/components/CustomActions.vue:69
#: frontend/src/components/ViewControls.vue:683
#: frontend/src/components/ViewControls.vue:1064
msgid "Actions"
msgstr "Radnje"

#: frontend/src/pages/Deal.vue:537 frontend/src/pages/Lead.vue:384
#: frontend/src/pages/MobileDeal.vue:430 frontend/src/pages/MobileLead.vue:337
msgid "Activity"
msgstr "Aktivnost"

#: frontend/src/components/Dashboard/AddChartModal.vue:41
#: frontend/src/components/Modals/AddExistingUserModal.vue:55
msgid "Add"
msgstr "Dodaj"

#: frontend/src/components/Settings/EmailAccountList.vue:19
msgid "Add Account"
msgstr "Dodaj Račun"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:9
msgid "Add Assignee"
msgstr "Dodaj Dodijeljenog"

#: frontend/src/components/ColumnSettings.vue:68
#: frontend/src/components/Kanban/KanbanView.vue:157
msgid "Add Column"
msgstr "Dodaj Stupac"

#: frontend/src/components/Modals/AddExistingUserModal.vue:4
#: frontend/src/components/Settings/Users.vue:21
msgid "Add Existing User"
msgstr "Dodaj Postojećeg Korisnika"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:57
#: frontend/src/components/FieldLayoutEditor.vue:172
#: frontend/src/components/Kanban/KanbanSettings.vue:80
#: frontend/src/components/SidePanelLayoutEditor.vue:97
msgid "Add Field"
msgstr "Dodaj Polje"

#: frontend/src/components/Filter.vue:136
msgid "Add Filter"
msgstr "Dodaj Filter"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:273
msgid "Add Lead or Deal"
msgstr "Dodaj Potencijalnog Klijenta ili Ponudu"

#: frontend/src/components/Controls/Grid.vue:334
msgid "Add Row"
msgstr "Dodaj Red"

#: frontend/src/components/FieldLayoutEditor.vue:197
#: frontend/src/components/SidePanelLayoutEditor.vue:127
msgid "Add Section"
msgstr "Dodaj Odjeljak"

#: frontend/src/components/SortBy.vue:142
msgid "Add Sort"
msgstr "Dodaj Sortiranje"

#: frontend/src/components/FieldLayoutEditor.vue:62
msgid "Add Tab"
msgstr "Dodaj Karticu"

#. Label of the add_weekly_holidays_section (Section Break) field in DocType
#. 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add Weekly Holidays"
msgstr "Dodaj Tjedne Praznike"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:20
msgid "Add a condition"
msgstr "Dodaj uvjet"

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:24
msgid "Add a name for your source"
msgstr ""

#: frontend/src/components/Telephony/ExotelCallUI.vue:183
#: frontend/src/components/Telephony/TwilioCallUI.vue:57
msgid "Add a note"
msgstr "Dodaj bilješku"

#: frontend/src/components/Telephony/ExotelCallUI.vue:191
msgid "Add a task"
msgstr "Dodaj zadatak"

#: frontend/src/components/Dashboard/AddChartModal.vue:4
msgid "Add chart"
msgstr "Dodaj grafikon"

#: frontend/src/components/FieldLayoutEditor.vue:420
msgid "Add column"
msgstr "Dodaj Stupac"

#: frontend/src/components/ConditionsFilter/CFConditions.vue:23
#: frontend/src/components/ConditionsFilter/CFConditions.vue:82
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:28
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:64
msgid "Add condition"
msgstr "Dodaj uvjet"

#: frontend/src/components/ConditionsFilter/CFConditions.vue:91
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:70
msgid "Add condition group"
msgstr "Dodaj grupu uvjeta"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:325
msgid "Add description"
msgstr "Dodaj Opis"

#: frontend/src/components/Modals/EventModal.vue:142
msgid "Add description."
msgstr "Dodaj Opis."

#: frontend/src/components/Telephony/TaskPanel.vue:17
msgid "Add description..."
msgstr "Dodaj Opis..."

#: frontend/src/components/Modals/AddExistingUserModal.vue:12
msgid "Add existing system users to this CRM. Assign them a role to grant access with their current credentials."
msgstr "Dodaj postojeće korisnike sustava u ovaj CRM. Dodijelite im ulogu kako biste im odobrili pristup s njihovim trenutnim akreditivima."

#: frontend/src/components/ViewControls.vue:107
msgid "Add filter"
msgstr "Dodaj filter"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Add note"
msgstr "Dodaj bilješku"

#: frontend/src/pages/Welcome.vue:24
msgid "Add sample data"
msgstr "Dodaj primjere podataka"

#. Description of the 'Icon' (Code) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Add svg code or use feather icons e.g 'settings'"
msgstr "Dodajte svg kod ili koristite pernate ikone, npr. 'postavke'"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Add task"
msgstr "Dodaj zadatak"

#. Label of the add_to_holidays (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add to Holidays"
msgstr "Dodaj Praznicima"

#: frontend/src/components/Layouts/AppSidebar.vue:439
msgid "Add your first comment"
msgstr "Dodaj komentar"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:11
msgid "Add, edit, and manage email templates for various CRM communications"
msgstr "Dodaj, uredi i upravljaj predlošcima e-pošte za različite CRM komunikacije"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:12
msgid "Add, edit, and manage sources for automatic lead syncing to your CRM"
msgstr ""

#. Label of the address (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Address"
msgstr "Adresa"

#: frontend/src/components/Modals/AddExistingUserModal.vue:94
#: frontend/src/components/Settings/InviteUserPage.vue:158
#: frontend/src/components/Settings/InviteUserPage.vue:165
#: frontend/src/components/Settings/Users.vue:86
#: frontend/src/components/Settings/Users.vue:126
#: frontend/src/components/Settings/Users.vue:192
#: frontend/src/components/Settings/Users.vue:242
#: frontend/src/components/Settings/Users.vue:245
msgid "Admin"
msgstr "Administrator"

#: crm/integrations/twilio/twilio_handler.py:144
msgid "Agent is unavailable to take the call, please call after some time."
msgstr "Agent nije dostupan da odgovori na poziv, nazovite nakon nekog vremena."

#. Name of a role
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:76
#: frontend/src/components/Settings/Users.vue:85
msgid "All"
msgstr "Sve"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:191
#: frontend/src/components/Calendar/CalendarEventPanel.vue:641
#: frontend/src/components/Modals/EventModal.vue:71
#: frontend/src/composables/event.js:122
msgid "All day"
msgstr "Cijeli dan"

#. Label of the amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
#: frontend/src/pages/Contact.vue:507 frontend/src/pages/MobileContact.vue:507
#: frontend/src/pages/MobileOrganization.vue:451
#: frontend/src/pages/Organization.vue:481
msgid "Amount"
msgstr "Iznos"

#. Description of the 'Net Amount' (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Amount after discount"
msgstr "Iznos nakon popusta"

#: frontend/src/data/script.js:50 frontend/src/data/script.js:51
msgid "An error occurred"
msgstr "Došlo je do pogreške"

#: frontend/src/data/document.js:66
msgid "An error occurred while updating the document"
msgstr "Došlo je do pogreške prilikom ažuriranja dokumenta"

#. Description of the 'Favicon' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org]"
msgstr "Datoteka ikone s nastavkom .ico. Trebala bi biti 16 x 16 px. Generirano pomoću generatora favicona. [favicon-generator.org]"

#. Description of the 'Logo' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An image with 1:1 & 2:1 ratio is preferred"
msgstr "Poželjna je slika omjera 1:1 i 2:1"

#: frontend/src/components/Filter.vue:43 frontend/src/components/Filter.vue:81
msgid "And"
msgstr "I"

#. Label of the annual_revenue (Currency) field in DocType 'CRM Deal'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Lead'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Annual Revenue"
msgstr "Godišnji Prihod"

#: frontend/src/components/Modals/DealModal.vue:200
#: frontend/src/components/Modals/LeadModal.vue:141
msgid "Annual Revenue should be a number"
msgstr "Godišnji Prihod trebao bi biti broj"

#: frontend/src/components/Settings/BrandSettings.vue:55
msgid "Appears in the left sidebar. Recommended size is 32x32 px in PNG or SVG"
msgstr "Pojavljuje se na lijevoj bočnoj traci. Preporučena veličina je 32x32 px u PNG ili SVG"

#: frontend/src/components/Settings/BrandSettings.vue:90
msgid "Appears next to the title in your browser tab. Recommended size is 32x32 px in PNG or ICO"
msgstr "Pojavljuje se pored naslova u kartici vašeg preglednika. Preporučena veličina je 32x32 px u PNG ili ICO formatu"

#: frontend/src/components/Kanban/KanbanSettings.vue:101
#: frontend/src/components/Kanban/KanbanView.vue:46
msgid "Apply"
msgstr "Primjeni"

#. Label of the apply_on (Link) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Apply On"
msgstr "Primijeni na"

#. Label of the view (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Apply To"
msgstr "Primijeni na"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:125
msgid "Apply on"
msgstr "Primijeni"

#: frontend/src/components/Apps.vue:19
msgid "Apps"
msgstr "Aplikacije"

#: frontend/src/components/Activities/AttachmentArea.vue:128
msgid "Are you sure you want to delete this attachment?"
msgstr "Jeste li sigurni da želite izbrisati ovaj privitak?"

#: frontend/src/pages/MobileContact.vue:260
msgid "Are you sure you want to delete this contact?"
msgstr "Jeste li sigurni da želite izbrisati ovaj kontakt?"

#: frontend/src/components/Modals/EventModal.vue:408
#: frontend/src/pages/Calendar.vue:300
msgid "Are you sure you want to delete this event?"
msgstr "Jeste li sigurni da želite izbrisati ovaj događaj?"

#: frontend/src/pages/MobileOrganization.vue:261
msgid "Are you sure you want to delete this organization?"
msgstr "Jeste li sigurni da želite izbrisati ovu organizaciju?"

#: frontend/src/components/Activities/TaskArea.vue:60
msgid "Are you sure you want to delete this task?"
msgstr "Jeste li sigurni da želite izbrisati ovaj zadatak?"

#: frontend/src/components/DeleteLinkedDocModal.vue:231
msgid "Are you sure you want to delete {0} linked item(s)?"
msgstr "Jeste li sigurni da želite izbrisati {0} povezanih stavki?"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:615
msgid "Are you sure you want to discard unsaved changes to this event?"
msgstr "Jeste li sigurni da želite odbaciti nespremljene promjene ovog događaja?"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:576
msgid "Are you sure you want to go back? Unsaved changes will be lost."
msgstr "Jeste li sigurni da se želite vratiti? Nespremljene promjene bit će izgubljene."

#: frontend/src/composables/frappecloud.js:24
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "Jeste li sigurni da se želite prijaviti na svoju Frappe Cloud Nadzornu Tablu?"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:9
msgid "Are you sure you want to reset 'Create Quotation from CRM Deal' Form Script?"
msgstr "Jeste li sigurni da želite poništiti Skriptu Obrasca \"Stvori ponudu iz Posla\"?"

#: frontend/src/components/Settings/CurrencySettings.vue:165
msgid "Are you sure you want to set the currency as {0}? This cannot be changed later."
msgstr "Jeste li sigurni da želite postaviti valutu kao {0}? To se kasnije ne može promijeniti."

#: frontend/src/components/DeleteLinkedDocModal.vue:244
msgid "Are you sure you want to unlink {0} linked item(s)?"
msgstr "Jeste li sigurni da želite prekinuti vezu {0} povezanih stavki?"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:193
msgid "Ask your manager to set up the Exchange Rate Provider, as default provider does not support currency conversion for {0} to {1}."
msgstr "Zamolite odgovornog da postavi pružatelja tečaja, jer zadani pružatelj ne podržava pretvorbu valuta iz {0} u {1}."

#: frontend/src/components/ListBulkActions.vue:186
#: frontend/src/components/Modals/AssignmentModal.vue:4
msgid "Assign To"
msgstr "Dodijeli"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:435
msgid "Assign condition is required"
msgstr "Uvjet dodjeljivanja je obavezan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:438
msgid "Assign conditions are invalid"
msgstr "Uvjeti dodjeljivanja nisu važeći"

#: frontend/src/components/AssignTo.vue:11
#: frontend/src/components/AssignToBody.vue:5
msgid "Assign to"
msgstr "Dodijeli"

#: frontend/src/components/AssignToBody.vue:63
msgid "Assign to me"
msgstr "Dodijeli meni"

#. Label of the assigned_to (Link) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Assigned To"
msgstr "Dodijeljeno"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:5
msgid "Assignee Rules"
msgstr "Pravila Dodjele"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:81
msgid "Assignees"
msgstr "Dodijeljeni"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Assignment"
msgstr "Dodjela"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Assignment Rule"
msgstr "Pravilo Dodjele"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:302
msgid "Assignment Schedule"
msgstr "Raspored dodjele"

#: frontend/src/components/ListBulkActions.vue:154
msgid "Assignment cleared successfully"
msgstr "Dodjela je uspješno izbrisana"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:145
msgid "Assignment condition"
msgstr "Uvjet dodjele"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:471
msgid "Assignment days are required"
msgstr "Obavezni su dani dodjele"

#: frontend/src/components/Layouts/AppSidebar.vue:582
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:19
msgid "Assignment rule"
msgstr "Pravilo dodjeljivanja"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:667
msgid "Assignment rule created"
msgstr "Pravilo dodjele je stvoreno"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:111
msgid "Assignment rule deleted"
msgstr "Pravilo dodjele je izbrisano"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:156
msgid "Assignment rule duplicated"
msgstr "Pravilo dodjele je duplicirano"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:749
msgid "Assignment rule updated"
msgstr "Pravilo dodjele je ažurirano"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:188
msgid "Assignment rule {0} updated"
msgstr "Pravilo dodjele {0} ažurirano"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:7
#: frontend/src/components/Settings/Settings.vue:164
msgid "Assignment rules"
msgstr "Pravila dodjeljivanja"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:11
msgid "Assignment rules automatically assign lead/deal to the right sales user based on predefined conditions"
msgstr "Pravila dodjele automatski dodjeljuju potencijalnog klijenta/posao pravom prodajnom korisniku na temelju unaprijed definiranih uvjeta"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:173
msgid "At least one field is required"
msgstr "Barem jedno polje je obavezno"

#: frontend/src/components/FilesUploader/FilesUploader.vue:5
#: frontend/src/components/FilesUploader/FilesUploader.vue:73
msgid "Attach"
msgstr "Priloži"

#: frontend/src/components/CommentBox.vue:65
#: frontend/src/components/EmailEditor.vue:146 frontend/src/pages/Deal.vue:105
#: frontend/src/pages/Lead.vue:151
msgid "Attach a file"
msgstr "Priloži datoteku"

#: frontend/src/pages/Deal.vue:577 frontend/src/pages/Lead.vue:424
#: frontend/src/pages/MobileDeal.vue:466 frontend/src/pages/MobileLead.vue:372
msgid "Attachments"
msgstr "Prilozi"

#: frontend/src/components/Modals/EventModal.vue:120
msgid "Attendees"
msgstr "Učesnici"

#. Label of the auth_token (Password) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Auth Token"
msgstr "Auth Token"

#. Label of the auto_update_expected_deal_value (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Auto Update Expected Deal Value"
msgstr "Automatsko ažuriranje očekivane vrijednosti ponude"

#: frontend/src/components/Settings/ForecastingSettings.vue:42
msgid "Auto update expected deal value"
msgstr "Automatsko ažuriranje očekivane vrijednosti ponude"

#: frontend/src/components/Settings/ForecastingSettings.vue:88
msgid "Auto update of expected deal value disabled"
msgstr "Automatsko ažuriranje očekivane vrijednosti posla onemogućeno"

#: frontend/src/components/Settings/ForecastingSettings.vue:87
msgid "Auto update of expected deal value enabled"
msgstr "Automatsko ažuriranje očekivane vrijednosti posla omogućeno"

#. Description of the 'Auto Update Expected Deal Value' (Check) field in
#. DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/ForecastingSettings.vue:46
msgid "Automatically update \"Expected Deal Value\" based on the total value of associated products in a deal"
msgstr "Automatski ažuriraj \"Očekivanu vrijednost ponude\" na temelju ukupne vrijednosti povezanih proizvoda u ponudi"

#: frontend/src/components/Settings/Settings.vue:161
msgid "Automation & Rules"
msgstr "Automatizacija & Pravila"

#: crm/api/dashboard.py:250
msgid "Average deal value of non won/lost deals"
msgstr "Prosječna vrijednost izgubljenih poslova"

#: crm/api/dashboard.py:429
msgid "Average deal value of ongoing & won deals"
msgstr "Prosječna vrijednost tekućih i dobivenih poslova"

#: crm/api/dashboard.py:370
msgid "Average deal value of won deals"
msgstr "Prosječna vrijednost dobivenih poslova"

#: crm/api/dashboard.py:534
msgid "Average time taken from deal creation to deal closure"
msgstr "Prosječno vrijeme od kreiranja posla do njegovog zaključenja"

#: crm/api/dashboard.py:481
msgid "Average time taken from lead creation to deal closure"
msgstr "Prosječno vrijeme potrebno od kreiranja potencijalnog klijenta do zaključenja posla"

#: frontend/src/components/Dashboard/AddChartModal.vue:81
msgid "Avg deal value"
msgstr "Prosječna vrijednost posla"

#: frontend/src/components/Dashboard/AddChartModal.vue:78
msgid "Avg ongoing deal value"
msgstr "Prosječna vrijednost tekućeg posla"

#: frontend/src/components/Dashboard/AddChartModal.vue:87
msgid "Avg time to close a deal"
msgstr "Prosječno vrijeme potrebno za sklapanje posla"

#: frontend/src/components/Dashboard/AddChartModal.vue:83
msgid "Avg time to close a lead"
msgstr "Prosječno vrijeme potrebno za zatvaranje potencijalnog klijenta"

#: frontend/src/components/Dashboard/AddChartModal.vue:80
msgid "Avg won deal value"
msgstr "Prosječna vrijednost dobivenog posla"

#: crm/api/dashboard.py:428
msgid "Avg. deal value"
msgstr "Prosječna vrijednost posla"

#: crm/api/dashboard.py:249
msgid "Avg. ongoing deal value"
msgstr "Prosječna vrijednost tekućeg posla"

#: crm/api/dashboard.py:533
msgid "Avg. time to close a deal"
msgstr "Prosječno vrijeme potrebno za sklapanje posla"

#: crm/api/dashboard.py:480
msgid "Avg. time to close a lead"
msgstr "Prosječno vrijeme potrebno za zatvaranje potencijalnog klijenta"

#: crm/api/dashboard.py:369
msgid "Avg. won deal value"
msgstr "Prosječna vrijednost dobivenog posla"

#: frontend/src/components/Dashboard/AddChartModal.vue:26
#: frontend/src/components/Dashboard/AddChartModal.vue:70
msgid "Axis chart"
msgstr "Osni grafikon"

#: frontend/src/components/Activities/EmailArea.vue:62
#: frontend/src/components/EmailEditor.vue:45
#: frontend/src/components/EmailEditor.vue:71
msgid "BCC"
msgstr "BCC"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
#: frontend/src/components/Settings/EmailAdd.vue:79
#: frontend/src/components/Settings/EmailEdit.vue:67
msgid "Back"
msgstr "Nazad"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
msgid "Back to file upload"
msgstr "Natrag na učitavanje datoteke"

#. Label of the background_sync_frequency (Select) field in DocType 'Lead Sync
#. Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:80
msgid "Background Sync Frequency"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Backlog"
msgstr "Zaostatak"

#: frontend/src/components/Filter.vue:350
msgid "Between"
msgstr "Između"

#: frontend/src/components/Settings/Settings.vue:119
msgid "Brand Settings"
msgstr "Postavke marke"

#: frontend/src/components/Settings/BrandSettings.vue:52
msgid "Brand logo"
msgstr "Logotip marke"

#: frontend/src/components/Settings/BrandSettings.vue:32
msgid "Brand name"
msgstr "Naziv robne marke"

#: frontend/src/components/Settings/BrandSettings.vue:7
msgid "Brand settings"
msgstr "Postavke robne marke"

#. Label of the branding_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Branding"
msgstr "Brendiranje"

#: frontend/src/components/Modals/EditValueModal.vue:2
msgid "Bulk Edit"
msgstr "Grupno Uređivanje"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Busy"
msgstr "Zauzeto"

#: frontend/src/components/Activities/EmailArea.vue:57
#: frontend/src/components/EmailEditor.vue:35
#: frontend/src/components/EmailEditor.vue:57
msgid "CC"
msgstr "CC"

#. Name of a DocType
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "CRM Call Log"
msgstr "Zapisnik Poziva"

#. Name of a DocType
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
msgid "CRM Communication Status"
msgstr "Status Konverzacije"

#. Name of a DocType
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
msgid "CRM Contacts"
msgstr "Kontakti"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/pages/Dashboard.vue:304
msgid "CRM Dashboard"
msgstr "Nadzorna Ploča"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "CRM Deal"
msgstr "Posao"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "CRM Deal Status"
msgstr "Status Posla"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "CRM Dropdown Item"
msgstr "Padajuća Stavka"

#. Name of a DocType
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "CRM Exotel Settings"
msgstr "Exotel Postavke"

#. Name of a DocType
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "CRM Fields Layout"
msgstr "Izgled Polja"

#. Name of a DocType
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "CRM Form Script"
msgstr "Skripta Obrasca"

#. Name of a DocType
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "CRM Global Settings"
msgstr "Globalne Postavke"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "CRM Holiday"
msgstr "Praznik"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "CRM Holiday List"
msgstr "Lista Praznika"

#. Name of a DocType
#: crm/fcrm/doctype/crm_industry/crm_industry.json
msgid "CRM Industry"
msgstr "Industrija"

#. Name of a DocType
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "CRM Invitation"
msgstr "Pozivnica"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "CRM Lead"
msgstr "Potencijalni Klijent"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "CRM Lead Source"
msgstr "Izvor Potencijalnog Klijenta"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "CRM Lead Status"
msgstr "Status Potencijalnog Klijenta"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "CRM Lost Reason"
msgstr "Razlog Gubitka"

#. Name of a DocType
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "CRM Notification"
msgstr "Obavjest"

#. Name of a DocType
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "CRM Organization"
msgstr "Organizacija"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "CRM Portal Page"
msgstr "Stranica Portala"

#. Name of a DocType
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "CRM Product"
msgstr "Stavka"

#. Name of a DocType
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "CRM Products"
msgstr "Stavke"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "CRM Service Day"
msgstr "Dan Usluge"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "CRM Service Level Agreement"
msgstr "Ugovor Razine Usluge"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "CRM Service Level Priority"
msgstr "Prioritet Nivoa Usluge"

#. Name of a DocType
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "CRM Status Change Log"
msgstr "Zapisnik Promjena Statusa"

#. Name of a DocType
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "CRM Task"
msgstr "Zadatak"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "CRM Telephony Agent"
msgstr "Telefonski Agent"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "CRM Telephony Phone"
msgstr "Telefonija Telefon"

#. Name of a DocType
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "CRM Territory"
msgstr "Distrikt"

#. Name of a DocType
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "CRM Twilio Settings"
msgstr "Twilio Postavke"

#. Name of a DocType
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "CRM View Settings"
msgstr "Postavke Prikaza"

#: frontend/src/components/Settings/CurrencySettings.vue:35
msgid "CRM currency for all monetary values. Once set, cannot be edited."
msgstr "Valuta za sve novčane vrijednosti. Nakon postavljanja, ne može se uređivati."

#: frontend/src/components/ViewControls.vue:272
msgid "CSV"
msgstr "CSV"

#: frontend/src/components/Modals/CallLogDetailModal.vue:8
msgid "Call Details"
msgstr "Detalji Poziva"

#. Label of the receiver (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call Received By"
msgstr "Poziv Primljen Od"

#. Description of the 'Duration' (Duration) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call duration in seconds"
msgstr "Trajanje poziva u sekundama"

#: frontend/src/components/Layouts/AppSidebar.vue:556
msgid "Call log"
msgstr "Zapisnik poziva"

#: frontend/src/components/Telephony/CallUI.vue:10
msgid "Call using {0}"
msgstr "Nazovi koristeći {0}"

#: frontend/src/components/Modals/EventModal.vue:63
#: frontend/src/components/Modals/NoteModal.vue:28
#: frontend/src/components/Modals/TaskModal.vue:30
msgid "Call with John Doe"
msgstr "Poziv sa John Doeom"

#. Label of the caller (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Caller"
msgstr "Pozivatelj"

#: frontend/src/components/Telephony/CallUI.vue:27
msgid "Calling Medium"
msgstr "Pozivni Medij"

#: frontend/src/components/Telephony/TwilioCallUI.vue:40
#: frontend/src/components/Telephony/TwilioCallUI.vue:140
msgid "Calling..."
msgstr "Poziv u toku..."

#: frontend/src/pages/Deal.vue:562 frontend/src/pages/Lead.vue:409
#: frontend/src/pages/MobileDeal.vue:450 frontend/src/pages/MobileLead.vue:357
msgid "Calls"
msgstr "Pozivi"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:51
msgid "Camera"
msgstr "Kamera"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:81
#: frontend/src/components/Calendar/CalendarEventPanel.vue:620
#: frontend/src/components/ColumnSettings.vue:123
#: frontend/src/components/Dashboard/AddChartModal.vue:40
#: frontend/src/components/DeleteLinkedDocModal.vue:115
#: frontend/src/components/Modals/AssignmentModal.vue:69
#: frontend/src/components/Modals/EventModal.vue:151
#: frontend/src/components/Modals/LostReasonModal.vue:43
#: frontend/src/components/Telephony/TwilioCallUI.vue:73
#: frontend/src/components/ViewControls.vue:57
#: frontend/src/components/ViewControls.vue:154
#: frontend/src/pages/Dashboard.vue:32
msgid "Cancel"
msgstr "Otkaži"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Canceled"
msgstr "Otkazano"

#: frontend/src/components/Settings/Users.vue:124
msgid "Cannot change role of user with Admin access"
msgstr "Nije moguće promijeniti ulogu korisnika s administratorskim pristupom"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:34
msgid "Cannot delete standard items {0}"
msgstr "Nije moguće izbrisati standardne stavke {0}"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:171
msgid "Cannot enable rule without adding users in it"
msgstr "Nije moguće omogućiti pravilo bez dodavanja korisnika"

#: frontend/src/components/FilesUploader/FilesUploader.vue:91
msgid "Capture"
msgstr "Uhvati"

#: frontend/src/components/Layouts/AppSidebar.vue:561
msgid "Capturing leads"
msgstr "Prikupljanje Potencijalnih Klijenata"

#. Label of the category (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Category"
msgstr "Kategorija"

#: frontend/src/components/Controls/ImageUploader.vue:19
#: frontend/src/components/Layouts/AppSidebar.vue:490
msgid "Change"
msgstr "Promjeni"

#: frontend/src/components/Modals/ChangePasswordModal.vue:2
#: frontend/src/components/Settings/ProfileSettings.vue:65
msgid "Change Password"
msgstr "Promjeni lozinku"

#: frontend/src/components/Layouts/AppSidebar.vue:481
#: frontend/src/components/Layouts/AppSidebar.vue:489
msgid "Change deal status"
msgstr "Promijeni status posla"

#: frontend/src/components/Settings/ProfileSettings.vue:26
#: frontend/src/pages/Contact.vue:47 frontend/src/pages/Lead.vue:88
#: frontend/src/pages/MobileContact.vue:37
#: frontend/src/pages/MobileOrganization.vue:37
#: frontend/src/pages/Organization.vue:47
msgid "Change image"
msgstr "Promijeni Sliku"

#: frontend/src/components/Activities/TaskArea.vue:45
msgid "Change status"
msgstr "Promijeni status"

#: frontend/src/pages/Dashboard.vue:22
msgid "Chart"
msgstr "Grafikon"

#: frontend/src/components/Dashboard/AddChartModal.vue:12
msgid "Chart Type"
msgstr "Tip Grafikona"

#: frontend/src/components/Modals/ConvertToDealModal.vue:29
#: frontend/src/components/Modals/ConvertToDealModal.vue:55
#: frontend/src/pages/MobileLead.vue:122 frontend/src/pages/MobileLead.vue:149
msgid "Choose Existing"
msgstr "Odaberi Postojeći"

#: frontend/src/components/Modals/DealModal.vue:44
msgid "Choose Existing Contact"
msgstr "Odaberi Postojeći Kontakt"

#: frontend/src/components/Modals/DealModal.vue:37
msgid "Choose Existing Organization"
msgstr "Odaberi Postojeću Organizaciju"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:9
msgid "Choose how {0} are assigned among salespeople."
msgstr "Odaberi kako se {0} dodjeljuju među prodavačima."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:306
msgid "Choose the days of the week when this rule should be active."
msgstr "Odaberite dane u tjednu kada bi ovo pravilo trebalo biti aktivno."

#: frontend/src/components/Settings/EmailAdd.vue:9
msgid "Choose the email service provider you want to configure."
msgstr "Odaberite davatelja usluga e-pošte kojeg želite konfigurirati."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:229
msgid "Choose which {0} are affected by this un-assignment rule."
msgstr "Odaberite na koje {0} utječe ovo pravilo poništavanja dodjele."

#: frontend/src/components/Controls/Link.vue:59
msgid "Clear"
msgstr "Očisti"

#: frontend/src/components/ListBulkActions.vue:136
#: frontend/src/components/ListBulkActions.vue:144
#: frontend/src/components/ListBulkActions.vue:190
msgid "Clear Assignment"
msgstr "Očisti Dodjelu"

#: frontend/src/components/SortBy.vue:153
msgid "Clear Sort"
msgstr "Očisti Sortiranje"

#. Label of the clear_table (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Clear Table"
msgstr "Očisti Tabelu"

#: frontend/src/components/Filter.vue:21 frontend/src/components/Filter.vue:146
msgid "Clear all Filter"
msgstr "Očisti sve Filtere"

#: crm/templates/emails/helpdesk_invitation.html:7
msgid "Click on the link below to complete your registration and set a new password"
msgstr "Klikni na vezu ispod kako biste dovršili registraciju i postavili novu lozinku"

#: frontend/src/components/Notifications.vue:26
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:54
msgid "Close"
msgstr "Zatvori"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:38
msgid "Close panel"
msgstr "Zatvori ploču"

#. Label of the closed_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Closed Date"
msgstr "Datum zaključenja"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Collapse"
msgstr "Sklopi"

#: frontend/src/components/FieldLayoutEditor.vue:344
msgid "Collapsible"
msgstr "Sklopivo"

#. Label of the color (Select) field in DocType 'CRM Deal Status'
#. Label of the color (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Color"
msgstr "Boja"

#: frontend/src/components/FieldLayoutEditor.vue:417
msgid "Column"
msgstr "Kolona"

#. Label of the column_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:12
msgid "Column Field"
msgstr "Polje Stupca"

#. Label of the columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:4
msgid "Columns"
msgstr "Kolone"

#. Label of the comment (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/CommentBox.vue:78
#: frontend/src/components/CommunicationArea.vue:16
#: frontend/src/components/Layouts/AppSidebar.vue:579
msgid "Comment"
msgstr "Komentar"

#: frontend/src/pages/Deal.vue:547 frontend/src/pages/Lead.vue:394
#: frontend/src/pages/MobileDeal.vue:440 frontend/src/pages/MobileLead.vue:347
msgid "Comments"
msgstr "Komentari"

#: crm/api/dashboard.py:920
msgid "Common reasons for losing deals"
msgstr "Uobičajeni razlozi za gubitak poslova"

#. Label of the communication_status (Link) field in DocType 'CRM Deal'
#. Label of the communication_status (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Communication Status"
msgstr "Status Konverzacije"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Communication Statuses"
msgstr "Statusi Konverzacije"

#. Label of the erpnext_company (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Company in ERPNext Site"
msgstr "Tvrtka u Sistemu"

#: crm/templates/emails/helpdesk_invitation.html:10
msgid "Complete Registration"
msgstr "Završi Registraciju"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Completed"
msgstr "Završeno"

#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Computer"
msgstr "Računar"

#. Label of the condition (Code) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Condition"
msgstr "Uslov"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:188
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:272
msgid "Conditions for this rule were created from"
msgstr "Uvjeti za ovo pravilo stvoreni su iz"

#: frontend/src/components/Settings/HomeActions.vue:10
msgid "Configure actions that appear on the home dropdown"
msgstr "Konfigurirajte radnje koje se prikazuju na početnom padajućem izborniku"

#: frontend/src/components/Settings/ForecastingSettings.vue:9
msgid "Configure forecasting feature to help predict sales performance and growth"
msgstr "Konfigurirajte značajku predviđanja kako biste predvidjeli uspješnost i rast prodaje"

#: frontend/src/components/Settings/TelephonySettings.vue:17
msgid "Configure telephony settings for your CRM"
msgstr "Konfiguriraj postavke telefonije"

#: frontend/src/components/Settings/CurrencySettings.vue:11
msgid "Configure the currency and exchange rate provider for your CRM"
msgstr "Konfiguriraj pružatelja usluga valute i tečaja za Prodajnu Podršku"

#: frontend/src/components/Settings/CurrencySettings.vue:63
msgid "Configure the exchange rate provider for your CRM"
msgstr "Konfiguriši pružatelja tečaja za Podršku Prodaje"

#: frontend/src/components/Settings/BrandSettings.vue:10
msgid "Configure your brand name, logo, and favicon"
msgstr "Konfiguriraj naziv marke, logotip i favicon"

#: frontend/src/composables/frappecloud.js:29
msgid "Confirm"
msgstr "Potvrdi"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:139
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:239
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:209
msgid "Confirm Delete"
msgstr "Potvrdi Brisanje"

#: frontend/src/components/Modals/ChangePasswordModal.vue:18
msgid "Confirm Password"
msgstr "Potvrdi Lozinku"

#: frontend/src/components/Settings/Users.vue:228
msgid "Confirm Remove"
msgstr "Potvrdi Uklanjanje"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:609
msgid "Confirm overwrite"
msgstr "Potvrdi prepisivanje"

#: frontend/src/pages/Welcome.vue:35
msgid "Connect your email"
msgstr "Poveži e-poštu"

#. Label of the contact (Link) field in DocType 'CRM Contacts'
#. Label of the contact (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:552
#: frontend/src/components/Modals/ConvertToDealModal.vue:51
#: frontend/src/pages/MobileLead.vue:145
msgid "Contact"
msgstr "Kontakt"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:259
msgid "Contact Already Exists"
msgstr "Kontakt već postoji"

#: frontend/src/components/Modals/AboutModal.vue:77
msgid "Contact Support"
msgstr "Kontakt Podrške"

#: frontend/src/components/Modals/EditValueModal.vue:20
msgid "Contact Us"
msgstr "Kontaktirajte nas"

#: frontend/src/pages/Deal.vue:657 frontend/src/pages/MobileDeal.vue:544
msgid "Contact added"
msgstr "Kontakt je dodan"

#: frontend/src/pages/Deal.vue:647 frontend/src/pages/MobileDeal.vue:534
msgid "Contact already added"
msgstr "Kontakt je već dodan"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:258
msgid "Contact already exists with {0}"
msgstr "Kontakt već postoji s {0}"

#: frontend/src/pages/Contact.vue:287 frontend/src/pages/MobileContact.vue:252
msgid "Contact image updated"
msgstr "Slika kontakta ažurirana"

#: frontend/src/pages/Deal.vue:668 frontend/src/pages/MobileDeal.vue:555
msgid "Contact removed"
msgstr "Kontakt uklonjen"

#: frontend/src/pages/Contact.vue:432 frontend/src/pages/Contact.vue:445
#: frontend/src/pages/Contact.vue:458 frontend/src/pages/Contact.vue:468
#: frontend/src/pages/MobileContact.vue:432
#: frontend/src/pages/MobileContact.vue:445
#: frontend/src/pages/MobileContact.vue:458
#: frontend/src/pages/MobileContact.vue:468
msgid "Contact updated"
msgstr "Kontakt ažuriran"

#. Label of the contacts_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the contacts (Table) field in DocType 'CRM Deal'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Contact.vue:242 frontend/src/pages/MobileContact.vue:212
#: frontend/src/pages/MobileOrganization.vue:331
msgid "Contacts"
msgstr "Kontakti"

#. Label of the content (Text Editor) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:33
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:89
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:102
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:89
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:102
msgid "Content"
msgstr "Sadržaj"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:78
msgid "Content Type"
msgstr "Vrsta sadržaja"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:159
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:163
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:162
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:166
msgid "Content is required"
msgstr "Sadržaj je obavezan"

#: frontend/src/components/Layouts/AppSidebar.vue:380
#: frontend/src/components/ListBulkActions.vue:88
#: frontend/src/components/Modals/ConvertToDealModal.vue:83
#: frontend/src/pages/MobileLead.vue:54 frontend/src/pages/MobileLead.vue:108
msgid "Convert"
msgstr "Pretvori"

#: frontend/src/components/Layouts/AppSidebar.vue:371
#: frontend/src/components/Layouts/AppSidebar.vue:379
msgid "Convert lead to deal"
msgstr "Pretvori potencijalnog klijenta u posao"

#: frontend/src/components/ListBulkActions.vue:80
#: frontend/src/components/ListBulkActions.vue:197
#: frontend/src/components/Modals/ConvertToDealModal.vue:7
#: frontend/src/pages/Lead.vue:38 frontend/src/pages/MobileLead.vue:104
msgid "Convert to Deal"
msgstr "Pretvori u Posao"

#. Label of the converted (Check) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Converted"
msgstr "Pretvoreno"

#: frontend/src/components/ListBulkActions.vue:96
msgid "Converted successfully"
msgstr "Uspješno Pretvoreno"

#: frontend/src/utils/index.js:338
msgid "Copied to clipboard"
msgstr "Kopirano u međuspremnik"

#: crm/api/dashboard.py:626 crm/api/dashboard.py:763 crm/api/dashboard.py:824
#: crm/api/dashboard.py:927
msgid "Count"
msgstr "Broj"

#: frontend/src/components/Modals/AddressModal.vue:98
#: frontend/src/components/Modals/CallLogModal.vue:101
#: frontend/src/components/Modals/ContactModal.vue:40
#: frontend/src/components/Modals/CreateDocumentModal.vue:92
#: frontend/src/components/Modals/DealModal.vue:66
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:20
#: frontend/src/components/Modals/EventModal.vue:159
#: frontend/src/components/Modals/LeadModal.vue:37
#: frontend/src/components/Modals/NoteModal.vue:52
#: frontend/src/components/Modals/OrganizationModal.vue:41
#: frontend/src/components/Modals/TaskModal.vue:106
#: frontend/src/components/Modals/ViewModal.vue:43
#: frontend/src/components/Settings/EmailAdd.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:23
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:30
#: frontend/src/pages/Calendar.vue:10 frontend/src/pages/CallLogs.vue:13
#: frontend/src/pages/Contacts.vue:13 frontend/src/pages/Contacts.vue:60
#: frontend/src/pages/Deals.vue:13 frontend/src/pages/Deals.vue:236
#: frontend/src/pages/Leads.vue:13 frontend/src/pages/Leads.vue:262
#: frontend/src/pages/Notes.vue:9 frontend/src/pages/Notes.vue:96
#: frontend/src/pages/Organizations.vue:13
#: frontend/src/pages/Organizations.vue:60 frontend/src/pages/Tasks.vue:13
#: frontend/src/pages/Tasks.vue:186
msgid "Create"
msgstr "Kreiraj"

#: frontend/src/components/Modals/DealModal.vue:8
msgid "Create Deal"
msgstr "Kreiraj Posao"

#: frontend/src/components/Modals/LeadModal.vue:8
msgid "Create Lead"
msgstr "Kreiraj Potencijalnog Klijenta"

#: frontend/src/components/Controls/Link.vue:50
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:69
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:45
#: frontend/src/components/PrimaryDropdown.vue:41
msgid "Create New"
msgstr "Kreiraj"

#: frontend/src/components/Activities/Activities.vue:388
#: frontend/src/components/Modals/NoteModal.vue:6
msgid "Create Note"
msgstr "Stvori Bilješku"

#: frontend/src/components/Activities/Activities.vue:403
#: frontend/src/components/Modals/TaskModal.vue:6
msgid "Create Task"
msgstr "Stvori Zadatak"

#: frontend/src/components/Modals/ViewModal.vue:9
#: frontend/src/components/ViewControls.vue:687
msgid "Create View"
msgstr "Stvori Pogled"

#: frontend/src/components/Modals/EventModal.vue:12
msgid "Create an event"
msgstr "Stvori događaj"

#. Label of the create_customer_on_status_change (Check) field in DocType
#. 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Create customer on status change"
msgstr "Stvori klijenta pri promjeni statusa"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:348
#: frontend/src/pages/Calendar.vue:7
msgid "Create event"
msgstr "Stvori događaj"

#: frontend/src/components/Modals/CallLogDetailModal.vue:151
msgid "Create lead"
msgstr "Stvori Potencijalnog Klijenta"

#: frontend/src/components/Layouts/AppSidebar.vue:349
msgid "Create your first lead"
msgstr "Napravi potencijalnog klijenta"

#: frontend/src/components/Layouts/AppSidebar.vue:419
msgid "Create your first note"
msgstr "Napravi bilješku"

#: frontend/src/components/Layouts/AppSidebar.vue:399
msgid "Create your first task"
msgstr "Napravi zadatak"

#. Label of the currency (Link) field in DocType 'CRM Deal'
#. Label of the currency (Link) field in DocType 'CRM Organization'
#. Label of the currency (Link) field in DocType 'FCRM Settings'
#. Label of the currency_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/CurrencySettings.vue:31
msgid "Currency"
msgstr "Valuta"

#: frontend/src/components/Settings/Settings.vue:114
msgid "Currency & Exchange Rate"
msgstr "Valuta & Tečaj"

#: frontend/src/components/Settings/CurrencySettings.vue:7
msgid "Currency & Exchange rate provider"
msgstr "Pružatelj usluga Valuta i Tečaja"

#: frontend/src/components/Settings/CurrencySettings.vue:179
msgid "Currency set as {0} successfully"
msgstr "Valuta je uspješno postavljena na {0}"

#: crm/api/dashboard.py:872
msgid "Current pipeline distribution"
msgstr "Trenutna distribucija cjevovoda"

#: frontend/src/components/Layouts/AppSidebar.vue:591
msgid "Custom actions"
msgstr "Prilagođene radnje"

#: frontend/src/components/Layouts/AppSidebar.vue:541
msgid "Custom branding"
msgstr "Prilagođeno brendiranje"

#: frontend/src/components/Layouts/AppSidebar.vue:590
msgid "Custom fields"
msgstr "Prilagođena polja"

#: frontend/src/components/Layouts/AppSidebar.vue:593
msgid "Custom list actions"
msgstr "Radnje prilagođenog popisa"

#: frontend/src/components/Layouts/AppSidebar.vue:592
msgid "Custom statuses"
msgstr "Prilagođeni statusi"

#: frontend/src/pages/Deal.vue:476
msgid "Customer created successfully"
msgstr "Klijent je uspješno kreiran"

#: frontend/src/components/Layouts/AppSidebar.vue:587
#: frontend/src/components/Settings/Settings.vue:171
msgid "Customization"
msgstr "Prilagodjavanje"

#: frontend/src/components/ViewControls.vue:211
msgid "Customize quick filters"
msgstr "Prilagodi brze filtere"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Daily"
msgstr "Dnevno"

#: crm/api/dashboard.py:618
msgid "Daily performance of leads, deals, and wins"
msgstr "Dnevna učinkovitost potencijalnih klijenata, poslova i dobivenih poslova"

#: frontend/src/components/Activities/DataFields.vue:6
#: frontend/src/components/Layouts/AppSidebar.vue:580
#: frontend/src/pages/Deal.vue:552 frontend/src/pages/Lead.vue:399
#: frontend/src/pages/MobileDeal.vue:445 frontend/src/pages/MobileLead.vue:352
msgid "Data"
msgstr "Podaci"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Data Fields"
msgstr "Polja Podataka"

#. Label of the date (Date) field in DocType 'CRM Holiday'
#: crm/api/dashboard.py:620 crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:202
msgid "Date"
msgstr "Datum"

#: frontend/src/components/Modals/EventModal.vue:78
msgid "Date & Time"
msgstr "Datum & Vrijeme"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:267
#: frontend/src/components/Calendar/CalendarEventPanel.vue:288
#: frontend/src/components/Layouts/AppSidebar.vue:551
#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:20
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:59
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:59
#: frontend/src/components/Telephony/ExotelCallUI.vue:200
#: frontend/src/pages/Tasks.vue:132
msgid "Deal"
msgstr "Posao"

#. Label of the deal_owner (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Owner"
msgstr "Odgovorni"

#. Label of the deal_status (Link) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Deal Status"
msgstr "Status Posla"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Deal Statuses"
msgstr "Statusi Posla"

#. Label of the deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Value"
msgstr "Vrijednost posla"

#: crm/api/dashboard.py:1019
msgid "Deal generation channel analysis"
msgstr "Analiza kanala generisanja poslova"

#: frontend/src/pages/Contact.vue:528 frontend/src/pages/MobileContact.vue:528
#: frontend/src/pages/MobileOrganization.vue:472
#: frontend/src/pages/Organization.vue:502
msgid "Deal owner"
msgstr "Odgovorni Posla"

#: crm/api/dashboard.py:1075 crm/api/dashboard.py:1135
msgid "Deal value"
msgstr "Vrijednost posla"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Deal.vue:490 frontend/src/pages/MobileContact.vue:288
#: frontend/src/pages/MobileDeal.vue:384
#: frontend/src/pages/MobileOrganization.vue:325
msgid "Deals"
msgstr "Poslovi"

#: crm/api/dashboard.py:818
#: frontend/src/components/Dashboard/AddChartModal.vue:97
msgid "Deals by ongoing & won stage"
msgstr "Poslovi po tekućoj i dobivenoj fazi"

#: crm/api/dashboard.py:1124
#: frontend/src/components/Dashboard/AddChartModal.vue:100
msgid "Deals by salesperson"
msgstr "Poslovi po prodavača"

#: crm/api/dashboard.py:1018
#: frontend/src/components/Dashboard/AddChartModal.vue:107
msgid "Deals by source"
msgstr "Poslovi po izvoru"

#: crm/api/dashboard.py:871
#: frontend/src/components/Dashboard/AddChartModal.vue:105
msgid "Deals by stage"
msgstr "Poslovi po fazama"

#: crm/api/dashboard.py:1064
#: frontend/src/components/Dashboard/AddChartModal.vue:99
msgid "Deals by territory"
msgstr "Poslovi po distriktu"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:112
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:112
msgid "Dear {{ lead_name }}, \\n\\nThis is a reminder for the payment of {{ grand_total }}. \\n\\nThanks, \\nFrappé"
msgstr "Poštovani {{ lead_name }}, \\n\\nOvo je podsjetnik za plaćanje {{ grand_total }}. \\n\\nHvala, \\nFrappé"

#. Label of the default (Check) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Default"
msgstr "Standard"

#: frontend/src/components/Settings/EmailAccountCard.vue:41
msgid "Default Inbox"
msgstr "Standard Sanduče Pristigle e-pošte"

#: frontend/src/components/Settings/emailConfig.js:44
msgid "Default Incoming"
msgstr "Standard Dolazna"

#. Label of the default_medium (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Default Medium"
msgstr "Zadani Medij"

#: frontend/src/components/Settings/emailConfig.js:52
msgid "Default Outgoing"
msgstr "Standard Odlazna"

#. Label of the default_priority (Check) field in DocType 'CRM Service Level
#. Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "Default Priority"
msgstr "Standard Prioritet"

#: frontend/src/components/Settings/EmailAccountCard.vue:43
msgid "Default Sending"
msgstr "Standard Slanja"

#: frontend/src/components/Settings/EmailAccountCard.vue:39
msgid "Default Sending and Inbox"
msgstr "Standard Slanja i Prijema"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:33
msgid "Default Service Level Agreement already exists for {0}"
msgstr "Zadani Ugovor Razine Usluge već postoji za {0}"

#: frontend/src/components/Settings/TelephonySettings.vue:44
msgid "Default calling medium for logged in user"
msgstr "Zadani medij pozivanja za prijavljenog korisnika"

#: frontend/src/components/Telephony/CallUI.vue:112
msgid "Default calling medium set successfully to {0}"
msgstr "Zadani medij za pozivanje uspješno je postavljen na {0}"

#: frontend/src/components/Settings/TelephonySettings.vue:280
msgid "Default calling medium updated successfully"
msgstr "Zadani medij za pozivanje uspješno je ažuriran"

#: frontend/src/components/Settings/TelephonySettings.vue:37
msgid "Default medium"
msgstr "Standard Medij"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:18
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:30
msgid "Default statuses, custom fields and layouts restored successfully."
msgstr "Standard statusi, prilagođena polja i izgledi uspješno su vraćeni."

#: frontend/src/components/Activities/AttachmentArea.vue:131
#: frontend/src/components/Activities/NoteArea.vue:12
#: frontend/src/components/Activities/TaskArea.vue:55
#: frontend/src/components/Activities/TaskArea.vue:63
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:8
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:48
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:121
#: frontend/src/components/Controls/Grid.vue:329
#: frontend/src/components/DeleteLinkedDocModal.vue:10
#: frontend/src/components/DeleteLinkedDocModal.vue:90
#: frontend/src/components/Kanban/KanbanView.vue:222
#: frontend/src/components/ListBulkActions.vue:179
#: frontend/src/components/Modals/EventModal.vue:407
#: frontend/src/components/Modals/EventModal.vue:411
#: frontend/src/components/PrimaryDropdownItem.vue:42
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:129
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:229
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:199
#: frontend/src/components/ViewControls.vue:1116
#: frontend/src/components/ViewControls.vue:1127
#: frontend/src/pages/Calendar.vue:299 frontend/src/pages/Calendar.vue:303
#: frontend/src/pages/Contact.vue:106 frontend/src/pages/Deal.vue:111
#: frontend/src/pages/Lead.vue:157 frontend/src/pages/MobileContact.vue:79
#: frontend/src/pages/MobileContact.vue:263
#: frontend/src/pages/MobileDeal.vue:515
#: frontend/src/pages/MobileOrganization.vue:72
#: frontend/src/pages/MobileOrganization.vue:264
#: frontend/src/pages/Notes.vue:43 frontend/src/pages/Organization.vue:89
#: frontend/src/pages/Tasks.vue:371
msgid "Delete"
msgstr "Izbriši"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:26
msgid "Delete & Restore"
msgstr "Izbriši & Vrati"

#: frontend/src/components/Activities/TaskArea.vue:59
msgid "Delete Task"
msgstr "Izbriši Zadatak"

#: frontend/src/components/ViewControls.vue:1112
#: frontend/src/components/ViewControls.vue:1120
msgid "Delete View"
msgstr "Izbriši Prikaz"

#: frontend/src/components/DeleteLinkedDocModal.vue:66
msgid "Delete all"
msgstr "Izbriši sve"

#: frontend/src/components/Activities/AttachmentArea.vue:58
#: frontend/src/components/Activities/AttachmentArea.vue:127
msgid "Delete attachment"
msgstr "Izbriši privitak"

#: frontend/src/pages/MobileContact.vue:259
msgid "Delete contact"
msgstr "Izbriši kontakt"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:25
msgid "Delete event"
msgstr "Izbriši događaj"

#: frontend/src/components/Settings/InviteUserPage.vue:79
msgid "Delete invitation"
msgstr "Izbriši pozivnicu"

#: frontend/src/components/DeleteLinkedDocModal.vue:230
msgid "Delete linked item"
msgstr "Izbriši povezanu stavku"

#: frontend/src/components/DeleteLinkedDocModal.vue:11
msgid "Delete or unlink linked documents"
msgstr "Izbriši ili prekini veze povezanih dokumenata"

#: frontend/src/components/DeleteLinkedDocModal.vue:23
msgid "Delete or unlink these linked documents before deleting this document"
msgstr "Izbriši ili prekini vezu ovih povezanih dokumenata prije brisanja ovog dokumenta"

#: frontend/src/pages/MobileOrganization.vue:260
msgid "Delete organization"
msgstr "Izbriši organizaciju"

#: frontend/src/components/DeleteLinkedDocModal.vue:67
msgid "Delete {0} item(s)"
msgstr "Izbriši {0} stavku(e)"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:28
msgid "Delete {0} items"
msgstr "Izbriši {0} stavke"

#. Label of the description (Text Editor) field in DocType 'CRM Holiday'
#. Label of the description (Text Editor) field in DocType 'CRM Lost Reason'
#. Label of the description (Text Editor) field in DocType 'CRM Product'
#. Label of the description (Text Editor) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:152
#: frontend/src/components/Modals/EventModal.vue:134
#: frontend/src/components/Modals/TaskModal.vue:36
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:112
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:113
msgid "Description"
msgstr "Opis"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:426
msgid "Description is required"
msgstr "Opis je obavezan"

#: frontend/src/components/Apps.vue:63
msgid "Desk"
msgstr "Radni Prostor"

#. Label of the details (Tab Break) field in DocType 'CRM Lead'
#. Label of the details (Text Editor) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/pages/MobileContact.vue:283
#: frontend/src/pages/MobileDeal.vue:424 frontend/src/pages/MobileLead.vue:331
#: frontend/src/pages/MobileOrganization.vue:320
msgid "Details"
msgstr "Detalji"

#. Label of the call_receiving_device (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:39
msgid "Device"
msgstr "Uređaj"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Disable"
msgstr "Onemogući"

#. Label of the disabled (Check) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Disabled"
msgstr "Onemogućeno"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:626
#: frontend/src/components/CommentBox.vue:74
#: frontend/src/components/EmailEditor.vue:161
msgid "Discard"
msgstr "Odbaci"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:614
msgid "Discard unsaved changes?"
msgstr "Odbaciti nespremljene promjene?"

#. Label of the discount_percentage (Percent) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount %"
msgstr "Popust %"

#. Label of the discount_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount Amount"
msgstr "Iznos Popusta"

#. Label of the dt (Link) field in DocType 'CRM Form Script'
#. Label of the dt (Link) field in DocType 'CRM Global Settings'
#. Label of the dt (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "DocType"
msgstr "DocType"

#. Label of the dt (Link) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Document Type"
msgstr "Tip Dokumenta"

#: frontend/src/data/document.js:30
msgid "Document does not exist"
msgstr "Dokument ne postoji"

#: crm/api/activities.py:19
msgid "Document not found"
msgstr "Dokument nije pronađen"

#: frontend/src/data/document.js:45
msgid "Document updated successfully"
msgstr "Dokument je uspješno ažuriran"

#: frontend/src/components/Modals/AboutModal.vue:62
msgid "Documentation"
msgstr "Dokumentacija"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Done"
msgstr "Gotovo"

#: frontend/src/components/Dashboard/AddChartModal.vue:33
#: frontend/src/components/Dashboard/AddChartModal.vue:71
msgid "Donut chart"
msgstr "Prstenast grafikon"

#: frontend/src/components/Activities/AudioPlayer.vue:166
#: frontend/src/components/ViewControls.vue:254
msgid "Download"
msgstr "Preuzmi"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:24
msgid "Drag and drop files here or upload from"
msgstr "Povuci i Ispusti datoteke ovdje ili ih učitaj iz"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:56
msgid "Drop files here"
msgstr "Ispusti datoteke ovdje"

#. Label of the dropdown_items_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Dropdown Items"
msgstr "Padajuće Stavke"

#. Label of the due_date (Datetime) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Due Date"
msgstr "Krajnji Rok"

#. Option for the 'Type' (Select) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: frontend/src/components/Modals/EventModal.vue:158
#: frontend/src/components/Modals/ViewModal.vue:42
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:57
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:119
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:224
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:23
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:194
#: frontend/src/components/ViewControls.vue:1068
msgid "Duplicate"
msgstr "Kopiraj"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:38
msgid "Duplicate Assignment Rule"
msgstr "Dupliciraj Pravilo Dodjele"

#: frontend/src/components/Modals/ViewModal.vue:8
msgid "Duplicate View"
msgstr "Kopiraj Prikaz"

#: frontend/src/components/Modals/EventModal.vue:11
msgid "Duplicate an event"
msgstr "Dupliciraj događaj"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:33
#: frontend/src/components/Calendar/CalendarEventPanel.vue:347
#: frontend/src/components/Calendar/CalendarEventPanel.vue:432
msgid "Duplicate event"
msgstr "Dupliciraj događaj"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "Duplicate template"
msgstr "Dupliciraj predložak"

#. Label of the duration (Duration) field in DocType 'CRM Call Log'
#. Label of the duration (Duration) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Duration"
msgstr "Trajanje"

#: frontend/src/components/Layouts/AppSidebar.vue:604
#: frontend/src/components/Settings/Settings.vue:197
msgid "ERPNext"
msgstr "Sistem"

#. Name of a DocType
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext CRM Settings"
msgstr "Postavke Sistema za Prodajnu Podršku"

#. Label of the section_break_oubd (Section Break) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site API's"
msgstr "API-ji Web Stranice Sistema"

#. Label of the erpnext_site_url (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site URL"
msgstr "URL Web Stranice Sistema"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:25
msgid "ERPNext is not installed in the current site"
msgstr "Sistem nije instaliran na trenutnoj Web Stranici"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:98
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:124
msgid "ERPNext is not integrated with the CRM"
msgstr "Sistem nije integrisan sa Prodajnom Podrškom"

#: frontend/src/components/Settings/ERPNextSettings.vue:4
msgid "ERPNext settings"
msgstr "Postavke Poslovnog Sustava"

#: frontend/src/components/Settings/ERPNextSettings.vue:5
msgid "ERPNext settings updated"
msgstr "Postavke Poslovnog Sustrava ažurirane"

#: frontend/src/components/FieldLayout/Field.vue:91
#: frontend/src/components/FieldLayoutEditor.vue:313
#: frontend/src/components/FieldLayoutEditor.vue:339
#: frontend/src/components/ListBulkActions.vue:172
#: frontend/src/components/PrimaryDropdownItem.vue:35
#: frontend/src/components/ViewControls.vue:1086
#: frontend/src/pages/Dashboard.vue:16
msgid "Edit"
msgstr "Uredi"

#: frontend/src/components/Modals/CallLogModal.vue:97
msgid "Edit Call Log"
msgstr "Uredi Zapisnik Poziva"

#: frontend/src/components/Modals/DataFieldsModal.vue:7
msgid "Edit Data Fields Layout"
msgstr "Uredi Izgled Polja Podataka"

#: frontend/src/components/Settings/EmailEdit.vue:6
msgid "Edit Email"
msgstr "Uredi e-poštu"

#: frontend/src/components/Modals/SidePanelModal.vue:7
msgid "Edit Field Layout"
msgstr "Uredi Izgled Polja"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:7
msgid "Edit Grid Fields Layout"
msgstr "Uredi Raspored Polja Mreže"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:7
msgid "Edit Grid Row Fields Layout"
msgstr "Uredi Raspored Polja Reda Mreže"

#: frontend/src/components/Modals/NoteModal.vue:6
msgid "Edit Note"
msgstr "Uredi Bilješku"

#: frontend/src/components/Modals/QuickEntryModal.vue:7
msgid "Edit Quick Entry Layout"
msgstr "Uredi Izgled Brzog Unosa"

#: frontend/src/components/Modals/TaskModal.vue:6
msgid "Edit Task"
msgstr "Uredi Zadatak"

#: frontend/src/components/Modals/ViewModal.vue:6
msgid "Edit View"
msgstr "Uredi Prikaz"

#: frontend/src/components/Modals/EventModal.vue:9
msgid "Edit an event"
msgstr "Uredi događaj"

#: frontend/src/components/Modals/CallLogDetailModal.vue:39
msgid "Edit call log"
msgstr "Uredi zapisnik poziva"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:18
msgid "Edit event"
msgstr "Uredi događaj"

#: frontend/src/components/Activities/DataFields.vue:17
#: frontend/src/components/Controls/GridRowModal.vue:14
#: frontend/src/components/Modals/AddressModal.vue:14
#: frontend/src/components/Modals/CallLogModal.vue:16
#: frontend/src/components/Modals/ContactModal.vue:16
#: frontend/src/components/Modals/CreateDocumentModal.vue:16
#: frontend/src/components/Modals/DealModal.vue:16
#: frontend/src/components/Modals/LeadModal.vue:16
#: frontend/src/components/Modals/OrganizationModal.vue:16
msgid "Edit fields layout"
msgstr "Uredi raspored polja"

#: frontend/src/components/Controls/Grid.vue:57
msgid "Edit grid fields"
msgstr "Uredi polja mreže"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Edit note"
msgstr "Uredi bilješku"

#: frontend/src/components/Controls/Grid.vue:297
msgid "Edit row"
msgstr "Uredi redak"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Edit task"
msgstr "Uredi zadatak"

#: frontend/src/components/Controls/GridRowModal.vue:8
msgid "Editing Row {0}"
msgstr "Uređivanje Reda {0}"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:430
msgid "Editing event"
msgstr "Uređivanje događaja"

#. Label of the email (Data) field in DocType 'CRM Contacts'
#. Label of the email (Data) field in DocType 'CRM Invitation'
#. Label of the email (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json frontend/src/pages/Contact.vue:518
#: frontend/src/pages/MobileContact.vue:518
#: frontend/src/pages/MobileOrganization.vue:462
#: frontend/src/pages/MobileOrganization.vue:490
#: frontend/src/pages/Organization.vue:492
#: frontend/src/pages/Organization.vue:520
msgid "Email"
msgstr "E-pošta"

#: frontend/src/components/Settings/Settings.vue:148
msgid "Email Accounts"
msgstr "Računi e-pošte"

#: frontend/src/components/Settings/emailConfig.js:168
msgid "Email ID is required"
msgstr "Adresa e-pošte je obavezna"

#. Label of the email_sent_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Email Sent At"
msgstr "E-pošta poslana u"

#: frontend/src/components/Settings/Settings.vue:145
msgid "Email Settings"
msgstr "Postavke e-pošte"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:4
#: frontend/src/components/Settings/Settings.vue:154
msgid "Email Templates"
msgstr "Šablon e-pošte"

#: frontend/src/components/Settings/EmailAdd.vue:141
msgid "Email account created successfully"
msgstr "Račun e-pošte uspješno kreiran"

#: frontend/src/components/Settings/EmailEdit.vue:208
msgid "Email account updated successfully"
msgstr "Račun e-pošte uspješno ažuriran"

#: frontend/src/components/Settings/EmailAccountList.vue:7
msgid "Email accounts"
msgstr "Računi e-pošte"

#: frontend/src/components/Layouts/AppSidebar.vue:578
msgid "Email communication"
msgstr "Komunikacija e-poštom"

#: frontend/src/components/EmailEditor.vue:209
msgid "Email from Lead"
msgstr "E-pošta od Potencijalnog Klijenta"

#: frontend/src/components/Layouts/AppSidebar.vue:557
msgid "Email template"
msgstr "Predložak e-pošte"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:7
msgid "Email templates"
msgstr "Predlošci e-pošte"

#: frontend/src/pages/Deal.vue:542 frontend/src/pages/Lead.vue:389
#: frontend/src/pages/MobileDeal.vue:435 frontend/src/pages/MobileLead.vue:342
msgid "Emails"
msgstr "E-pošta"

#: frontend/src/components/ListViews/ListRows.vue:12
msgid "Empty"
msgstr "Prazno"

#: frontend/src/components/Filter.vue:123
msgid "Empty - Choose a field to filter by"
msgstr "Prazno - Odaberi polje po kojem ćete filtrirati"

#: frontend/src/components/SortBy.vue:130
msgid "Empty - Choose a field to sort by"
msgstr "Prazno - Odaberi polje po kojem ćete sortirati"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Enable"
msgstr "Omogući"

#. Label of the enable_forecasting (Check) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Enable Forecasting"
msgstr "Omogući Predviđanje"

#: frontend/src/components/Settings/emailConfig.js:28
msgid "Enable Incoming"
msgstr "Omogući Dolaznu"

#: frontend/src/components/Settings/emailConfig.js:36
msgid "Enable Outgoing"
msgstr "Omogući odlazne"

#: frontend/src/components/Settings/ForecastingSettings.vue:20
msgid "Enable forecasting"
msgstr "Omogući Predviđanje"

#. Label of the enabled (Check) field in DocType 'CRM Exotel Settings'
#. Label of the enabled (Check) field in DocType 'CRM Form Script'
#. Label of the enabled (Check) field in DocType 'CRM Service Level Agreement'
#. Label of the enabled (Check) field in DocType 'CRM Twilio Settings'
#. Label of the enabled (Check) field in DocType 'ERPNext CRM Settings'
#. Label of the enabled (Check) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:32
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:21
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:18
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:20
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:18
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:61
msgid "Enabled"
msgstr "Omogućeno"

#. Label of the enabled (Check) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Enabled?"
msgstr "Omogućeno?"

#. Label of the end_date (Date) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "End Date"
msgstr "Datum završetka"

#. Label of the end_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the end_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:242
#: frontend/src/components/Modals/EventModal.vue:112
msgid "End Time"
msgstr "Vrijeme Završetka"

#: frontend/src/composables/event.js:206
msgid "End time should be after start time"
msgstr "Vrijeme završetka treba biti nakon vremena početka"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:88
msgid "Enter Access Token"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:70
msgid "Enter Source Name"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:115
msgid "Enter access key"
msgstr "Unesi pristupni ključ"

#: frontend/src/components/Settings/BrandSettings.vue:33
msgid "Enter brand name"
msgstr "Unesite naziv marke"

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:30
msgid "Enter your Facebook Access Token"
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:347
msgid "Enter {0}"
msgstr "Unesi {0}"

#: frontend/src/components/Filter.vue:66 frontend/src/components/Filter.vue:99
#: frontend/src/components/Filter.vue:267
#: frontend/src/components/Filter.vue:288
#: frontend/src/components/Filter.vue:305
#: frontend/src/components/Filter.vue:316
#: frontend/src/components/Filter.vue:327
#: frontend/src/components/Filter.vue:343
msgid "Equals"
msgstr "Jednako"

#: frontend/src/components/Modals/ConvertToDealModal.vue:176
msgid "Error converting to deal: {0}"
msgstr "Greška pri pretvaranju u posao: {0}"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:245
msgid "Error creating Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:362
msgid "Error syncing leads"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:226
msgid "Error updating Lead Sync Source"
msgstr ""

#: frontend/src/pages/Deal.vue:741 frontend/src/pages/Lead.vue:469
#: frontend/src/pages/MobileDeal.vue:612 frontend/src/pages/MobileLead.vue:412
msgid "Error updating field"
msgstr "Pogreška pri ažuriranju polja"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:279
msgid "Error while creating customer in ERPNext, check error log for more details"
msgstr "Pogreška prilikom kreiranja klijenta u Sistemu, provjerite zapisnik grešaka za više detalja"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:191
msgid "Error while creating prospect in ERPNext, check error log for more details"
msgstr "Pogreška prilikom kreiranja potencijalnog klijenta u Sistemu, provjeri zapisnik grešaka za više detalja"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:117
msgid "Error while fetching customer in ERPNext, check error log for more details"
msgstr "Pogreška prilikom preuzimanja klijenta u Sistem, provjeri zapisnik grešaka za više detalja"

#: frontend/src/components/Modals/EventModal.vue:368
msgid "Event ID is required"
msgstr "ID događaja je obavezan"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:429
msgid "Event details"
msgstr "Detalji događaja"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:182
msgid "Event title"
msgstr "Naslov događaja"

#: frontend/src/pages/Deal.vue:557 frontend/src/pages/Lead.vue:404
msgid "Events"
msgstr "Događaji"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 10 Minutes"
msgstr ""

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 15 Minutes"
msgstr ""

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 5 Minutes"
msgstr ""

#: frontend/src/components/ViewControls.vue:268
#: frontend/src/components/ViewControls.vue:277
msgid "Excel"
msgstr "Excel"

#. Label of the exchange_rate (Float) field in DocType 'CRM Deal'
#. Label of the exchange_rate (Float) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Exchange Rate"
msgstr "Devizni Kurs"

#. Label of the exchange_rate_provider_section (Section Break) field in DocType
#. 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Exchange Rate Provider"
msgstr "Pružatelj Usluga Tečaja"

#: frontend/src/components/Settings/CurrencySettings.vue:60
msgid "Exchange rate provider"
msgstr "Pružatelj Usluga Tečaja"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the exotel (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:602
#: frontend/src/components/Settings/TelephonySettings.vue:41
#: frontend/src/components/Settings/TelephonySettings.vue:63
msgid "Exotel"
msgstr "Exotel"

#: crm/integrations/exotel/handler.py:114
msgid "Exotel Exception"
msgstr "Exotel Iznimka"

#. Label of the exotel_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Exotel Number"
msgstr "Exotel Broj"

#: crm/integrations/exotel/handler.py:85
msgid "Exotel Number Missing"
msgstr "Nedostaje broj Exotela"

#: crm/integrations/exotel/handler.py:89
msgid "Exotel Number {0} is not valid"
msgstr "Exotel broj {0} nije valjan"

#: frontend/src/components/Settings/TelephonySettings.vue:293
msgid "Exotel is not enabled"
msgstr "Exotel nije omogućen"

#: frontend/src/components/Settings/TelephonySettings.vue:140
msgid "Exotel settings updated successfully"
msgstr "Exotel postavke su uspješno ažurirane"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Expand"
msgstr "Proširi"

#. Label of the expected_closure_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Closure Date"
msgstr "Očekivani datum zaključivanja"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:173
msgid "Expected Closure Date is required."
msgstr "Očekivani datum zatvaranja je obavezan."

#. Label of the expected_deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Deal Value"
msgstr "Očekivana vrijednost posla"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:171
msgid "Expected Deal Value is required."
msgstr "Očekivana vrijednost posla je obavezna."

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Expired"
msgstr "Isteklo"

#: frontend/src/components/ViewControls.vue:203
#: frontend/src/components/ViewControls.vue:251
msgid "Export"
msgstr "Izvoz"

#: frontend/src/components/ViewControls.vue:282
msgid "Export All {0} Record(s)"
msgstr "Izvezi Sve {0} Zapis(e)"

#: frontend/src/components/ViewControls.vue:264
msgid "Export Type"
msgstr "Tip Izvoza"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "FCRM Note"
msgstr "Napomena"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "FCRM Settings"
msgstr "Postavke"

#. Option for the 'Type' (Select) field in DocType 'Lead Sync Source'
#. Label of the facebook_section (Section Break) field in DocType 'Lead Sync
#. Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook"
msgstr "Facebook"

#. Label of the facebook_form_id (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Facebook Form ID"
msgstr ""

#. Name of a DocType
#. Label of the facebook_lead_form (Link) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook Lead Form"
msgstr ""

#. Name of a DocType
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Facebook Lead Form Question"
msgstr ""

#. Label of the facebook_lead_id (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Facebook Lead ID"
msgstr ""

#. Name of a DocType
#. Label of the facebook_page (Link) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook Page"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Failed"
msgstr "Neuspješno"

#. Name of a DocType
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Failed Lead Sync Log"
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:111
msgid "Failed to add users"
msgstr "Dodavanje korisnika nije uspjelo"

#: crm/integrations/twilio/api.py:130
msgid "Failed to capture Twilio recording"
msgstr "Spremanje Twilio snimke nije uspjelo"

#: frontend/src/components/Settings/EmailAdd.vue:145
msgid "Failed to create email account, Invalid credentials"
msgstr "Nije uspjelo stvaranje računa e-pošte, nevažeći podaci za prijavu"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:179
msgid "Failed to create template"
msgstr "Nije uspjelo stvaranje predloška"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:186
msgid "Failed to delete Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:216
msgid "Failed to delete template"
msgstr "Brisanje predloška nije uspjelo"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:205
msgid "Failed to fetch exchange rate from {0} to {1} on {2}. Please check your internet connection or try again later."
msgstr "Nije uspjelo preuzimanje tečaja iz {0} u {1} {2}. Provjerite internetsku vezu ili pokušajte ponovno kasnije."

#: frontend/src/data/script.js:106
msgid "Failed to load form controller: {0}"
msgstr "Nije uspjelo učitavanje kontrolera obrasca: {0}"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:242
msgid "Failed to rename template"
msgstr "Preimenovanje predloška nije uspjelo"

#: crm/integrations/twilio/api.py:152
msgid "Failed to update Twilio call status"
msgstr "Ažuriranje statusa Twilio poziva nije uspjelo"

#: frontend/src/components/Settings/EmailEdit.vue:213
msgid "Failed to update email account, Invalid credentials"
msgstr "Nije uspjelo ažuriranje računa e-pošte, nevažeći akreditivi"

#: frontend/src/components/Modals/ChangePasswordModal.vue:95
msgid "Failed to update password"
msgstr "Nije uspjelo ažuriranje lozinke"

#: frontend/src/components/Settings/ProfileSettings.vue:151
msgid "Failed to update profile"
msgstr "Nije uspjelo ažuriranje profila"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:171
msgid "Failed to update source"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:208
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:201
msgid "Failed to update template"
msgstr "Nije uspjelo ažuriranje predloška"

#. Option for the 'Type' (Select) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Failure"
msgstr "Neuspjeh"

#. Label of the favicon (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/BrandSettings.vue:87
msgid "Favicon"
msgstr "Favicon"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:38
#: frontend/src/components/Modals/EditValueModal.vue:5
msgid "Field"
msgstr "Polje"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:19
#: frontend/src/components/Kanban/KanbanSettings.vue:48
msgid "Fields Order"
msgstr "Redoslijed Polja"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:333
msgid "File \"{0}\" was skipped because of invalid file type"
msgstr "Datoteka \"{0}\" je preskočena zbog nevažećeg tipa datoteke"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:354
msgid "File \"{0}\" was skipped because only {1} uploads are allowed"
msgstr "Datoteka \"{0}\" je preskočena jer su dopušteni prijenosi samo {1}"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:359
msgid "File \"{0}\" was skipped because only {1} uploads are allowed for DocType \"{2}\""
msgstr "Datoteka \"{0}\" je preskočena jer su samo {1} prijenosi dopušteni za DocType \"{2}\""

#: frontend/src/components/Filter.vue:6
msgid "Filter"
msgstr "Filtriraj"

#. Label of the filters (Code) field in DocType 'CRM View Settings'
#. Label of the filters_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Filters"
msgstr "Filteri"

#. Label of the first_name (Data) field in DocType 'CRM Deal'
#. Label of the first_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/ColumnSettings.vue:103
#: frontend/src/components/Filter.vue:57 frontend/src/components/Filter.vue:88
#: frontend/src/components/SortBy.vue:6 frontend/src/components/SortBy.vue:103
#: frontend/src/components/SortBy.vue:136
msgid "First Name"
msgstr "Ime"

#: frontend/src/components/Modals/LeadModal.vue:134
msgid "First Name is mandatory"
msgstr "Ime je obavezno"

#. Label of the first_responded_on (Datetime) field in DocType 'CRM Deal'
#. Label of the first_responded_on (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Responded On"
msgstr "Prvi Odgovor"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Due"
msgstr "Rok za Prvi Odgovor"

#. Label of the first_response_time (Duration) field in DocType 'CRM Service
#. Level Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "First Response TIme"
msgstr "Vrijeme Prvog Odgovora"

#. Label of the first_response_time (Duration) field in DocType 'CRM Deal'
#. Label of the first_response_time (Duration) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Time"
msgstr "Vrijeme Prvog Odgovora"

#: frontend/src/components/Filter.vue:130
#: frontend/src/components/Settings/ProfileSettings.vue:78
msgid "First name"
msgstr "Ime"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:48
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:48
msgid "For"
msgstr "Za"

#: crm/api/dashboard.py:688
#: frontend/src/components/Dashboard/AddChartModal.vue:95
msgid "Forecasted revenue"
msgstr "Predviđeni prihod"

#: frontend/src/components/Settings/ForecastingSettings.vue:5
#: frontend/src/components/Settings/Settings.vue:109
msgid "Forecasting"
msgstr "Prognoza"

#: frontend/src/components/Settings/ForecastingSettings.vue:76
msgid "Forecasting disabled successfully"
msgstr "Predviđanje je uspješno onemogućeno"

#: frontend/src/components/Settings/ForecastingSettings.vue:75
msgid "Forecasting enabled successfully"
msgstr "Predviđanje je uspješno omogućeno"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Form"
msgstr "Forma"

#. Label of the form_name (Data) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Form Name"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:19
msgid "Form Script updated successfully"
msgstr "Skripta Obrasca je uspješno ažurirana"

#. Name of a Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Frappe CRM"
msgstr "Frappe CRM"

#: frontend/src/components/Layouts/AppSidebar.vue:608
msgid "Frappe CRM mobile"
msgstr "Mobilna Aplikacija"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Friday"
msgstr "Petak"

#. Label of the from (Data) field in DocType 'CRM Call Log'
#. Label of the from (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From"
msgstr "Od"

#. Label of the from_date (Date) field in DocType 'CRM Holiday List'
#. Label of the from_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Date"
msgstr "Od Datuma"

#. Label of the from_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Type"
msgstr "Od Tipa"

#. Label of the from_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "From User"
msgstr "Od korisnika"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Fulfilled"
msgstr "Ispunjeno"

#. Label of the full_name (Data) field in DocType 'CRM Contacts'
#. Label of the lead_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Full Name"
msgstr "Puno Ime"

#: crm/api/dashboard.py:755
#: frontend/src/components/Dashboard/AddChartModal.vue:96
msgid "Funnel conversion"
msgstr "Konverzija lijevka"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:196
msgid "GMT+5:30"
msgstr "GMT+5:30"

#. Label of the gender (Link) field in DocType 'CRM Contacts'
#. Label of the gender (Link) field in DocType 'CRM Deal'
#. Label of the gender (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Gender"
msgstr "Rod"

#: crm/api/dashboard.py:1065
msgid "Geographic distribution of deals and revenue"
msgstr "Geografska distribucija poslova i prihoda"

#: frontend/src/components/Modals/AboutModal.vue:57
msgid "GitHub Repository"
msgstr "GitHub Repozitorij"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:582
msgid "Go back"
msgstr "Idi Nazad"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:156
msgid "Go to invite page"
msgstr "Idi na stranicu pozivnica"

#: frontend/src/pages/Deal.vue:95 frontend/src/pages/Lead.vue:141
msgid "Go to website"
msgstr "Idi na web stranicu"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Grid Row"
msgstr "Red Mreže"

#. Label of the group_by_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:379
#: frontend/src/components/ViewControls.vue:611 frontend/src/utils/view.js:16
msgid "Group By"
msgstr "Grupiši po"

#. Label of the group_by_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Group By Field"
msgstr "Grupiraj po Polju"

#: frontend/src/components/GroupBy.vue:8
msgid "Group By: "
msgstr "Grupiši po: "

#: frontend/src/components/Telephony/TwilioCallUI.vue:63
msgid "Hang up"
msgstr "Prekini poziv"

#: crm/templates/emails/helpdesk_invitation.html:2
msgid "Hello"
msgstr "Zdravo"

#: frontend/src/components/Layouts/AppSidebar.vue:93
msgid "Help"
msgstr "Pomoć"

#: frontend/src/components/Settings/Settings.vue:203
msgid "Helpdesk"
msgstr "Podrška"

#. Name of a DocType
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk CRM Settings"
msgstr "Postavke Prodajne Podršku"

#. Label of the helpdesk_site_apis_section (Section Break) field in DocType
#. 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk Site API's"
msgstr "API Helpdesk sajta"

#. Label of the helpdesk_site_url (Data) field in DocType 'Helpdesk CRM
#. Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk Site URL"
msgstr "URL Helpdesk sajta"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:18
msgid "Helpdesk is not installed in the current site"
msgstr "Helpdesk nije instaliran na trenutnom sajtu"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:40
msgid "Helpdesk is not integrated with the CRM"
msgstr "Helpdesk nije integrisan sa Prodajnom Podrškom"

#: frontend/src/components/Settings/HelpdeskSettings.vue:4
msgid "Helpdesk settings"
msgstr "Helpdesk Postavke"

#: frontend/src/components/Settings/HelpdeskSettings.vue:5
msgid "Helpdesk settings updated"
msgstr "Helpdeks Postavke ažurirane"

#: frontend/src/components/CommunicationArea.vue:56
msgid "Hi John, \\n\\nCan you please provide more details on this..."
msgstr "Bok John, \\n\\nMožete li dati više pojedinosti o ovome..."

#. Label of the hidden (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Hidden"
msgstr "Sakriveno"

#: frontend/src/components/Activities/Activities.vue:237
msgid "Hide"
msgstr "Sakrij"

#: frontend/src/components/Controls/Password.vue:19
msgid "Hide Password"
msgstr "Sakrij Lozinku"

#: frontend/src/components/Activities/CallArea.vue:75
msgid "Hide Recording"
msgstr "Sakrij Snimanje"

#: frontend/src/components/FieldLayoutEditor.vue:354
msgid "Hide border"
msgstr "Sakrij obrub"

#: frontend/src/components/FieldLayoutEditor.vue:349
msgid "Hide label"
msgstr "Sakrij oznaku"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Hide preview"
msgstr "Sakrij pregled"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "High"
msgstr "Visoki"

#. Label of the holiday_list (Link) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Holiday List"
msgstr "Lista Praznika"

#. Label of the holiday_list_name (Data) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holiday List Name"
msgstr "Naziv Liste Praznika"

#. Label of the holidays_section (Section Break) field in DocType 'CRM Holiday
#. List'
#. Label of the holidays (Table) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holidays"
msgstr "Praznici"

#: frontend/src/components/Settings/Settings.vue:174
msgid "Home Actions"
msgstr "Početne Radnje"

#: frontend/src/components/Layouts/AppSidebar.vue:542
#: frontend/src/components/Settings/HomeActions.vue:7
msgid "Home actions"
msgstr "Početnje radnje"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Hourly"
msgstr "Po satu"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:199
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:283
msgid "I understand, add conditions"
msgstr "Razumijem, dodaj uvjete"

#. Label of the id (Data) field in DocType 'CRM Call Log'
#. Label of the id (Data) field in DocType 'Facebook Lead Form'
#. Label of the id (Data) field in DocType 'Facebook Lead Form Question'
#. Label of the id (Data) field in DocType 'Facebook Page'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "ID"
msgstr "ID"

#. Label of the icon (Code) field in DocType 'CRM Dropdown Item'
#. Label of the icon (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Icon"
msgstr "Ikona"

#: frontend/src/components/Settings/emailConfig.js:55
msgid "If enabled, all outgoing emails will be sent from this account. Note: Only one account can be default outgoing."
msgstr "Ako je omogućeno, sve odlazne e-poruke bit će poslane s ovog računa. Napomena: Samo jedan račun može biti zadani odlazni."

#: frontend/src/components/Settings/emailConfig.js:47
msgid "If enabled, all replies to your company (eg: <EMAIL>) will come to this account. Note: Only one account can be default incoming."
msgstr "Ako je omogućeno, svi odgovori vašoj tvrtki (npr.: odgovori@vašatvrtka.com) stizat će na ovaj račun. Napomena: Samo jedan račun može biti zadani dolazni."

#: frontend/src/components/Settings/emailConfig.js:39
msgid "If enabled, outgoing emails can be sent from this account."
msgstr "Ako je omogućeno, odlazna e-pošta može se slati s ovog računa."

#: frontend/src/components/Settings/emailConfig.js:31
msgid "If enabled, records can be created from the incoming emails on this account."
msgstr "Ako je omogućeno, zapisi se mogu kreirati iz dolazne e-pošte na ovom računu."

#. Label of the image (Attach Image) field in DocType 'CRM Lead'
#. Label of the image (Attach Image) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Image"
msgstr "Slika"

#: frontend/src/components/Filter.vue:271
#: frontend/src/components/Filter.vue:292
#: frontend/src/components/Filter.vue:307
#: frontend/src/components/Filter.vue:320
#: frontend/src/components/Filter.vue:334
msgid "In"
msgstr "U"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "In Progress"
msgstr "U Toku"

#: frontend/src/components/SLASection.vue:68
msgid "In less than a minute"
msgstr "Za manje od minute"

#: frontend/src/components/Activities/CallArea.vue:36
msgid "Inbound Call"
msgstr "Dolazni Poziv"

#: frontend/src/components/Settings/EmailAccountCard.vue:45
msgid "Inbox"
msgstr "Pristigla Pošta"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Incoming"
msgstr "Dolazeći"

#: frontend/src/components/Telephony/TwilioCallUI.vue:41
msgid "Incoming call..."
msgstr "Dolazni poziv..."

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Industries"
msgstr "Industrije"

#. Label of the industry (Link) field in DocType 'CRM Deal'
#. Label of the industry (Data) field in DocType 'CRM Industry'
#. Label of the industry (Link) field in DocType 'CRM Lead'
#. Label of the industry (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Industry"
msgstr "Industrija"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Initiated"
msgstr "Pokrenut"

#: frontend/src/components/Telephony/TwilioCallUI.vue:36
msgid "Initiating call..."
msgstr "Pokretanje poziva..."

#: frontend/src/components/EmailEditor.vue:154
msgid "Insert Email Template"
msgstr "Umetni predložak e-pošte"

#: frontend/src/components/CommentBox.vue:49
#: frontend/src/components/EmailEditor.vue:130
msgid "Insert Emoji"
msgstr "Umetni emotikon"

#: frontend/src/components/Layouts/AppSidebar.vue:598
msgid "Integration"
msgstr "Integracija"

#: crm/integrations/exotel/handler.py:73
msgid "Integration Not Enabled"
msgstr "Integracija nije omogućena"

#: frontend/src/components/Settings/Settings.vue:182
msgctxt "FCRM"
msgid "Integrations"
msgstr "Integracije"

#: frontend/src/components/Layouts/AppSidebar.vue:529
#: frontend/src/components/Layouts/AppSidebar.vue:532
msgid "Introduction"
msgstr "Uvod"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:33
msgid "Invalid Account SID or Auth Token."
msgstr "Nevažeći SID računa ili Auth Token."

#: frontend/src/components/Modals/DealModal.vue:212
#: frontend/src/components/Modals/LeadModal.vue:153
msgid "Invalid Email"
msgstr "Nevažeća adresa e-pošte"

#: crm/integrations/exotel/handler.py:89
msgid "Invalid Exotel Number"
msgstr "Nevažeći Exotel Broj"

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:137
msgid "Invalid access token provided for Facebook."
msgstr ""

#: crm/api/dashboard.py:77
msgid "Invalid chart name"
msgstr "Nevažeći naziv grafikona"

#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.py:25
msgid "Invalid credentials"
msgstr "Nevažeće vjerodajnice"

#: frontend/src/components/Settings/emailConfig.js:172
msgid "Invalid email ID"
msgstr "Nevažeća e-pošta"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:601
msgid "Invalid fields, check if all are filled in and values are correct."
msgstr "Nevažeća polja, provjerite jesu li sva ispunjena i jesu li vrijednosti ispravne."

#: frontend/src/pages/InvalidPage.vue:6
msgid "Invalid page or not permitted to access"
msgstr "Nevažeća stranica ili pristup nije dopušten"

#: frontend/src/composables/event.js:203
msgid "Invalid start or end time"
msgstr "Nevažeće vrijeme početka ili završetka"

#: frontend/src/components/Settings/Users.vue:25
msgid "Invite New User"
msgstr "Pozovi novog korisnika"

#: frontend/src/components/Settings/Settings.vue:136
msgid "Invite User"
msgstr "Pozovi korisnika"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:78
#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:149
msgid "Invite agent"
msgstr "Pozovi agenta"

#: frontend/src/components/Settings/InviteUserPage.vue:51
msgid "Invite as"
msgstr "Pozovi kao"

#: frontend/src/components/Layouts/AppSidebar.vue:543
msgid "Invite users"
msgstr "Pozovi korisnike"

#: frontend/src/components/Settings/InviteUserPage.vue:10
msgid "Invite users to access CRM. Specify their roles to control access and permissions"
msgstr "Pozovite korisnike da pristupe CRM-u. Navedi njihove uloge kako biste kontrolirali pristup i dopuštenja"

#: frontend/src/components/Layouts/AppSidebar.vue:359
msgid "Invite your team"
msgstr "Pozovi tim"

#. Label of the invited_by (Link) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Invited By"
msgstr "Pozvao/la"

#: frontend/src/components/Filter.vue:273
#: frontend/src/components/Filter.vue:282
#: frontend/src/components/Filter.vue:294
#: frontend/src/components/Filter.vue:309
#: frontend/src/components/Filter.vue:322
#: frontend/src/components/Filter.vue:336
#: frontend/src/components/Filter.vue:345
msgid "Is"
msgstr "Je"

#. Label of the is_default (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Default"
msgstr "Je Standard"

#. Label of the is_erpnext_in_different_site (Check) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Is ERPNext installed on a different site?"
msgstr "Je li ERPNext instaliran na drugom mjestu?"

#. Label of the is_group (Check) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Is Group"
msgstr "Grupa"

#. Label of the is_helpdesk_in_different_site (Check) field in DocType
#. 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Is Helpdesk installed on a different site?"
msgstr "Da li je Helpdesk instaliran na drugom sajtu?"

#. Label of the is_primary (Check) field in DocType 'CRM Contacts'
#. Label of the is_primary (Check) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Is Primary"
msgstr "Primarno je"

#. Label of the is_standard (Check) field in DocType 'CRM Dropdown Item'
#. Label of the is_standard (Check) field in DocType 'CRM Form Script'
#. Label of the is_standard (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Standard"
msgstr "Je standardno"

#. Description of the 'Enable Forecasting' (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "It will make deal's \"Expected Closure Date\" & \"Expected Deal Value\" mandatory to get accurate forecasting insights"
msgstr "\"Očekivani datum zaključenja\" i \"Očekivana vrijednost posla\" bit će obvezni za dobivanje točnih uvida u predviđanja"

#. Label of the json (JSON) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "JSON"
msgstr "JSON"

#. Label of the job_title (Data) field in DocType 'CRM Deal'
#. Label of the job_title (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Job Title"
msgstr "Naziv Posla"

#: frontend/src/components/AssignToBody.vue:11
#: frontend/src/components/Filter.vue:74 frontend/src/components/Filter.vue:107
#: frontend/src/components/Modals/AssignmentModal.vue:13
#: frontend/src/components/Modals/TaskModal.vue:63
#: frontend/src/components/Telephony/TaskPanel.vue:47
msgid "John Doe"
msgstr "John Doe"

#. Label of the kanban_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:384
#: frontend/src/components/ViewControls.vue:600 frontend/src/utils/view.js:20
msgid "Kanban"
msgstr "Oglasna Tabla"

#. Label of the kanban_columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Columns"
msgstr "Stupci Oglasne Ploče"

#. Label of the kanban_fields (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Fields"
msgstr "Polja Oglasne Ploče"

#: frontend/src/components/Kanban/KanbanSettings.vue:3
#: frontend/src/components/Kanban/KanbanSettings.vue:8
msgid "Kanban Settings"
msgstr "Postavke Oglasne Table"

#. Label of the key (Data) field in DocType 'CRM Invitation'
#. Label of the key (Data) field in DocType 'Facebook Lead Form Question'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Key"
msgstr "Ključ"

#. Label of the label (Data) field in DocType 'CRM Dropdown Item'
#. Label of the label (Data) field in DocType 'CRM View Settings'
#. Label of the label (Data) field in DocType 'Facebook Lead Form Question'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: frontend/src/components/ColumnSettings.vue:100
msgid "Label"
msgstr "Oznaka"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:108
msgid "Last"
msgstr "Posljednje"

#: frontend/src/components/Filter.vue:615
msgid "Last 6 Months"
msgstr "Poslednjih 6 Mjeseci"

#: frontend/src/components/Filter.vue:607
msgid "Last Month"
msgstr "Zadnji mjesec"

#. Label of the last_name (Data) field in DocType 'CRM Deal'
#. Label of the last_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Last Name"
msgstr "Prezime"

#: frontend/src/components/Filter.vue:611
msgid "Last Quarter"
msgstr "Zadnji kvartal"

#. Label of the last_status_change_log (Link) field in DocType 'CRM Status
#. Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Last Status Change Log"
msgstr "Zadnji Zapisnik Promjene Statusa"

#. Label of the last_synced_at (Datetime) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:102
msgid "Last Synced At"
msgstr "Zadnja Sinhronizacija"

#: frontend/src/components/Filter.vue:603
msgid "Last Week"
msgstr "Prošle sedmice"

#: frontend/src/components/Filter.vue:619
msgid "Last Year"
msgstr "Prošle godine"

#: frontend/src/pages/Contact.vue:533 frontend/src/pages/MobileContact.vue:533
#: frontend/src/pages/MobileOrganization.vue:477
#: frontend/src/pages/MobileOrganization.vue:505
#: frontend/src/pages/Organization.vue:507
#: frontend/src/pages/Organization.vue:535
msgid "Last modified"
msgstr "Zadnja izmjena"

#: frontend/src/components/Settings/ProfileSettings.vue:83
msgid "Last name"
msgstr "Prezime"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:101
msgid "Last user assigned by this rule"
msgstr "Posljednji korisnik dodijeljen ovim pravilom"

#. Label of the layout (Code) field in DocType 'CRM Dashboard'
#. Label of the layout (Code) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Layout"
msgstr "Izgled"

#. Label of the lead (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:263
#: frontend/src/components/Calendar/CalendarEventPanel.vue:288
#: frontend/src/components/Layouts/AppSidebar.vue:550
#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:19
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:55
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:77
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:55
#: frontend/src/components/Telephony/ExotelCallUI.vue:200
#: frontend/src/pages/Tasks.vue:133
msgid "Lead"
msgstr "Potencijalni Klijent"

#. Label of the lead_data (Code) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Lead Data"
msgstr ""

#. Label of the lead_details_tab (Tab Break) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Details"
msgstr "Detalji Potencijalnog Klijenta"

#. Label of the lead_name (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Name"
msgstr "Naziv Potencijalnog Klijenta"

#. Label of the lead_owner (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Lead Owner"
msgstr "Odgovorni"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:120
msgid "Lead Owner cannot be same as the Lead Email Address"
msgstr "Odgovorni za Potencijalnog Klijenta ne može biti isti kao i adresa e-pošte potencijalnog klijenta"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Sources"
msgstr "Izvori Potencijalnog Klijenta"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Statuses"
msgstr "Statusi Potencijalnog Klijenta"

#. Name of a DocType
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:240
msgid "Lead Sync Source created successfully"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:183
msgid "Lead Sync Source deleted successfully"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:209
msgid "Lead Syncing"
msgstr ""

#: crm/api/dashboard.py:974
msgid "Lead generation channel analysis"
msgstr "Analiza kanala generisanja potencijalnih klijenata"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:7
msgid "Lead sources"
msgstr ""

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.js:8
msgid "Lead sync initiated."
msgstr ""

#: crm/api/dashboard.py:756
msgid "Lead to deal conversion pipeline"
msgstr "Konverzija potencijalnih klijenata u poslove"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/InvalidPage.vue:9 frontend/src/pages/Lead.vue:340
#: frontend/src/pages/MobileLead.vue:291
msgid "Leads"
msgstr "Potencijalni Klijenti"

#: crm/api/dashboard.py:973
#: frontend/src/components/Dashboard/AddChartModal.vue:106
msgid "Leads by source"
msgstr "Potencijalni klijenti prema izvoru"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:158
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:238
msgid "Learn about conditions"
msgstr "Saznajte više o uvjetima"

#. Label of the lft (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Left"
msgstr "Lijevo"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:43
msgid "Library"
msgstr "Biblioteka"

#: frontend/src/components/Filter.vue:269
#: frontend/src/components/Filter.vue:280
#: frontend/src/components/Filter.vue:290
#: frontend/src/components/Filter.vue:318
#: frontend/src/components/Filter.vue:332
msgid "Like"
msgstr "Lajk"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:252
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:47
msgid "Link"
msgstr "Veza"

#. Label of the links (Table) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Links"
msgstr "Veze"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#. Label of the list_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:374
#: frontend/src/components/ViewControls.vue:589 frontend/src/utils/view.js:12
msgid "List"
msgstr "Lista"

#: frontend/src/components/Activities/CallArea.vue:75
msgid "Listen"
msgstr "Slušaj"

#. Label of the load_default_columns (Check) field in DocType 'CRM View
#. Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Load Default Columns"
msgstr "Učitaj Zadane Stupce"

#: frontend/src/components/Kanban/KanbanView.vue:140
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:142
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:120
#: frontend/src/components/Settings/Users.vue:155
msgid "Load More"
msgstr "Učitaj Još"

#: frontend/src/components/Activities/Activities.vue:22
#: frontend/src/components/Activities/DataFields.vue:35
#: frontend/src/pages/Deal.vue:176 frontend/src/pages/MobileDeal.vue:117
msgid "Loading..."
msgstr "Učitavanje u toku..."

#. Label of the log_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the log_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Log"
msgstr "Dnevnik"

#: frontend/src/components/Activities/Activities.vue:814
#: frontend/src/components/Activities/ActivityHeader.vue:133
#: frontend/src/components/Activities/ActivityHeader.vue:176
msgid "Log a Call"
msgstr "Zabilježi poziv"

#: frontend/src/composables/frappecloud.js:23
msgid "Login to Frappe Cloud?"
msgstr "Prijavi se na Frappe Cloud?"

#. Label of the brand_logo (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Logo"
msgstr "Logo"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Lost"
msgstr "Izgubljen(a)"

#. Label of the lost_notes (Text) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lost Notes"
msgstr "Bilješke Izgubljenog Posla"

#. Label of the lost_reason (Link) field in DocType 'CRM Deal'
#. Label of the lost_reason (Data) field in DocType 'CRM Lost Reason'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "Lost Reason"
msgstr "Izgubljen(a) Razlog"

#: crm/api/dashboard.py:919
#: frontend/src/components/Dashboard/AddChartModal.vue:98
msgid "Lost deal reasons"
msgstr "Razlozi izgubljenog posla"

#: frontend/src/components/Modals/LostReasonModal.vue:27
msgid "Lost notes"
msgstr "Bilješke Izgubljenog Posla"

#: frontend/src/components/Modals/LostReasonModal.vue:83
msgid "Lost notes are required when lost reason is \"Other\""
msgstr "Bilješke izgubljenog posla su potrebne kada je razlog gubitka \"Ostalo\""

#: frontend/src/components/Modals/LostReasonModal.vue:4
#: frontend/src/components/Modals/LostReasonModal.vue:14
msgid "Lost reason"
msgstr "Razlog gubitka"

#: frontend/src/components/Modals/LostReasonModal.vue:79
msgid "Lost reason is required"
msgstr "Razlog gubitka je obavezan"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Low"
msgstr "Nisko"

#: frontend/src/pages/Contact.vue:100 frontend/src/pages/MobileContact.vue:73
msgid "Make Call"
msgstr "Pozovi"

#: frontend/src/components/ViewControls.vue:1101
msgid "Make Private"
msgstr "Učini Privatno"

#: frontend/src/components/ViewControls.vue:1101
msgid "Make Public"
msgstr "Učini Javno"

#: frontend/src/components/Activities/Activities.vue:818
#: frontend/src/components/Activities/ActivityHeader.vue:138
#: frontend/src/components/Activities/ActivityHeader.vue:181
#: frontend/src/pages/Deals.vue:505 frontend/src/pages/Leads.vue:532
msgid "Make a Call"
msgstr "Pozovi"

#: frontend/src/pages/Deal.vue:81 frontend/src/pages/Lead.vue:123
msgid "Make a call"
msgstr "Pozovi"

#: frontend/src/components/Activities/AttachmentArea.vue:98
msgid "Make attachment {0}"
msgstr "Priloži {0}"

#: frontend/src/components/Telephony/CallUI.vue:7
msgid "Make call"
msgstr "Pozovi"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make private"
msgstr "Učini Privatno"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make public"
msgstr "Učini Javno"

#: frontend/src/components/Activities/AttachmentArea.vue:107
msgid "Make {0}"
msgstr "Napravi {0}"

#: frontend/src/components/Telephony/CallUI.vue:34
msgid "Make {0} as default calling medium"
msgstr "Postavi {0} kao zadani medij za pozivanje"

#: frontend/src/components/Settings/ForecastingSettings.vue:24
msgid "Makes \"Expected Closure Date\" and \"Expected Deal Value\" mandatory for deal value forecasting"
msgstr "\"Očekivani datum zaključenja\" i \"Očekivana vrijednost posla\" postaju obavezni za predviđanje vrijednosti posla"

#: frontend/src/components/Settings/Users.vue:11
msgid "Manage CRM users by adding or inviting them, and assign roles to control their access and permissions"
msgstr "Upravljaj CRM korisnicima dodavanjem ili pozivanjem i dodijelite uloge za kontrolu njihovog pristupa i dopuštenja"

#: frontend/src/components/Settings/EmailAccountList.vue:11
msgid "Manage your email accounts to send and receive emails directly from CRM. You can add multiple accounts and set one as default for incoming and outgoing emails."
msgstr "Upravljaj računima e-pošte kako biste slali i primali e-poštu direktno iz CRM-a. Možete dodati više računa i postaviti jedan kao standard za dolazne i odlazne e-pošte."

#: frontend/src/components/Modals/AddExistingUserModal.vue:93
#: frontend/src/components/Settings/InviteUserPage.vue:157
#: frontend/src/components/Settings/InviteUserPage.vue:164
#: frontend/src/components/Settings/Users.vue:87
#: frontend/src/components/Settings/Users.vue:193
#: frontend/src/components/Settings/Users.vue:253
#: frontend/src/components/Settings/Users.vue:256
msgid "Manager"
msgstr "Upravitelj"

#: frontend/src/data/document.js:57
msgid "Mandatory field error: {0}"
msgstr "Pogreška obaveznog polja: {0}"

#: frontend/src/data/document.js:187 frontend/src/data/document.js:190
msgid "Mandatory fields required: {0}"
msgstr "Obavezna polja nedostaju: {0}"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Manual"
msgstr "Ručno"

#. Label of the mapped_to_crm_field (Autocomplete) field in DocType 'Facebook
#. Lead Form Question'
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Mapped to CRM Field"
msgstr ""

#: frontend/src/components/Notifications.vue:20
#: frontend/src/pages/MobileNotification.vue:12
#: frontend/src/pages/MobileNotification.vue:13
msgid "Mark all as read"
msgstr "Označi sve kao pročitano"

#: frontend/src/components/Layouts/AppSidebar.vue:547
msgid "Masters"
msgstr "Postavke"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:209
#: frontend/src/components/Modals/EventModal.vue:86
msgid "May 1, 2025"
msgstr "1. Svibnja 2025."

#. Label of the medium (Data) field in DocType 'CRM Call Log'
#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Medium"
msgstr "Srednje"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Mention"
msgstr "Spominjanje"

#. Label of the message (HTML Editor) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Message"
msgstr "Poruka"

#. Label of the middle_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Middle Name"
msgstr "Srednje ime"

#: frontend/src/components/Telephony/ExotelCallUI.vue:127
msgid "Minimize"
msgstr "Minimiziraj"

#. Label of the mobile_no (Data) field in DocType 'CRM Contacts'
#. Label of the mobile_no (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Mobile No"
msgstr "Mobilni Broj"

#: frontend/src/components/Modals/DealModal.vue:208
#: frontend/src/components/Modals/LeadModal.vue:149
msgid "Mobile No should be a number"
msgstr "Broj mobitela trebao bi biti broj"

#. Label of the mobile_no (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Mobile No."
msgstr "Broj mobilnog."

#: frontend/src/components/Telephony/CallUI.vue:22
msgid "Mobile Number"
msgstr "Mobilni Broj"

#: crm/integrations/exotel/handler.py:93
msgid "Mobile Number Missing"
msgstr "Nedostaje Broj Mobitela"

#: frontend/src/components/Layouts/AppSidebar.vue:611
msgid "Mobile app installation"
msgstr "Instalacija mobilne aplikacije"

#: frontend/src/pages/Contact.vue:523 frontend/src/pages/MobileContact.vue:523
#: frontend/src/pages/MobileOrganization.vue:467
#: frontend/src/pages/Organization.vue:497
msgid "Mobile no"
msgstr "Mobilni Broj"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Monday"
msgstr "Ponedjeljak"

#: crm/api/dashboard.py:691
msgid "Month"
msgstr "Mjesec"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Monthly"
msgstr "Mjesečno"

#: frontend/src/components/ViewControls.vue:221
msgid "More Options"
msgstr "Više Opcija"

#: frontend/src/components/FieldLayoutEditor.vue:448
msgid "Move to next section"
msgstr "Prijeđi na sljedeći odjeljak"

#: frontend/src/components/FieldLayoutEditor.vue:401
msgid "Move to next tab"
msgstr "Prijeđi na sljedeću karticu"

#: frontend/src/components/FieldLayoutEditor.vue:458
msgid "Move to previous section"
msgstr "Prijeđi na prethodni odjeljak"

#: frontend/src/components/FieldLayoutEditor.vue:387
msgid "Move to previous tab"
msgstr "Prijeđi na prethodnu karticu"

#: frontend/src/components/Modals/ViewModal.vue:29
msgid "My Open Deals"
msgstr "Moji Otvoreni Poslovi"

#. Label of the title (Data) field in DocType 'CRM Dashboard'
#. Label of the name1 (Data) field in DocType 'CRM Dropdown Item'
#. Label of the brand_name (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:51
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:52
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:39
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:39
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:59
#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:22
#: frontend/src/components/ViewControls.vue:779
#: frontend/src/pages/MobileOrganization.vue:485
#: frontend/src/pages/Organization.vue:515
msgid "Name"
msgstr "Naziv"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:417
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:151
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:154
msgid "Name is required"
msgstr "Ime je obavezno"

#. Label of the naming_series (Select) field in DocType 'CRM Deal'
#. Label of the naming_series (Select) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Naming Series"
msgstr "Imenovanje serije"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:99
msgid "Nested conditions"
msgstr "Ugniježđeni uvjeti"

#. Label of the net_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Net Amount"
msgstr "Neto Iznos"

#. Label of the net_total (Currency) field in DocType 'CRM Deal'
#. Label of the net_total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Net Total"
msgstr "Neto Ukupno"

#: frontend/src/components/Activities/ActivityHeader.vue:76
#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:19
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:19
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:20
#: frontend/src/components/Settings/Users.vue:30
msgid "New"
msgstr "Novi"

#: frontend/src/components/Modals/AddressModal.vue:93
msgid "New Address"
msgstr "Nova Adresa"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:12
msgid "New Assignment Rule"
msgstr "Novo Pravilo Dodjele"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:44
msgid "New Assignment Rule Name"
msgstr "Naziv novog pravila dodjeljivanja"

#: frontend/src/components/Modals/CallLogModal.vue:97
msgid "New Call Log"
msgstr "Novi Zapisnik Poziva"

#: frontend/src/components/Activities/Activities.vue:398
#: frontend/src/components/Activities/ActivityHeader.vue:19
#: frontend/src/components/Activities/ActivityHeader.vue:123
msgid "New Comment"
msgstr "Novi Komentar"

#: frontend/src/components/Modals/ContactModal.vue:8
msgid "New Contact"
msgstr "Novi Kontakt"

#: frontend/src/components/Activities/Activities.vue:393
#: frontend/src/components/Activities/ActivityHeader.vue:12
#: frontend/src/components/Activities/ActivityHeader.vue:118
msgid "New Email"
msgstr "Nova e-pošta"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:9
msgid "New Lead Sync Source"
msgstr ""

#: frontend/src/components/Activities/ActivityHeader.vue:66
msgid "New Message"
msgstr "Nova Poruka"

#: frontend/src/components/Activities/ActivityHeader.vue:41
#: frontend/src/components/Activities/ActivityHeader.vue:144
#: frontend/src/pages/Deals.vue:511 frontend/src/pages/Leads.vue:538
msgid "New Note"
msgstr "Nova Napomena"

#: frontend/src/components/Modals/OrganizationModal.vue:8
msgid "New Organization"
msgstr "Nova Organizacija"

#: frontend/src/components/Modals/ChangePasswordModal.vue:6
msgid "New Password"
msgstr "Nova Lozinka"

#: frontend/src/components/FieldLayoutEditor.vue:201
#: frontend/src/components/SidePanelLayoutEditor.vue:131
msgid "New Section"
msgstr "Novi Odjeljak"

#: frontend/src/components/FieldLayoutEditor.vue:293
#: frontend/src/components/FieldLayoutEditor.vue:298
msgid "New Tab"
msgstr "Nova Kartica"

#: frontend/src/components/Activities/ActivityHeader.vue:48
#: frontend/src/components/Activities/ActivityHeader.vue:149
#: frontend/src/pages/Deals.vue:516 frontend/src/pages/Leads.vue:543
msgid "New Task"
msgstr "Novi Zadatak"

#: frontend/src/components/Activities/ActivityHeader.vue:159
msgid "New WhatsApp Message"
msgstr "Nova WhatsApp Poruka"

#: frontend/src/components/Modals/ConvertToDealModal.vue:67
#: frontend/src/pages/MobileLead.vue:162
msgid "New contact will be created based on the person's details"
msgstr "Novi kontakt će se stvoriti na temelju podataka o osobi"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:431
msgid "New event"
msgstr "Novi događaj"

#: frontend/src/components/Modals/ConvertToDealModal.vue:42
#: frontend/src/pages/MobileLead.vue:136
msgid "New organization will be created based on the data in details section"
msgstr "Nova organizacija će se stvoriti na temelju podataka u odjeljku s detaljima"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "New template"
msgstr "Novi predložak"

#: frontend/src/components/Modals/CreateDocumentModal.vue:88
msgid "New {0}"
msgstr "Novi {0}"

#: frontend/src/components/Filter.vue:663
msgid "Next 6 Months"
msgstr "Sljedećih 6 Mjeseci"

#: frontend/src/components/Filter.vue:655
msgid "Next Month"
msgstr "Sljedeći Mjesec"

#: frontend/src/components/Filter.vue:659
msgid "Next Quarter"
msgstr "Sljedeći Kvartal"

#. Label of the next_step (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Next Step"
msgstr "Sljedeći Korak"

#: frontend/src/components/Filter.vue:651
msgid "Next Week"
msgstr "Sljedeći Tjedan"

#: frontend/src/components/Filter.vue:667
msgid "Next Year"
msgstr "Sljedeće Godine"

#: frontend/src/components/Controls/Grid.vue:27
msgid "No"
msgstr "Ne"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "No Answer"
msgstr "Bez Odgovora"

#: frontend/src/components/Controls/Grid.vue:322
msgid "No Data"
msgstr "Nema podataka"

#: frontend/src/components/Activities/EventArea.vue:78
msgid "No Events Scheduled"
msgstr "Nema Zakazanih Događaja"

#: frontend/src/components/Kanban/KanbanView.vue:103
#: frontend/src/pages/Deals.vue:105 frontend/src/pages/Leads.vue:121
#: frontend/src/pages/Tasks.vue:71
msgid "No Title"
msgstr "Bez Naziva"

#: frontend/src/components/Settings/EmailEdit.vue:150
msgid "No changes made"
msgstr "Nisu napravljene promjene"

#: frontend/src/components/Modals/SidePanelModal.vue:51
#: frontend/src/pages/Deal.vue:271 frontend/src/pages/MobileDeal.vue:205
msgid "No contacts added"
msgstr "Nema dodanih kontakata"

#: frontend/src/pages/Deal.vue:257
msgid "No details added"
msgstr ""

#: frontend/src/pages/Deal.vue:90 frontend/src/pages/Lead.vue:137
msgid "No email set"
msgstr "Nije postavljena e-pošta"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:48
msgid "No email templates found"
msgstr "Nisu pronađeni predlošci e-pošte"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:14
msgid "No items in the list"
msgstr "Nema stavki na popisu"

#: frontend/src/components/FieldLayoutEditor.vue:92
msgid "No label"
msgstr "Bez Oznake"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:49
msgid "No lead sources found"
msgstr ""

#: frontend/src/pages/Deal.vue:707
msgid "No mobile number set"
msgstr "Broj mobitela nije postavljen"

#: frontend/src/components/Notifications.vue:77
#: frontend/src/pages/MobileNotification.vue:61
msgid "No new notifications"
msgstr "Nema novih obavijesti"

#: frontend/src/pages/Lead.vue:129
msgid "No phone number set"
msgstr "Telefonski broj nije postavljen"

#: frontend/src/pages/Deal.vue:702
msgid "No primary contact set"
msgstr "Nije postavljen primarni kontakt"

#: frontend/src/components/Calendar/Attendee.vue:232
#: frontend/src/components/Controls/EmailMultiSelect.vue:133
#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:70
msgid "No results found"
msgstr "Nisu pronađeni rezultati"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:66
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:42
msgid "No templates found"
msgstr "Nema pronađenih predložaka"

#: frontend/src/components/Modals/AddExistingUserModal.vue:39
#: frontend/src/components/Settings/Users.vue:57
msgid "No users found"
msgstr "Nisu pronađeni korisnici"

#: frontend/src/pages/MobileOrganization.vue:281
#: frontend/src/pages/Organization.vue:318
msgid "No website found"
msgstr "Nije pronađena web stranica"

#: frontend/src/pages/Deal.vue:100 frontend/src/pages/Lead.vue:146
msgid "No website set"
msgstr "Nije postavljena web stranica"

#: frontend/src/components/PrimaryDropdown.vue:33
msgid "No {0} Available"
msgstr "Nema {0} Dostupno"

#: frontend/src/pages/CallLogs.vue:59 frontend/src/pages/Contact.vue:162
#: frontend/src/pages/Contacts.vue:58 frontend/src/pages/Deals.vue:234
#: frontend/src/pages/Leads.vue:260 frontend/src/pages/MobileContact.vue:147
#: frontend/src/pages/MobileOrganization.vue:139
#: frontend/src/pages/Notes.vue:95 frontend/src/pages/Organization.vue:158
#: frontend/src/pages/Organizations.vue:58 frontend/src/pages/Tasks.vue:184
msgid "No {0} Found"
msgstr "Nije pronađeno {0}"

#. Label of the no_of_employees (Select) field in DocType 'CRM Deal'
#. Label of the no_of_employees (Select) field in DocType 'CRM Lead'
#. Label of the no_of_employees (Select) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "No. of Employees"
msgstr "Personalni Broj"

#: frontend/src/components/Activities/AudioPlayer.vue:148
msgid "Normal"
msgstr "Normalan"

#: crm/utils/__init__.py:263
msgid "Not Allowed"
msgstr "Nije dozvoljeno"

#: frontend/src/components/Filter.vue:268
#: frontend/src/components/Filter.vue:289
#: frontend/src/components/Filter.vue:306
#: frontend/src/components/Filter.vue:317
#: frontend/src/components/Filter.vue:344
msgid "Not Equals"
msgstr "Nije Jednako"

#: frontend/src/components/Filter.vue:272
#: frontend/src/components/Filter.vue:293
#: frontend/src/components/Filter.vue:308
#: frontend/src/components/Filter.vue:321
#: frontend/src/components/Filter.vue:335
msgid "Not In"
msgstr "Nije u"

#: frontend/src/components/Filter.vue:270
#: frontend/src/components/Filter.vue:281
#: frontend/src/components/Filter.vue:291
#: frontend/src/components/Filter.vue:319
#: frontend/src/components/Filter.vue:333
msgid "Not Like"
msgstr "Nije Kao"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:10
#: frontend/src/components/Controls/GridRowFieldsModal.vue:10
#: frontend/src/components/Modals/DataFieldsModal.vue:10
#: frontend/src/components/Modals/QuickEntryModal.vue:10
#: frontend/src/components/Modals/SidePanelModal.vue:10
#: frontend/src/components/Settings/SettingsPage.vue:11
#: frontend/src/components/Settings/TelephonySettings.vue:11
msgid "Not Saved"
msgstr "Nespremljeno"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:133
msgid "Not Synced"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:272
msgid "Not allowed to add contact to Deal"
msgstr "Nije dopušteno dodati kontakt u Posao"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:455
msgid "Not allowed to convert Lead to Deal"
msgstr "Nije dopušteno pretvoriti Potencijalnog Klijenta u Posao"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:283
msgid "Not allowed to remove contact from Deal"
msgstr "Nije dopušteno ukloniti kontakt iz Posla"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:294
msgid "Not allowed to set primary contact for Deal"
msgstr "Nije dopušteno postaviti primarni kontakt za Posao"

#. Label of the note (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: frontend/src/components/Layouts/AppSidebar.vue:554
msgid "Note"
msgstr "Napomena"

#: frontend/src/pages/Deal.vue:572 frontend/src/pages/Lead.vue:419
#: frontend/src/pages/MobileDeal.vue:461 frontend/src/pages/MobileLead.vue:367
msgid "Notes"
msgstr "Napomene"

#: frontend/src/pages/Notes.vue:23
msgid "Notes View"
msgstr "Prikaz Napomena"

#: frontend/src/components/Activities/EmailArea.vue:15
#: frontend/src/components/Layouts/AppSidebar.vue:583
msgid "Notification"
msgstr "Obavijest"

#. Label of the notification_text (Text) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Text"
msgstr "Tekst Obavijesti"

#. Label of the notification_type_doc (Dynamic Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doc"
msgstr "Tip Obavijesti Dokument"

#. Label of the notification_type_doctype (Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doctype"
msgstr "Tip Obavijesti Dokument"

#: frontend/src/components/Layouts/AppSidebar.vue:13
#: frontend/src/components/Mobile/MobileSidebar.vue:23
#: frontend/src/components/Notifications.vue:17
#: frontend/src/pages/MobileNotification.vue:6
msgid "Notifications"
msgstr "Obavještenja"

#. Label of the number (Data) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Number"
msgstr "Broj"

#: frontend/src/components/Dashboard/AddChartModal.vue:19
#: frontend/src/components/Dashboard/AddChartModal.vue:69
msgid "Number chart"
msgstr "Brojevni grafikon"

#: crm/api/dashboard.py:1072 crm/api/dashboard.py:1132
msgid "Number of deals"
msgstr "Broj Poslova"

#: crm/api/dashboard.py:1125
msgid "Number of deals and total value per salesperson"
msgstr "Broj poslova i ukupna vrijednost po prodavaču"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:167
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:251
msgid "Old Condition"
msgstr "Stari uvjet"

#. Label of the old_parent (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Old Parent"
msgstr "Stari Nadređeni"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "On Hold"
msgstr "Na čekanju"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Ongoing"
msgstr "U tijeku"

#: crm/api/dashboard.py:190
#: frontend/src/components/Dashboard/AddChartModal.vue:77
msgid "Ongoing deals"
msgstr "Tekući poslovi"

#: frontend/src/utils/index.js:474
msgid "Only image files are allowed"
msgstr "Dopuštene su samo slikovne datoteke"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:60
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.py:23
msgid "Only one {0} can be set as primary."
msgstr "Samo jedan {0} može biti postavljen kao primarni."

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Open"
msgstr "Otvori"

#: frontend/src/components/Modals/NoteModal.vue:13
#: frontend/src/components/Modals/TaskModal.vue:13
msgid "Open Deal"
msgstr "Otvori Posao"

#: frontend/src/components/Modals/NoteModal.vue:14
#: frontend/src/components/Modals/TaskModal.vue:14
msgid "Open Lead"
msgstr "Otvori Potencijalni Klijent"

#: crm/fcrm/doctype/crm_deal/crm_deal.js:6
#: crm/fcrm/doctype/crm_lead/crm_lead.js:6
msgid "Open in Portal"
msgstr "Otvori u Portalu"

#. Label of the open_in_new_window (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Open in new window"
msgstr "Otvori u novom prozoru"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:88
msgid "Open nested conditions"
msgstr "Otvori ugniježđene uvjete"

#: frontend/src/pages/Organization.vue:96
msgid "Open website"
msgstr "Otvori web stranicu"

#: frontend/src/components/Kanban/KanbanView.vue:218
#: frontend/src/components/Modals/CallLogDetailModal.vue:15
#: frontend/src/components/ViewControls.vue:199
msgid "Options"
msgstr "Opcije"

#: frontend/src/pages/Welcome.vue:40
msgid "Or create leads manually"
msgstr "Ili ručno napravi potencijalne klijente"

#. Label of the order_by_tab (Tab Break) field in DocType 'CRM View Settings'
#. Label of the order_by (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Order By"
msgstr "Sortiraj prema"

#. Label of the organization (Link) field in DocType 'CRM Deal'
#. Label of the organization_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the organization (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Layouts/AppSidebar.vue:553
#: frontend/src/components/Modals/ConvertToDealModal.vue:25
#: frontend/src/pages/Contact.vue:502 frontend/src/pages/MobileContact.vue:502
#: frontend/src/pages/MobileLead.vue:118
#: frontend/src/pages/MobileOrganization.vue:446
#: frontend/src/pages/MobileOrganization.vue:500
#: frontend/src/pages/Organization.vue:476
#: frontend/src/pages/Organization.vue:530
msgid "Organization"
msgstr "Organizacija"

#. Label of the organization_details_section (Section Break) field in DocType
#. 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Organization Details"
msgstr "Detalji Organizacije"

#. Label of the organization_logo (Attach Image) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Logo"
msgstr "Logo Organizacije"

#. Label of the organization_name (Data) field in DocType 'CRM Deal'
#. Label of the organization_name (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Name"
msgstr "Naziv Organizacije"

#: frontend/src/pages/Deal.vue:62
msgid "Organization logo"
msgstr "Logo Organizacije"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/MobileOrganization.vue:205
#: frontend/src/pages/Organization.vue:243
msgid "Organizations"
msgstr "Organizacije"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:107
msgid "Organizer"
msgstr "Organizator"

#: frontend/src/components/Layouts/AppSidebar.vue:575
msgid "Other features"
msgstr "Ostale značajke"

#. Label of the organization_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Others"
msgstr "Ostalo"

#: frontend/src/components/Activities/CallArea.vue:37
msgid "Outbound Call"
msgstr "Odlazni Poziv"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Outgoing"
msgstr "Odlazno"

#. Label of the log_owner (Link) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Owner"
msgstr "Odgovorni"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:102
msgid "Owner: {0}"
msgstr "Vlasnik: {0}"

#. Label of the page (Link) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Page"
msgstr "Stranica"

#. Label of the page_name (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Page Name"
msgstr "Naziv stranice"

#. Label of the parent_crm_territory (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Parent CRM Territory"
msgstr "Nadređeni Distrikt"

#: frontend/src/components/Settings/emailConfig.js:64
msgid "Password"
msgstr "Lozinka"

#: crm/api/demo.py:21 crm/api/demo.py:29
msgid "Password cannot be reset by Demo User {}"
msgstr "Demo Korisnik {} ne može poništiti lozinku"

#: frontend/src/components/Settings/emailConfig.js:175
msgid "Password is required"
msgstr "Lozinka je obavezna"

#: frontend/src/components/Modals/ChangePasswordModal.vue:88
msgid "Password updated successfully"
msgstr "Lozinka je uspješno ažurirana"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:13
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:38
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:38
msgid "Payment Reminder"
msgstr "Podsjetnik Plaćanja"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:69
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:69
msgid "Payment Reminder from Frappé - (#{{ name }})"
msgstr "Podsjetnik za plaćanje od Frappéa - (#{{ name }})"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Pending"
msgstr "Na čekanju"

#: frontend/src/components/Settings/InviteUserPage.vue:61
msgid "Pending Invites"
msgstr "Pozivnice na Čekanju"

#: frontend/src/pages/Dashboard.vue:66
msgid "Period"
msgstr "Razdoblje"

#. Label of the person_section (Section Break) field in DocType 'CRM Deal'
#. Label of the person_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Person"
msgstr "Osoba"

#: frontend/src/components/Settings/Settings.vue:90
msgid "Personal Settings"
msgstr "Osobne Postavke"

#. Label of the phone (Data) field in DocType 'CRM Contacts'
#. Label of the phone (Data) field in DocType 'CRM Lead'
#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/pages/MobileOrganization.vue:495
#: frontend/src/pages/Organization.vue:525
msgid "Phone"
msgstr "Telefon"

#. Label of the phone_nos (Table) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Phone Numbers"
msgstr "Telefonski Brojevi"

#: frontend/src/components/ViewControls.vue:1093
msgid "Pin View"
msgstr "Prikači Prikaz"

#. Label of the pinned (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Pinned"
msgstr "Prikačeno"

#: frontend/src/components/ViewControls.vue:677
msgid "Pinned Views"
msgstr "Prikačeni Prikazi"

#: frontend/src/components/Layouts/AppSidebar.vue:571
msgid "Pinned view"
msgstr "Prikvačeni prikaz"

#: frontend/src/components/Activities/AudioPlayer.vue:176
msgid "Playback speed"
msgstr "Brzina izvođenja"

#: frontend/src/components/Settings/EmailAccountList.vue:49
msgid "Please add an email account to continue."
msgstr "Dodaj račun e-pošte za nastavak."

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:157
msgid "Please check your access token"
msgstr ""

#: crm/integrations/twilio/twilio_handler.py:119
msgid "Please enable twilio settings before making a call."
msgstr "Omogući twilio postavke prije upućivanja poziva."

#: frontend/src/components/FilesUploader/FilesUploader.vue:165
msgid "Please enter a valid URL"
msgstr "Unesi važeći URL"

#: frontend/src/components/Settings/CurrencySettings.vue:150
msgid "Please enter the Exchangerate Host access key."
msgstr "Unesi pristupni ključ Pružitelja Usluga Tečaja."

#: frontend/src/components/Modals/LostReasonModal.vue:9
msgid "Please provide a reason for marking this deal as lost"
msgstr "Navedi razlog zašto je ovaj posao označen kao izgubljen"

#: frontend/src/components/Settings/CurrencySettings.vue:143
msgid "Please select a currency before saving."
msgstr "Odaberi valutu prije spremanja."

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.py:65
msgid "Please select a lead gen form before syncing!"
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:136
#: frontend/src/pages/MobileLead.vue:431
msgid "Please select an existing contact"
msgstr "Odaberi postojeći kontakt"

#: frontend/src/components/Modals/ConvertToDealModal.vue:141
#: frontend/src/pages/MobileLead.vue:436
msgid "Please select an existing organization"
msgstr "Odaberi postojeću organizaciju"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:126
msgid "Please set Email Address"
msgstr "Postavi adresu e-pošte"

#: crm/integrations/exotel/handler.py:73
msgid "Please setup Exotel intergration"
msgstr "Postavi Exotel Integraciju"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:181
msgid "Please specify a reason for losing the deal."
msgstr "Navedi razlog gubitka posla."

#: crm/fcrm/doctype/crm_deal/crm_deal.py:183
msgid "Please specify the reason for losing the deal."
msgstr "Navedi razlog gubitka posla."

#. Label of the position (Int) field in DocType 'CRM Deal Status'
#. Label of the position (Int) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Position"
msgstr "Pozicija"

#: frontend/src/pages/Deal.vue:205 frontend/src/pages/MobileDeal.vue:149
msgid "Primary"
msgstr "Primarni"

#. Label of the email (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Email"
msgstr "Primarna e-pošta"

#. Label of the mobile_no (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Mobile No"
msgstr "Primarni broj mobitela"

#. Label of the phone (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Phone"
msgstr "Primarni Telefon"

#: frontend/src/pages/Deal.vue:679 frontend/src/pages/MobileDeal.vue:566
msgid "Primary contact set"
msgstr "Primarni kontakt postavljen"

#. Label of the priorities (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Priorities"
msgstr "Prioriteti"

#. Label of the priority (Link) field in DocType 'CRM Service Level Priority'
#. Label of the priority (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:64
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:20
msgid "Priority"
msgstr "Prioritet"

#. Label of the private (Check) field in DocType 'CRM Dashboard'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:89
msgid "Private"
msgstr "Privatno"

#. Label of the probability (Percent) field in DocType 'CRM Deal'
#. Label of the probability (Percent) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Probability"
msgstr "Vjerovatnoća"

#. Label of the product_code (Link) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product"
msgstr "Proizvod"

#. Label of the product_code (Data) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Product Code"
msgstr "Šifra Stavke"

#. Label of the product_name (Data) field in DocType 'CRM Product'
#. Label of the product_name (Data) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product Name"
msgstr "Naziv Stavke"

#. Label of the products_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the products (Table) field in DocType 'CRM Deal'
#. Label of the products_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the products (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Products"
msgstr "Proizvodi"

#: frontend/src/components/Layouts/AppSidebar.vue:540
#: frontend/src/components/Settings/Settings.vue:94
msgid "Profile"
msgstr "Profil"

#: frontend/src/components/Settings/ProfileSettings.vue:147
msgid "Profile updated successfully"
msgstr "Profil je uspješno ažuriran"

#: crm/api/dashboard.py:689
msgid "Projected vs actual revenue based on deal probability"
msgstr "Predviđeni u odnosu na stvarni prihod na temelju vjerojatnosti posla"

#. Label of the public (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Public"
msgstr "Javno"

#: frontend/src/components/ViewControls.vue:672
msgid "Public Views"
msgstr "Javni Prikazi"

#: frontend/src/components/Layouts/AppSidebar.vue:570
msgid "Public view"
msgstr "Javni Prikaz"

#. Label of the qty (Float) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Quantity"
msgstr "Količina"

#. Label of the questions (Table) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Questions"
msgstr "Pitanja"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Queued"
msgstr "U Redu"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Quick Entry"
msgstr "Brzi Unos"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Quick Filters"
msgstr "Brzi Filteri"

#: frontend/src/components/ViewControls.vue:731
msgid "Quick Filters updated successfully"
msgstr "Brzi Filtri uspješno ažurirani"

#: frontend/src/components/Layouts/AppSidebar.vue:594
msgid "Quick entry layout"
msgstr "Raspored za brzi unos"

#. Label of the rate (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Rate"
msgstr "Cijena"

#. Label of the read (Check) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Read"
msgstr "Čitaj"

#: crm/api/dashboard.py:922
msgid "Reason"
msgstr "Razlog"

#. Label of the record_calls (Check) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Record Calls"
msgstr "Snimaj Poziv"

#. Label of the record_call (Check) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Record Outgoing Calls"
msgstr "Snimaj Odlazne Pozive"

#. Label of the recording_url (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Recording URL"
msgstr "URL Snimanja"

#. Label of the reference_name (Dynamic Link) field in DocType 'CRM
#. Notification'
#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Task'
#. Label of the reference_docname (Dynamic Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Doc"
msgstr "Referentni Dokument"

#. Label of the reference_doctype (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Reference Doctype"
msgstr "Referentni Doctype"

#. Label of the reference_doctype (Link) field in DocType 'CRM Call Log'
#. Label of the reference_doctype (Link) field in DocType 'CRM Task'
#. Label of the reference_doctype (Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Document Type"
msgstr "Referentna vrsta dokumenta"

#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Call
#. Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Reference Name"
msgstr "Referentni Naziv"

#: frontend/src/components/ViewControls.vue:26
#: frontend/src/components/ViewControls.vue:159
#: frontend/src/pages/Dashboard.vue:10
msgid "Refresh"
msgstr "Osvježi"

#: frontend/src/components/Telephony/TwilioCallUI.vue:97
msgid "Reject"
msgstr "Odbij"

#: frontend/src/components/Telephony/TwilioCallUI.vue:163
msgid "Reject call"
msgstr "Odbij poziv"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:192
#: frontend/src/components/Controls/ImageUploader.vue:25
#: frontend/src/components/Settings/Users.vue:218
#: frontend/src/pages/Deal.vue:628
msgid "Remove"
msgstr "Ukloni"

#: frontend/src/components/FilesUploader/FilesUploader.vue:23
msgid "Remove all"
msgstr "Ukloni sve"

#: frontend/src/components/FieldLayoutEditor.vue:438
msgid "Remove and move fields to previous column"
msgstr "Ukloni i premjesti polja u prethodni stupac"

#: frontend/src/components/FieldLayoutEditor.vue:432
msgid "Remove column"
msgstr "Ukloni kolonu"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:200
msgid "Remove group"
msgstr "Ukloni grupu"

#: frontend/src/components/Settings/ProfileSettings.vue:32
#: frontend/src/pages/Contact.vue:53 frontend/src/pages/Lead.vue:94
#: frontend/src/pages/MobileContact.vue:43
#: frontend/src/pages/MobileOrganization.vue:43
#: frontend/src/pages/Organization.vue:53
msgid "Remove image"
msgstr "Ukloni sliku"

#: frontend/src/components/FieldLayoutEditor.vue:359
msgid "Remove section"
msgstr "Ukloni sekciju"

#: frontend/src/components/FieldLayoutEditor.vue:318
msgid "Remove tab"
msgstr "Ukloni karticu"

#: frontend/src/components/Activities/EmailArea.vue:34
#: frontend/src/components/CommunicationArea.vue:10
msgid "Reply"
msgstr "Odgovor"

#: frontend/src/components/Activities/EmailArea.vue:41
msgid "Reply All"
msgstr "Odgovori Svima"

#: frontend/src/components/Modals/AboutModal.vue:72
msgid "Report an Issue"
msgstr "Prijavi Slučaj"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Required Fields"
msgstr "Obavezna Polja"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:79
#: frontend/src/components/Controls/GridRowFieldsModal.vue:30
#: frontend/src/components/Modals/DataFieldsModal.vue:30
#: frontend/src/components/Modals/QuickEntryModal.vue:30
#: frontend/src/components/Modals/SidePanelModal.vue:30
msgid "Reset"
msgstr "Poništi"

#: frontend/src/components/ColumnSettings.vue:78
msgid "Reset Changes"
msgstr "Poništi Promjene"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:7
msgid "Reset ERPNext Form Script"
msgstr "Poništi Skriptu Obrasca Sistema"

#: frontend/src/components/ColumnSettings.vue:86
msgid "Reset to Default"
msgstr "Vrati na Zadano"

#: frontend/src/pages/Dashboard.vue:28
msgid "Reset to default"
msgstr "Vrati na Standard"

#. Label of the response_by (Datetime) field in DocType 'CRM Deal'
#. Label of the response_by (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response By"
msgstr "Odgovor od"

#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Deal'
#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response Details"
msgstr "Detalji Odgovora"

#. Label of the section_break_ufaf (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Response and Follow Up"
msgstr "Odgovor i Praćenje"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:14
msgid "Restore"
msgstr "Vrati"

#. Label of the restore_defaults (Button) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:13
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Restore Defaults"
msgstr "Vrati Zadane Postavke"

#: frontend/src/components/FilesUploader/FilesUploader.vue:51
msgid "Retake"
msgstr "Ponovi"

#: crm/api/dashboard.py:697
msgid "Revenue"
msgstr "Prihod"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:81
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:81
msgid "Rich Text"
msgstr "Rich Text"

#. Label of the rgt (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Right"
msgstr "Desno"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Ringing"
msgstr "Zvoni"

#: frontend/src/components/Telephony/TwilioCallUI.vue:38
#: frontend/src/components/Telephony/TwilioCallUI.vue:140
msgid "Ringing..."
msgstr "Zvoni..."

#. Label of the role (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:46
msgid "Role"
msgstr "Uloga"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#. Label of the route (Data) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Route"
msgstr "Ruta"

#. Label of the route_name (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Route Name"
msgstr "Naziv Rute"

#. Label of the rows (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Rows"
msgstr "Redovi"

#. Label of the sla_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the sla (Link) field in DocType 'CRM Deal'
#. Label of the sla_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the sla (Link) field in DocType 'CRM Lead'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "SLA"
msgstr "Standard Nivo Ugovor"

#. Label of the sla_creation (Datetime) field in DocType 'CRM Deal'
#. Label of the sla_creation (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Creation"
msgstr "Standard Nivo Ugovora Stvaranje"

#. Label of the sla_name (Data) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "SLA Name"
msgstr "Naziv Standard Nivo Ugovora"

#. Label of the sla_status (Select) field in DocType 'CRM Deal'
#. Label of the sla_status (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Status"
msgstr "Status Standard Nivo Ugovora"

#: frontend/src/components/EmailEditor.vue:85
msgid "SUBJECT"
msgstr "PREDMET"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Sales Manager"
msgstr "Upravitelj Prodaje"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:92
#: frontend/src/components/Settings/InviteUserPage.vue:156
#: frontend/src/components/Settings/InviteUserPage.vue:163
#: frontend/src/components/Settings/Users.vue:88
#: frontend/src/components/Settings/Users.vue:194
#: frontend/src/components/Settings/Users.vue:264
#: frontend/src/components/Settings/Users.vue:267
msgid "Sales User"
msgstr "Korisnik Prodaje"

#: crm/api/dashboard.py:617
#: frontend/src/components/Dashboard/AddChartModal.vue:94
msgid "Sales trend"
msgstr "Prodajni Trend"

#: frontend/src/pages/Dashboard.vue:93
msgid "Sales user"
msgstr "Korisnik Prodaje"

#: crm/api/dashboard.py:1127
msgid "Salesperson"
msgstr "Prodavač"

#. Label of the salutation (Link) field in DocType 'CRM Deal'
#. Label of the salutation (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Salutation"
msgstr "Titula"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Saturday"
msgstr "Subota"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:345
#: frontend/src/components/Controls/GridFieldsEditorModal.vue:84
#: frontend/src/components/Controls/GridRowFieldsModal.vue:26
#: frontend/src/components/Modals/AddressModal.vue:98
#: frontend/src/components/Modals/CallLogModal.vue:101
#: frontend/src/components/Modals/DataFieldsModal.vue:26
#: frontend/src/components/Modals/LostReasonModal.vue:44
#: frontend/src/components/Modals/QuickEntryModal.vue:26
#: frontend/src/components/Modals/SidePanelModal.vue:26
#: frontend/src/components/PrimaryDropdownItem.vue:21
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:36
#: frontend/src/components/Settings/CurrencySettings.vue:173
#: frontend/src/components/Telephony/ExotelCallUI.vue:222
#: frontend/src/components/ViewControls.vue:125
#: frontend/src/pages/Dashboard.vue:36
msgid "Save"
msgstr "Spremi"

#: frontend/src/components/Modals/ViewModal.vue:40
#: frontend/src/components/ViewControls.vue:58
#: frontend/src/components/ViewControls.vue:155
msgid "Save Changes"
msgstr "Spremi Promjene"

#: frontend/src/components/ViewControls.vue:667
msgid "Saved Views"
msgstr "Spremljeni Prikazi"

#: frontend/src/components/Layouts/AppSidebar.vue:569
msgid "Saved view"
msgstr "Sačuvani prikaz"

#: frontend/src/components/Telephony/TaskPanel.vue:8
msgid "Schedule a task..."
msgstr "Zakaži zadatak..."

#: frontend/src/components/Activities/EventArea.vue:79
msgid "Schedule an Event"
msgstr "Zakaži Događaj"

#: frontend/src/components/Activities/ActivityHeader.vue:36
#: frontend/src/components/Activities/ActivityHeader.vue:128
msgid "Schedule an event"
msgstr "Zakaži Događaj"

#. Label of the script (Code) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Script"
msgstr "Skripta"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:22
msgid "Search"
msgstr "Traži"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:64
msgid "Search template"
msgstr "Predložak pretraživanja"

#: frontend/src/components/Settings/Users.vue:73
msgid "Search user"
msgstr "Korisnik Pretrage"

#: frontend/src/components/FieldLayoutEditor.vue:336
msgid "Section"
msgstr "Odjeljak"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:130
msgid "See all participants"
msgstr "Vidi sve sudionike"

#: frontend/src/pages/Dashboard.vue:50
msgid "Select Range"
msgstr "Odaberi raspon"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:47
msgid "Select Source Type"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:51
msgid "Select currency"
msgstr "Odaberi valutu"

#: frontend/src/components/Settings/CurrencySettings.vue:75
msgid "Select provider"
msgstr "Odaberi pružatelja usluga"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:84
msgid "Select the assignees for {0}."
msgstr "Odaberi dodijeljene osobe za {0}."

#: frontend/src/components/FieldLayout/Field.vue:345
msgid "Select {0}"
msgstr "Odaberi {0}"

#: frontend/src/components/EmailEditor.vue:165
msgid "Send"
msgstr "Pošalji"

#: frontend/src/components/Settings/InviteUserPage.vue:18
msgid "Send Invites"
msgstr "Pošalji Pozivnice"

#: frontend/src/components/Activities/ActivityHeader.vue:61
msgid "Send Template"
msgstr "Pošalji Predložak"

#: frontend/src/pages/Deal.vue:87 frontend/src/pages/Lead.vue:134
msgid "Send an email"
msgstr "Pošalji e-poštu"

#: frontend/src/components/Layouts/AppSidebar.vue:460
msgid "Send email"
msgstr "Pošalji e-poštu"

#: frontend/src/components/Settings/InviteUserPage.vue:6
msgid "Send invites to"
msgstr "Pošalji pozivnice"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Separator"
msgstr "Razdjelnik"

#. Label of the naming_series (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Series"
msgstr "Numeričke Serije"

#. Label of the service_provider (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Service Provider"
msgstr "Dobavljač Servisa"

#: frontend/src/components/Layouts/AppSidebar.vue:581
msgid "Service level agreement"
msgstr "Ugovor Razine Usluga"

#: frontend/src/components/PrimaryDropdownItem.vue:27
msgid "Set As Primary"
msgstr "Postavi kao primarno"

#: frontend/src/components/FilesUploader/FilesUploader.vue:66
msgid "Set all as private"
msgstr "Postavi sve kao privatno"

#: frontend/src/components/FilesUploader/FilesUploader.vue:59
msgid "Set all as public"
msgstr "Postavi sve kao javno"

#: frontend/src/pages/Deal.vue:73
msgid "Set an organization"
msgstr "Postavi organizaciju"

#: frontend/src/pages/Deal.vue:636 frontend/src/pages/MobileDeal.vue:523
msgid "Set as Primary Contact"
msgstr "Postavi kao Primarni Kontakt"

#: frontend/src/components/ViewControls.vue:1078
msgid "Set as default"
msgstr "Postavi kao Standard"

#: frontend/src/components/Settings/CurrencySettings.vue:164
msgid "Set currency"
msgstr "Postavi valutu"

#: frontend/src/pages/Lead.vue:115
msgid "Set first name"
msgstr "Postavi Ime"

#: frontend/src/components/Layouts/AppSidebar.vue:533
msgid "Setting up"
msgstr "Postavljanje"

#: frontend/src/components/Settings/emailConfig.js:145
msgid "Setting up Frappe Mail requires you to have an API key and API Secret of your email account. Read more "
msgstr "Za postavljanje Frappe Mail-a potrebno je da imate API Ključ i API Tajnu za račun e-pošte. Pročitaj više. "

#: frontend/src/components/Settings/emailConfig.js:97
msgid "Setting up GMail requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "Za postavljanjeGmaila potrebno je omogućiti dvofaktorsku autentifikaciju\n"
"\t\t  i lozinke specifične za aplikacije. Pročitaj više"

#: frontend/src/components/Settings/emailConfig.js:105
msgid "Setting up Outlook requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "Za postavljanje Outlooka potrebno je omogućiti dvofaktorsku autentifikaciju\n"
"\t\t  i lozinke specifične za aplikacije. Pročitaj više"

#: frontend/src/components/Settings/emailConfig.js:113
msgid "Setting up Sendgrid requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Za postavljanje Sendgrida potrebno je omogućiti dvofaktorsku autentifikaciju\n"
"\t\t  i lozinke specifične za aplikaciju. Pročitaj više "

#: frontend/src/components/Settings/emailConfig.js:121
msgid "Setting up SparkPost requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Za postavljanje SparkPosta potrebno je omogućiti dvofaktorsku autentifikaciju\n"
"\t\t  i lozinke specifične za aplikaciju. Pročitaj više "

#: frontend/src/components/Settings/emailConfig.js:129
msgid "Setting up Yahoo requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Za postavljanje Yahoo-a potrebno je omogućiti dvofaktorsku autentifikaciju\n"
"\t\t  i lozinke specifične za aplikaciju. Pročitaj više "

#: frontend/src/components/Settings/emailConfig.js:137
msgid "Setting up Yandex requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Za postavljanje Yandexa potrebno je omogućiti dvofaktorsku autentifikaciju\n"
"\t\t  i lozinke specifične za aplikaciju. Pročitaj više "

#. Label of the defaults_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Layouts/AppSidebar.vue:537
#: frontend/src/components/Settings/Settings.vue:12
msgid "Settings"
msgstr "Postavke"

#: frontend/src/components/Settings/EmailAdd.vue:6
msgid "Setup Email"
msgstr "Postavljanje e-pošte"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:199
msgid "Setup the Exchange Rate Provider as 'Exchangerate Host' in settings, as default provider does not support currency conversion for {0} to {1}."
msgstr "Postavite Pružatelja Usluge Tečaja kao 'Pružitelja Usluge Tečaja' u postavkama, jer zadani pružatelj ne podržava pretvorbu valuta iz {0} u {1}."

#: frontend/src/components/Layouts/AppSidebar.vue:339
msgid "Setup your password"
msgstr "Postavi lozinku"

#: frontend/src/components/Activities/Activities.vue:237
msgid "Show"
msgstr "Prikaži"

#: frontend/src/components/Controls/Password.vue:19
msgid "Show Password"
msgstr "Prikaži lozinku"

#: frontend/src/components/FieldLayoutEditor.vue:354
msgid "Show border"
msgstr "Prikaži Okvir"

#: frontend/src/components/FieldLayoutEditor.vue:349
msgid "Show label"
msgstr "Prikaži oznaku"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:138
msgid "Show less"
msgstr "Prikaži manje"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Show preview"
msgstr "Prikaži pregled"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Side Panel"
msgstr "Bočna ploča"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Sidebar Items"
msgstr "Stavke bočne trake"

#. Description of the 'Condition' (Code) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Simple Python Expression, Example: doc.status == 'Open' and doc.lead_source == 'Ads'"
msgstr "Jednostavan Python Izraz, Primjer: doc.status == 'Open' i doc.lead_source == 'Ads'"

#: frontend/src/components/AssignTo.vue:83
msgid "Since you removed {0} from the assignee, the {0} has also been removed."
msgstr "Pošto ste uklonili {0} od dodijeljenog, uklonjen je i {0}."

#: frontend/src/components/AssignTo.vue:76
msgid "Since you removed {0} from the assignee, the {0} has been changed to the next available assignee {1}."
msgstr "Pošto ste uklonili {0} od dodijeljenog, {0} je promijenjeno na sljedećeg dostupnog dodijeljenog {1}."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:735
msgid "Some error occurred while renaming assignment rule"
msgstr "Došlo je do pogreške prilikom preimenovanja pravila dodjele"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:720
msgid "Some error occurred while updating assignment rule"
msgstr "Došlo je do pogreške prilikom ažuriranja pravila dodjele"

#: frontend/src/components/SortBy.vue:10 frontend/src/components/SortBy.vue:24
#: frontend/src/components/SortBy.vue:232
msgid "Sort"
msgstr "Sortiraj"

#. Label of the source (Link) field in DocType 'CRM Deal'
#. Label of the source (Link) field in DocType 'CRM Lead'
#. Label of the source (Link) field in DocType 'Failed Lead Sync Log'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: frontend/src/components/Modals/EditValueModal.vue:10
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:60
msgid "Source"
msgstr "Izvor"

#. Label of the source_name (Data) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:69
msgid "Source Name"
msgstr "Naziv Izvora"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:46
msgid "Source Type"
msgstr "Tip Izvora"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:167
msgid "Source disabled successfully"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:166
msgid "Source enabled successfully"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:68
#: frontend/src/components/Dashboard/DashboardItem.vue:21
msgid "Spacer"
msgstr "Razmak"

#: crm/api/dashboard.py:758 crm/api/dashboard.py:820
msgid "Stage"
msgstr "Faza"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:15
msgid "Standard Form Scripts can not be modified, duplicate the Form Script instead."
msgstr "Standardne Skripte Obrasca ne mogu se mijenjati, umjesto toga duplicirajte Skriptu Obrasca."

#. Label of the standard_rate (Currency) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Standard Selling Rate"
msgstr "Standardna Prodajna Cijena"

#: frontend/src/components/ViewControls.vue:634
msgid "Standard Views"
msgstr "Standard Prikazi"

#. Label of the start_date (Date) field in DocType 'CRM Service Level
#. Agreement'
#. Label of the start_date (Date) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Start Date"
msgstr "Start Datum"

#. Label of the start_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the start_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:234
#: frontend/src/components/Modals/EventModal.vue:103
msgid "Start Time"
msgstr "Vrijeme Početka"

#: frontend/src/composables/event.js:198
msgid "Start and end time are required"
msgstr "Vrijeme početka i završetka je obavezno"

#: frontend/src/pages/Welcome.vue:21
msgid "Start with sample 10 leads"
msgstr "Započni s uzorkom od 10 potencijalnih klijenata"

#. Label of the status (Select) field in DocType 'CRM Call Log'
#. Label of the status (Data) field in DocType 'CRM Communication Status'
#. Label of the status (Link) field in DocType 'CRM Deal'
#. Label of the deal_status (Data) field in DocType 'CRM Deal Status'
#. Label of the status (Select) field in DocType 'CRM Invitation'
#. Label of the status (Link) field in DocType 'CRM Lead'
#. Label of the lead_status (Data) field in DocType 'CRM Lead Status'
#. Label of the status (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_task/crm_task.json frontend/src/pages/Contact.vue:513
#: frontend/src/pages/MobileContact.vue:513
#: frontend/src/pages/MobileOrganization.vue:457
#: frontend/src/pages/Organization.vue:487
msgid "Status"
msgstr "Status"

#. Label of the status_change_log (Table) field in DocType 'CRM Deal'
#. Label of the status_change_log (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Status Change Log"
msgstr "Zapisnik Promjena Statusa"

#: frontend/src/components/Modals/DealModal.vue:216
#: frontend/src/components/Modals/LeadModal.vue:157
msgid "Status is required"
msgstr "Status je obavezan"

#. Label of the subdomain (Data) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Subdomain"
msgstr "Poddomena"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:68
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:68
msgid "Subject"
msgstr "Predmet"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:155
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:158
msgid "Subject is required"
msgstr "Predmet je obavezan"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:45
msgid "Subject: {0}"
msgstr "Predmet: {0}"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Sunday"
msgstr "Nedjelja"

#: frontend/src/components/Settings/emailConfig.js:16
msgid "Support / Sales"
msgstr "Podrška / Prodaja"

#: frontend/src/components/FilesUploader/FilesUploader.vue:46
msgid "Switch camera"
msgstr "Promijeni kameru"

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.js:6
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:23
msgid "Sync Now"
msgstr "Sinhronizuj Sad"

#: frontend/src/pages/Welcome.vue:32
msgid "Sync your contacts,email and calenders"
msgstr "Sinhronizuj kontakte, e-poštu i kalendare"

#. Label of the syncing_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Syncing"
msgstr "Sinhronizacija u toku"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:359
msgid "Syncing started in background"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:106
msgid "System Configuration"
msgstr "Konfiguracija Sustava"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "System Manager"
msgstr "Upravitelj Sustava"

#: frontend/src/components/EmailEditor.vue:22
msgid "TO"
msgstr "ZA"

#: frontend/src/components/Telephony/ExotelCallUI.vue:149
msgid "Take a note..."
msgstr "Zabilježi..."

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:555
msgid "Task"
msgstr "Zadatak"

#: frontend/src/pages/Deal.vue:567 frontend/src/pages/Lead.vue:414
#: frontend/src/pages/MobileDeal.vue:456 frontend/src/pages/MobileLead.vue:362
msgid "Tasks"
msgstr "Zadaci"

#: frontend/src/components/Modals/AboutModal.vue:67
msgid "Telegram Channel"
msgstr "Telegram Kanal"

#: frontend/src/components/Settings/Settings.vue:185
msgid "Telephony"
msgstr "Telefonija"

#. Label of the telephony_medium (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Telephony Medium"
msgstr "Medij Telefonije"

#: frontend/src/components/Settings/TelephonySettings.vue:8
msgid "Telephony settings"
msgstr "Postavke Telefonije"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:175
msgid "Template created successfully"
msgstr "Predložak je uspješno kreiran"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:213
msgid "Template deleted successfully"
msgstr "Predložak je uspješno izbrisan"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:197
msgid "Template disabled successfully"
msgstr "Predložak je uspješno onemogućen"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:196
msgid "Template enabled successfully"
msgstr "Predložak je uspješno omogućen"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:83
msgid "Template name"
msgstr "Naziv predloška"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:239
msgid "Template renamed successfully"
msgstr "Predložak je uspješno preimenovan"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:204
msgid "Template updated successfully"
msgstr "Predložak je uspješno ažuriran"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Territories"
msgstr "Distrikti"

#. Label of the territory (Link) field in DocType 'CRM Deal'
#. Label of the territory (Link) field in DocType 'CRM Lead'
#. Label of the territory (Link) field in DocType 'CRM Organization'
#: crm/api/dashboard.py:1067 crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Territory"
msgstr "Distrikt"

#. Label of the territory_manager (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Manager"
msgstr "Upravitelj Distrikta"

#. Label of the territory_name (Data) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Name"
msgstr "Naziv Distrikta"

#: crm/templates/emails/helpdesk_invitation.html:16
msgid "Thanks"
msgstr "Hvala"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:46
msgid "The Condition '{0}' is invalid: {1}"
msgstr "Uvjet '{0}' je nevažeći: {1}"

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "The rate used to convert the deal’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "Tečaj koji se koristi za pretvaranje valute posla u osnovnu valutu vašeg CRM-a (postavljeno u postavkama CRM-a). Postavlja se jednom prilikom prvog dodavanja valute i ne mijenja se automatski."

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "The rate used to convert the organization’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "Tečaj koji se koristi za pretvaranje valute organizacije u osnovnu valutu vašeg CRM-a (postavljeno u postavkama CRM-a). Postavlja se jednom prilikom prvog dodavanja valute i ne mijenja se automatski."

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.js:14
msgid "There can only be one default priority in Priorities table"
msgstr "U tablici prioriteta može postojati samo jedan zadani prioritet"

#: frontend/src/components/Modals/AddressModal.vue:128
#: frontend/src/components/Modals/CallLogModal.vue:131
msgid "These fields are required: {0}"
msgstr "Ova polja su obavezna: {0}"

#: frontend/src/components/Filter.vue:639
msgid "This Month"
msgstr "Ovaj Mjesec"

#: frontend/src/components/Filter.vue:643
msgid "This Quarter"
msgstr "Ovo Tromjesečje"

#: frontend/src/components/Filter.vue:635
msgid "This Week"
msgstr "Ovaj Tjedan"

#: frontend/src/components/Filter.vue:647
msgid "This Year"
msgstr "Ove Godine"

#: frontend/src/components/SidePanelLayoutEditor.vue:116
msgid "This section is not editable"
msgstr "Ovaj odjeljak nije moguće uređivati"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:59
msgid "This will delete selected items and items linked to it, are you sure?"
msgstr "Ovim ćete izbrisati odabrane stavke i stavke povezane s njima. Jeste li sigurni?"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:62
msgid "This will delete selected items and unlink linked items to it, are you sure?"
msgstr "Ovim ćete izbrisati odabrane stavke i prekinuti vezu s njima. Jeste li sigurni?"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:9
msgid "This will restore (if not exist) all the default statuses, custom fields and layouts. Delete & Restore will delete default layouts and then restore them."
msgstr "Ovo će vratiti (ako ne postoje) sve zadane statuse, prilagođena polja i izglede. Izbriši & Vrati će izbrisati zadane izglede i zatim ih vratiti."

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Thursday"
msgstr "Četvrtak"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:227
msgid "Time"
msgstr "Vrijeme"

#: frontend/src/components/Filter.vue:351
msgid "Timespan"
msgstr "Vremenski Razmak"

#. Label of the title (Data) field in DocType 'CRM Task'
#. Label of the title (Data) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/EventModal.vue:43
#: frontend/src/components/Modals/NoteModal.vue:26
#: frontend/src/components/Modals/TaskModal.vue:25
msgid "Title"
msgstr "Naziv"

#. Label of the title_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:29
msgid "Title Field"
msgstr "Polje naslova"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:545
#: frontend/src/components/Modals/EventModal.vue:320
msgid "Title is required"
msgstr "Naziv je obavezan"

#. Label of the to (Data) field in DocType 'CRM Call Log'
#. Label of the to (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
#: frontend/src/components/Activities/EmailArea.vue:53
msgid "To"
msgstr "Do"

#. Label of the to_date (Date) field in DocType 'CRM Holiday List'
#. Label of the to_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Date"
msgstr "Do Datuma"

#. Label of the to_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Type"
msgstr "Do Tipa"

#. Label of the to_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "To User"
msgstr "Za korisnika"

#: frontend/src/components/Settings/EmailEdit.vue:118
msgid "To know more about setting up email accounts, click"
msgstr "Za više informacija o postavljanju računa e-pošte kliknite"

#: frontend/src/components/Filter.vue:627 frontend/src/pages/Calendar.vue:79
msgid "Today"
msgstr "Danas"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Todo"
msgstr "Za Uraditi"

#: frontend/src/components/Modals/SidePanelModal.vue:59
msgid "Toggle on for preview"
msgstr "Uključi radi pregleda"

#: frontend/src/components/Filter.vue:631
msgid "Tomorrow"
msgstr "Sutra"

#: frontend/src/components/Modals/NoteModal.vue:42
#: frontend/src/components/Modals/TaskModal.vue:46
msgid "Took a call with John Doe and discussed the new project."
msgstr "Poziv sa John Doeom i razgovor o novom projektu."

#. Label of the total (Currency) field in DocType 'CRM Deal'
#. Label of the total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total"
msgstr "Ukupno"

#. Label of the total_holidays (Int) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Total Holidays"
msgstr "Ukupno Praznika"

#. Description of the 'Net Total' (Currency) field in DocType 'CRM Deal'
#. Description of the 'Net Total' (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total after discount"
msgstr "Ukupno nakon popusta"

#: crm/api/dashboard.py:129
#: frontend/src/components/Dashboard/AddChartModal.vue:76
msgid "Total leads"
msgstr "Ukupan broj potencijalnih klijenata"

#: crm/api/dashboard.py:130
msgid "Total number of leads"
msgstr "Ukupan broj potencijalnih klijenata"

#: crm/api/dashboard.py:191
msgid "Total number of non won/lost deals"
msgstr "Ukupan broj izgubljenih poslova"

#: crm/api/dashboard.py:311
msgid "Total number of won deals based on its closure date"
msgstr "Ukupan broj dobivenih poslova na temelju datuma zaključenja"

#. Label of the traceback (Code) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Traceback"
msgstr "Povratno praćenje"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Tuesday"
msgstr "Utorak"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:173
msgid "Turn into a group"
msgstr "Pretvori u grupu"

#. Label of the twiml_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "TwiML SID"
msgstr "TwiML SID"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the twilio (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:601
#: frontend/src/components/Settings/TelephonySettings.vue:40
#: frontend/src/components/Settings/TelephonySettings.vue:50
msgid "Twilio"
msgstr "Twilio"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:59
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:60
msgid "Twilio API credential creation error."
msgstr "Pogreška pri stvaranju vjerodajnice za Twilio API."

#. Label of the twilio_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Twilio Number"
msgstr "Twilio Broj"

#: frontend/src/components/Settings/TelephonySettings.vue:289
msgid "Twilio is not enabled"
msgstr "Twilio nije omogućen"

#: frontend/src/components/Settings/TelephonySettings.vue:125
msgid "Twilio settings updated successfully"
msgstr "Twilio postavke uspješno ažurirane"

#. Label of the type (Select) field in DocType 'CRM Call Log'
#. Label of the type (Select) field in DocType 'CRM Deal Status'
#. Label of the type (Select) field in DocType 'CRM Dropdown Item'
#. Label of the type (Select) field in DocType 'CRM Fields Layout'
#. Label of the type (Select) field in DocType 'CRM Global Settings'
#. Label of the type (Select) field in DocType 'CRM Notification'
#. Label of the type (Select) field in DocType 'CRM View Settings'
#. Label of the type (Data) field in DocType 'Facebook Lead Form Question'
#. Label of the type (Select) field in DocType 'Failed Lead Sync Log'
#. Label of the type (Select) field in DocType 'Lead Sync Source'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Type"
msgstr "Tip"

#: frontend/src/components/Calendar/Attendee.vue:233
msgid "Type an email address to add attendee"
msgstr "Upišite adresu e-pošte za dodavanje sudionika"

#: frontend/src/components/Activities/WhatsAppBox.vue:85
msgid "Type your message here..."
msgstr "Ovdje upiši svoju poruku..."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:454
msgid "Unassign conditions are invalid"
msgstr "Uvjeti poništavanja dodjele nisu važeći"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:224
msgid "Unassignment condition"
msgstr "Uslov za oduzimanje dodjele"

#: crm/integrations/exotel/handler.py:170
msgid "Unauthorized request"
msgstr "Neovlašteni zahtjev"

#: frontend/src/components/FieldLayoutEditor.vue:344
msgid "Uncollapsible"
msgstr "Nesklopivo"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:183
msgid "Ungroup conditions"
msgstr "Uvjeti razgrupiranja"

#: frontend/src/components/Telephony/TwilioCallUI.vue:24
#: frontend/src/components/Telephony/TwilioCallUI.vue:123
msgid "Unknown"
msgstr "Nepoznat"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:132
msgid "Unlink"
msgstr "Prekini vezu"

#: frontend/src/components/DeleteLinkedDocModal.vue:78
msgid "Unlink all"
msgstr "Prekini vezu sa svim"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
msgid "Unlink and delete"
msgstr "Prekini vezu i izbriši"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:35
msgid "Unlink and delete {0} items"
msgstr "Prekini vezu i izbriši {0} stavki"

#: frontend/src/components/DeleteLinkedDocModal.vue:243
msgid "Unlink linked item"
msgstr "Prekini vezu povezane stavke"

#: frontend/src/components/DeleteLinkedDocModal.vue:79
msgid "Unlink {0} item(s)"
msgstr "Prekini vezu {0} stavki"

#: frontend/src/components/ViewControls.vue:1093
msgid "Unpin View"
msgstr "Otkači Prikaz"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:22
msgid "Unsaved"
msgstr "Nespremljeno"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:575
msgid "Unsaved changes"
msgstr "Nespremljene promjene"

#: frontend/src/components/FieldLayoutEditor.vue:26
#: frontend/src/components/Modals/AddressModal.vue:8
#: frontend/src/components/Modals/CallLogModal.vue:8
#: frontend/src/components/Modals/CreateDocumentModal.vue:8
#: frontend/src/components/Section.vue:21
#: frontend/src/components/SidePanelLayoutEditor.vue:19
msgid "Untitled"
msgstr "Neimenovano"

#: frontend/src/components/ColumnSettings.vue:129
#: frontend/src/components/Modals/AssignmentModal.vue:79
#: frontend/src/components/Modals/ChangePasswordModal.vue:45
#: frontend/src/components/Modals/EventModal.vue:156
#: frontend/src/components/Modals/NoteModal.vue:52
#: frontend/src/components/Modals/TaskModal.vue:106
#: frontend/src/components/Settings/BrandSettings.vue:15
#: frontend/src/components/Settings/CurrencySettings.vue:17
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:21
#: frontend/src/components/Settings/HomeActions.vue:15
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:30
#: frontend/src/components/Settings/ProfileSettings.vue:95
#: frontend/src/components/Settings/SettingsPage.vue:20
#: frontend/src/components/Settings/TelephonySettings.vue:23
#: frontend/src/components/Telephony/ExotelCallUI.vue:210
msgid "Update"
msgstr "Ažuriraj"

#: frontend/src/components/Settings/EmailEdit.vue:74
msgid "Update Account"
msgstr "Ažuriraj račun"

#: frontend/src/components/Modals/EditValueModal.vue:30
msgid "Update {0} Records"
msgstr "Ažuriraj {0} Zapisa"

#: frontend/src/components/Controls/ImageUploader.vue:20
#: frontend/src/components/FilesUploader/FilesUploader.vue:83
msgid "Upload"
msgstr "Učitaj"

#: frontend/src/components/Activities/Activities.vue:408
#: frontend/src/components/Activities/ActivityHeader.vue:55
#: frontend/src/components/Activities/ActivityHeader.vue:154
msgid "Upload Attachment"
msgstr "Učitaj Prilog"

#: frontend/src/components/Activities/WhatsAppBox.vue:132
msgid "Upload Document"
msgstr "Učitaj Dokument"

#: frontend/src/components/Activities/WhatsAppBox.vue:140
msgid "Upload Image"
msgstr "Učitaj Sliku"

#: frontend/src/components/Activities/WhatsAppBox.vue:148
msgid "Upload Video"
msgstr "Učitaj Video"

#: frontend/src/components/Settings/ProfileSettings.vue:27
#: frontend/src/pages/Contact.vue:48 frontend/src/pages/Lead.vue:89
#: frontend/src/pages/MobileContact.vue:38
#: frontend/src/pages/MobileOrganization.vue:38
#: frontend/src/pages/Organization.vue:48
msgid "Upload image"
msgstr "Učitaj Sliku"

#: frontend/src/components/Controls/ImageUploader.vue:17
msgid "Uploading {0}%"
msgstr "Prijenos {0}%"

#. Label of the user (Link) field in DocType 'CRM Dashboard'
#. Label of the user (Link) field in DocType 'CRM Telephony Agent'
#. Label of the user (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "User"
msgstr "Korisnik"

#: frontend/src/components/Settings/Settings.vue:127
msgid "User Management"
msgstr "Upravljanje Korisnicima"

#. Label of the user_name (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "User Name"
msgstr "Korisničko ime"

#: frontend/src/components/Settings/Users.vue:296
msgid "User {0} has been removed"
msgstr "Korisnik {0} je uklonjen"

#: frontend/src/components/Modals/AddExistingUserModal.vue:20
#: frontend/src/components/Settings/Settings.vue:130
#: frontend/src/components/Settings/Users.vue:7
msgid "Users"
msgstr "Korisnici"

#: frontend/src/components/Modals/AddExistingUserModal.vue:105
msgid "Users added successfully"
msgstr "Korisnici su uspješno dodani"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:465
msgid "Users are required"
msgstr "Korisnici su obavezni"

#. Label of the section_break_nevd (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Validity"
msgstr "Validnost"

#: frontend/src/components/Modals/EditValueModal.vue:14
msgid "Value"
msgstr "Vrijednost"

#: frontend/src/components/Modals/ViewModal.vue:14
msgid "View Name"
msgstr "Prikaz Naziv"

#: frontend/src/pages/Deal.vue:219
msgid "View contact"
msgstr "Prikaži kontakt"

#: frontend/src/components/Layouts/AppSidebar.vue:566
msgid "Views"
msgstr "Pogledi"

#: frontend/src/components/Layouts/AppSidebar.vue:563
msgid "Web form"
msgstr "Web obrazac"

#. Label of the webhook_verify_token (Data) field in DocType 'CRM Exotel
#. Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Webhook Verify Token"
msgstr "Webhook Verify Token"

#. Label of the website (Data) field in DocType 'CRM Deal'
#. Label of the website (Data) field in DocType 'CRM Lead'
#. Label of the website (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: frontend/src/components/Modals/AboutModal.vue:52
msgid "Website"
msgstr "Web Stranica"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Wednesday"
msgstr "Srijeda"

#. Label of the weekly_off (Check) field in DocType 'CRM Holiday'
#. Label of the weekly_off (Select) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Weekly Off"
msgstr "Neradni Dan"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:11
msgid "Welcome Message"
msgstr "Poruka Dobrodošlice"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:155
msgid "Welcome to Helpdesk"
msgstr "Dobro došli u Helpdesk"

#: frontend/src/pages/Welcome.vue:4
msgid "Welcome {0}, lets add your first lead"
msgstr "Dobrodošli {0}, dodajmo vašeg prvog potencijalnog klijenta"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:603
#: frontend/src/components/Settings/Settings.vue:191
#: frontend/src/pages/Deal.vue:582 frontend/src/pages/Lead.vue:429
#: frontend/src/pages/MobileDeal.vue:471 frontend/src/pages/MobileLead.vue:377
msgid "WhatsApp"
msgstr "WhatsApp"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:4
msgid "WhatsApp Templates"
msgstr "WhatsApp Predlošci"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:20
#: frontend/src/components/Filter.vue:43 frontend/src/components/Filter.vue:81
msgid "Where"
msgstr "Gdje"

#: frontend/src/components/ColumnSettings.vue:108
msgid "Width"
msgstr "Širina"

#: frontend/src/components/ColumnSettings.vue:113
msgid "Width can be in number, pixel or rem (eg. 3, 30px, 10rem)"
msgstr "Širina može biti u broju, pikselu ili remu (npr. 3, 30 piksela, 10 rema)"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Won"
msgstr "Dobiven"

#: crm/api/dashboard.py:310
#: frontend/src/components/Dashboard/AddChartModal.vue:79
msgid "Won deals"
msgstr "Dobiveni poslovi"

#. Label of the workday (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Workday"
msgstr "Radni Dan"

#. Label of the section_break_rmgo (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#. Label of the working_hours (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Working Hours"
msgstr "Radno Vrijeme"

#: frontend/src/components/Filter.vue:623
msgid "Yesterday"
msgstr "Jučer"

#: crm/api/whatsapp.py:43 crm/api/whatsapp.py:223 crm/api/whatsapp.py:237
#: frontend/src/components/Activities/WhatsAppArea.vue:34
#: frontend/src/components/Activities/WhatsAppBox.vue:14
msgid "You"
msgstr "Vi"

#: crm/utils/__init__.py:262
msgid "You are not permitted to access this resource."
msgstr "Nije vam dozvoljen pristup ovom resursu."

#: crm/templates/emails/helpdesk_invitation.html:22
msgid "You can also copy-paste following link in your browser"
msgstr "Takođ možete kopirati i zalijepiti sljedeću vezu u svoj preglednik"

#: frontend/src/components/Telephony/CallUI.vue:39
msgid "You can change the default calling medium from the settings"
msgstr "Zadani medij za pozivanje možete promijeniti u postavkama"

#: frontend/src/components/Settings/CurrencySettings.vue:100
msgid "You can get your access key from "
msgstr "Pristupni ključ možete dobiti od "

#: frontend/src/components/Settings/InviteUserPage.vue:36
msgid "You can invite multiple users by comma separating their email addresses"
msgstr "Možete pozvati više korisnika odvajanjem njihovih adresa e-pošte zarezom"

#: crm/integrations/exotel/handler.py:85
msgid "You do not have Exotel Number set in your Telephony Agent"
msgstr "Nemate Exotel broj postavljen u svom telefonskom agentu"

#: crm/integrations/exotel/handler.py:93
msgid "You do not have mobile number set in your Telephony Agent"
msgstr "Nemate postavljen broj mobilnog telefona u svom Telefonskom Agentu"

#: frontend/src/data/document.js:34
msgid "You do not have permission to access this document"
msgstr "Nemate dopuštenje za pristup ovom dokumentu"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.py:24
msgid "You need to be in developer mode to edit a Standard Form Script"
msgstr "Morate biti u načinu rada razvojnog programera da biste uredili skriptu standardnog obrasca"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:150
msgid "You will be redirected to invite user page, unsaved changes will be lost."
msgstr "Bit ćete preusmjereni na stranicu za pozivanje korisnika, nespremljene promjene bit će izgubljene."

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:9
msgid "You will need a Meta developer account and an access token to sync leads from Facebook. Read more "
msgstr ""

#: crm/api/todo.py:100
msgid "Your assignment on task {0} has been removed by {1}"
msgstr "Vaša dodjela za zadatak {0} uklonio je {1}"

#: crm/api/todo.py:37 crm/api/todo.py:78
msgid "Your assignment on {0} {1} has been removed by {2}"
msgstr "Vašu dodjelu {0} {1} je uklonio {2}"

#: crm/templates/emails/helpdesk_invitation.html:6
msgid "Your login id is"
msgstr "Vaš Id za prijavu je"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:610
msgid "Your old condition will be overwritten. Are you sure you want to save?"
msgstr "Vaši stari uslovi će biti prepisani. Jeste li sigurni da želite spremiti?"

#: frontend/src/components/Activities/CommentArea.vue:9
msgid "added a"
msgstr "dodao"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "amber"
msgstr "jantar"

#: crm/api/todo.py:109
msgid "assigned a new task {0} to you"
msgstr "dodijelio ti je novi zadatak {0}"

#: crm/api/todo.py:89
msgid "assigned a {0} {1} to you"
msgstr "dodijelio ti je {0} {1}"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "black"
msgstr "crna"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "blue"
msgstr "plava"

#: frontend/src/components/Activities/Activities.vue:239
msgid "changes from"
msgstr "promjene od"

#: frontend/src/components/Activities/CommentArea.vue:11
msgid "comment"
msgstr "komentar"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:65
#: frontend/src/components/ConditionsFilter/CFCondition.vue:73
msgid "condition"
msgstr "uvjet"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "cyan"
msgstr "cijan"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:131
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:380
msgid "deals"
msgstr "poslovi"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:190
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:274
msgid "desk"
msgstr "radni Prostor"

#: frontend/src/components/Calendar/Attendee.vue:295
#: frontend/src/components/Controls/EmailMultiSelect.vue:254
msgid "email already exists"
msgstr "e-pošta već postoji"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/CurrencySettings.vue:106
msgid "exchangerate.host"
msgstr "exchangerate.host"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "frankfurter.app"
msgstr "frankfurter.app"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "gray"
msgstr "sivo"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "green"
msgstr "zeleno"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "group_by"
msgstr "grupiraj_po"

#: frontend/src/components/Activities/CallArea.vue:16
msgid "has made a call"
msgstr "uputio je poziv"

#: frontend/src/components/Activities/CallArea.vue:15
msgid "has reached out"
msgstr "je kontaktirao"

#: frontend/src/components/Settings/EmailAdd.vue:36
#: frontend/src/components/Settings/EmailEdit.vue:25
msgid "here"
msgstr "ovdje"

#: frontend/src/utils/index.js:146
msgid "in 1 hour"
msgstr "za 1 sat"

#: frontend/src/utils/index.js:142
msgid "in 1 minute"
msgstr "za 1 minutu"

#: frontend/src/utils/index.js:160
msgid "in 1 year"
msgstr "za 1 godinu"

#: frontend/src/utils/index.js:111
msgid "in {0} M"
msgstr "za {0} M"

#: frontend/src/utils/index.js:107
msgid "in {0} d"
msgstr "za {0} d"

#: frontend/src/utils/index.js:154
msgid "in {0} days"
msgstr "za {0} dana"

#: frontend/src/utils/index.js:101
msgid "in {0} h"
msgstr "za {0} s"

#: frontend/src/utils/index.js:148
msgid "in {0} hours"
msgstr "za {0} sati"

#: frontend/src/utils/index.js:99
msgid "in {0} m"
msgstr "za {0} m"

#: frontend/src/utils/index.js:144
msgid "in {0} minutes"
msgstr "za {0} minuta"

#: frontend/src/utils/index.js:158
msgid "in {0} months"
msgstr "za {0} mjeseci"

#: frontend/src/utils/index.js:109
msgid "in {0} w"
msgstr "za {0} t"

#: frontend/src/utils/index.js:156
msgid "in {0} weeks"
msgstr "za {0} tjedana"

#: frontend/src/utils/index.js:113
msgid "in {0} y"
msgstr "za {0} g"

#: frontend/src/utils/index.js:162
msgid "in {0} years"
msgstr "za {0} godina"

#: frontend/src/components/Modals/AddExistingUserModal.vue:28
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: frontend/src/utils/index.js:140 frontend/src/utils/index.js:166
msgid "just now"
msgstr "upravo sada"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "kanban"
msgstr "oglasna Tabla"

#: crm/api/doc.py:40 crm/api/doc.py:158 crm/api/doc.py:503
msgid "label"
msgstr "oznaka"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:130
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:379
msgid "leads"
msgstr "potencijalni Klijenti"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "list"
msgstr "lista"

#: crm/api/comment.py:36 frontend/src/components/Notifications.vue:59
#: frontend/src/pages/MobileNotification.vue:46
msgid "mentioned you in {0}"
msgstr "spomenuo vas je u {0}"

#: frontend/src/components/FieldLayoutEditor.vue:368
msgid "next"
msgstr "sljedeći"

#: frontend/src/utils/index.js:97 frontend/src/utils/index.js:117
msgid "now"
msgstr "sad"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:47
msgid "operator"
msgstr "operater"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "orange"
msgstr "narandžasta"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "pink"
msgstr "roze"

#: frontend/src/components/FieldLayoutEditor.vue:368
msgid "previous"
msgstr "prethodno"

#: frontend/src/components/Activities/AttachmentArea.vue:97
msgid "private"
msgstr "privatno"

#: frontend/src/components/Activities/AttachmentArea.vue:97
msgid "public"
msgstr "javno"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "purple"
msgstr "ljubičasta"

#: crm/api/whatsapp.py:44
msgid "received a whatsapp message in {0}"
msgstr "primio je WhatsApp poruku u {0}"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "red"
msgstr "crveno"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "teal"
msgstr "plavozelena"

#: frontend/src/components/Activities/Activities.vue:278
#: frontend/src/components/Activities/Activities.vue:341
msgid "to"
msgstr "do"

#: frontend/src/utils/index.js:105 frontend/src/utils/index.js:152
msgid "tomorrow"
msgstr "sutra"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "violet"
msgstr "ljubičasta"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:193
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:277
msgid "which are not compatible with this UI, you will need to recreate the conditions here if you want to manage and add new conditions from this UI."
msgstr "koji nisu kompatibilni s ovim korisničkim sučeljem, morat ćete ponovno stvoriti uvjete ovdje ako želite upravljati i dodavati nove uvjete iz ovog korisničkog sučelja."

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "yellow"
msgstr "žuta"

#: frontend/src/utils/index.js:179
msgid "yesterday"
msgstr "juče"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:94
msgid "{0} Attendees"
msgstr "{0} Sudionici"

#: frontend/src/utils/index.js:130
msgid "{0} M"
msgstr "{0} M"

#: crm/api/todo.py:41
msgid "{0} assigned a {1} {2} to you"
msgstr "{0} ti je dodijelio {1} {2}"

#: frontend/src/utils/index.js:126
msgid "{0} d"
msgstr "{0} d"

#: frontend/src/utils/index.js:181
msgid "{0} days ago"
msgstr "{0} dana prije"

#: frontend/src/utils/index.js:121
msgid "{0} h"
msgstr "{0} h"

#: frontend/src/components/Settings/Users.vue:286
msgid "{0} has been granted {1} access"
msgstr "{0} je dobio/la {1} pristup"

#: frontend/src/utils/index.js:174
msgid "{0} hours ago"
msgstr "{0} sati prije"

#: frontend/src/composables/event.js:163
msgid "{0} hrs"
msgstr "{0} sati"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:309
#: frontend/src/components/EmailEditor.vue:30
#: frontend/src/components/EmailEditor.vue:66
#: frontend/src/components/EmailEditor.vue:80
#: frontend/src/components/Modals/AddExistingUserModal.vue:37
#: frontend/src/components/Modals/EventModal.vue:127
msgid "{0} is an invalid email address"
msgstr "{0} je nevažeća adresa e-pošte"

#: frontend/src/components/Modals/ConvertToDealModal.vue:172
msgid "{0} is required"
msgstr "{0} je obavezan"

#: frontend/src/utils/index.js:119
msgid "{0} m"
msgstr "{0} m"

#: frontend/src/composables/event.js:157
msgid "{0} mins"
msgstr "{0} minuta"

#: frontend/src/utils/index.js:170
msgid "{0} minutes ago"
msgstr "prije {0} minuta"

#: frontend/src/utils/index.js:189
msgid "{0} months ago"
msgstr "{0} mjeseci prije"

#: frontend/src/utils/index.js:128
msgid "{0} w"
msgstr "{0} w"

#: frontend/src/utils/index.js:185
msgid "{0} weeks ago"
msgstr "prije {0} tjedana"

#: frontend/src/utils/index.js:132
msgid "{0} y"
msgstr "{0} g"

#: frontend/src/utils/index.js:193
msgid "{0} years ago"
msgstr "{0} godina prije"

#: frontend/src/data/script.js:326
msgid "⚠️ Avoid using \"trigger\" as a field name — it conflicts with the built-in trigger() method."
msgstr "⚠️ Izbjegavaj korištenje \"trigger\" kao naziva polja — to je u sukobu s ugrađenom metodom trigger()."

#: frontend/src/data/script.js:338
msgid "⚠️ Method \"{0}\" not found in class."
msgstr "⚠️ Metoda \"{0}\" nije pronađena u klasi."

#: frontend/src/data/script.js:83
msgid "⚠️ No class found for doctype: {0}, it is mandatory to have a class for the parent doctype. it can be empty, but it should be present."
msgstr "⚠️ Nije pronađena klasa za doctype: {0}, obavezno je imati klasu za nadređeni doctype. Može biti prazna, ali treba biti prisutna."

#: frontend/src/data/script.js:180
msgid "⚠️ No data found for parent field: {0}"
msgstr "⚠️ Nisu pronađeni podaci za nadređeno polje: {0}"

#: frontend/src/data/script.js:188
msgid "⚠️ No row found for idx: {0} in parent field: {1}"
msgstr "⚠️ Nije pronađen redak za idx: {0} u nadređenom polju: {1}"

