msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-11-09 09:35+0000\n"
"PO-Revision-Date: 2025-11-10 10:46\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Indonesian\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: id\n"
"X-Crowdin-File: /[frappe.crm] develop/crm/locale/main.pot\n"
"X-Crowdin-File-ID: 97\n"
"Language: id_ID\n"

#: frontend/src/components/ViewControls.vue:1172
msgid " (New)"
msgstr " (Baru)"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:58
msgid "(No title)"
msgstr "(Tidak ada judul)"

#: frontend/src/components/Modals/TaskModal.vue:87
#: frontend/src/components/Telephony/TaskPanel.vue:70
msgid "01/04/2024 11:30 PM"
msgstr "04/01/2024 23:30"

#: frontend/src/utils/index.js:172
msgid "1 hour ago"
msgstr "1 jam yang lalu"

#: frontend/src/composables/event.js:163
msgid "1 hr"
msgstr "1 jam"

#: frontend/src/utils/index.js:168
msgid "1 minute ago"
msgstr "1 menit yang lalu"

#: frontend/src/utils/index.js:187
msgid "1 month ago"
msgstr "1 bulan lalu"

#: frontend/src/utils/index.js:183
msgid "1 week ago"
msgstr "1 minggu yang lalu"

#: frontend/src/utils/index.js:191
msgid "1 year ago"
msgstr "1 tahun yang lalu"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1-10"
msgstr "1-10"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1000+"
msgstr "1000+"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "11-50"
msgstr "11-50"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "201-500"
msgstr "201-500"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "501-1000"
msgstr "501-1000"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "51-200"
msgstr "51-200"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>META</b>"
msgstr "<b>META</b>"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>SHORTCUTS</b>"
msgstr "<b>PINTASAN</b>"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:95
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:95
msgid "<p>Dear {{ lead_name }},</p>\\n\\n<p>This is a reminder for the payment of {{ grand_total }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappé</p>"
msgstr "<p>Yang Terhormat {{ lead_name }},</p>\\n\\n<p>Dengan ini, kami ingin mengingatkan mengenai pembayaran sebesar {{ grand_total }}.</p>\\n\\n<p>Hormat kami,</p>\\n<p>Frappé</p>"

#. Header text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<span class=\"h5\"><b>PORTAL</b></span>"
msgstr "<span class=\"h5\"><b>PORTAL</b></span>"

#: frontend/src/components/CommunicationArea.vue:79
msgid "@John, can you please check this?"
msgstr "@John, tolong periksa ini, ya?"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:103
msgid "A Lead requires either a person's name or an organization's name"
msgstr "Sebuah Prospek harus memiliki nama orang atau nama organisasi"

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.py:47
msgid "A lead sync source is already enabled for this Facebook Lead Form!"
msgstr ""

#: crm/templates/emails/helpdesk_invitation.html:5
msgid "A new account has been created for you at {0}"
msgstr "Sebuah akun baru telah dibuat untuk Anda di {0}"

#. Label of the api_key (Data) field in DocType 'CRM Exotel Settings'
#. Label of the api_key (Data) field in DocType 'CRM Twilio Settings'
#. Label of the api_key (Data) field in DocType 'ERPNext CRM Settings'
#. Label of the api_key (Data) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "API Key"
msgstr "API Key"

#: frontend/src/components/Settings/emailConfig.js:179
msgid "API Key is required"
msgstr "API Key diperlukan"

#. Label of the api_secret (Password) field in DocType 'CRM Twilio Settings'
#. Label of the api_secret (Password) field in DocType 'ERPNext CRM Settings'
#. Label of the api_secret (Password) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "API Secret"
msgstr "API Secret"

#. Label of the api_token (Password) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "API Token"
msgstr "API Token"

#: frontend/src/components/Telephony/TwilioCallUI.vue:88
msgid "Accept"
msgstr "Terima"

#: crm/fcrm/doctype/crm_invitation/crm_invitation.js:7
msgid "Accept Invitation"
msgstr "Terima Undangan"

#: frontend/src/components/Telephony/TwilioCallUI.vue:155
msgid "Accept call"
msgstr "Terima Panggilan"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted"
msgstr "Diterima"

#. Label of the accepted_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted At"
msgstr "Diterima pada"

#. Label of the access_key (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Access Key"
msgstr "Kunci Akses"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:156
msgid "Access Key is required for Service Provider: {0}"
msgstr "Kunci Akses diperlukan untuk Penyedia Layanan: {0}"

#. Label of the access_token (Small Text) field in DocType 'Facebook Page'
#. Label of the access_token (Password) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:87
#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:28
msgid "Access Token"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:90
msgid "Access key"
msgstr "Kunci akses"

#: frontend/src/components/Settings/CurrencySettings.vue:94
msgid "Access key for Exchangerate Host. Required for fetching exchange rates."
msgstr "Kunci akses untuk Exchangerate Host. Diperlukan untuk mengambil nilai tukar."

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:133
msgid "Access token is required"
msgstr ""

#. Label of the account_id (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Account ID"
msgstr ""

#: frontend/src/components/Settings/emailConfig.js:13
msgid "Account Name"
msgstr "Nama Akun"

#. Label of the account_sid (Data) field in DocType 'CRM Exotel Settings'
#. Label of the account_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Account SID"
msgstr "SID Akun"

#: frontend/src/components/Settings/emailConfig.js:165
msgid "Account name is required"
msgstr "Nama akun wajib diisi"

#: frontend/src/components/CustomActions.vue:69
#: frontend/src/components/ViewControls.vue:683
#: frontend/src/components/ViewControls.vue:1064
msgid "Actions"
msgstr "Tindakan"

#: frontend/src/pages/Deal.vue:537 frontend/src/pages/Lead.vue:384
#: frontend/src/pages/MobileDeal.vue:430 frontend/src/pages/MobileLead.vue:337
msgid "Activity"
msgstr "Aktivitas"

#: frontend/src/components/Dashboard/AddChartModal.vue:41
#: frontend/src/components/Modals/AddExistingUserModal.vue:55
msgid "Add"
msgstr "Tambah"

#: frontend/src/components/Settings/EmailAccountList.vue:19
msgid "Add Account"
msgstr "Tambah Akun"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:9
msgid "Add Assignee"
msgstr ""

#: frontend/src/components/ColumnSettings.vue:68
#: frontend/src/components/Kanban/KanbanView.vue:157
msgid "Add Column"
msgstr "Tambahkan Kolom"

#: frontend/src/components/Modals/AddExistingUserModal.vue:4
#: frontend/src/components/Settings/Users.vue:21
msgid "Add Existing User"
msgstr "Tambah Pengguna Terdaftar"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:57
#: frontend/src/components/FieldLayoutEditor.vue:172
#: frontend/src/components/Kanban/KanbanSettings.vue:80
#: frontend/src/components/SidePanelLayoutEditor.vue:97
msgid "Add Field"
msgstr "Tambah Bidang"

#: frontend/src/components/Filter.vue:136
msgid "Add Filter"
msgstr "Tambah Filter"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:273
msgid "Add Lead or Deal"
msgstr ""

#: frontend/src/components/Controls/Grid.vue:334
msgid "Add Row"
msgstr "Tambah Baris"

#: frontend/src/components/FieldLayoutEditor.vue:197
#: frontend/src/components/SidePanelLayoutEditor.vue:127
msgid "Add Section"
msgstr "Tambah Bagian"

#: frontend/src/components/SortBy.vue:142
msgid "Add Sort"
msgstr "Tambah Urutan"

#: frontend/src/components/FieldLayoutEditor.vue:62
msgid "Add Tab"
msgstr "Tambah Tab"

#. Label of the add_weekly_holidays_section (Section Break) field in DocType
#. 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add Weekly Holidays"
msgstr "Tambah Hari Libur Mingguan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:20
msgid "Add a condition"
msgstr "Tambahkan ketentuan"

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:24
msgid "Add a name for your source"
msgstr ""

#: frontend/src/components/Telephony/ExotelCallUI.vue:183
#: frontend/src/components/Telephony/TwilioCallUI.vue:57
msgid "Add a note"
msgstr "Tambah catatan"

#: frontend/src/components/Telephony/ExotelCallUI.vue:191
msgid "Add a task"
msgstr "Tambah Tugas"

#: frontend/src/components/Dashboard/AddChartModal.vue:4
msgid "Add chart"
msgstr "Tambah bagan"

#: frontend/src/components/FieldLayoutEditor.vue:420
msgid "Add column"
msgstr "Tambah kolom"

#: frontend/src/components/ConditionsFilter/CFConditions.vue:23
#: frontend/src/components/ConditionsFilter/CFConditions.vue:82
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:28
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:64
msgid "Add condition"
msgstr "Tambahkan ketentuan"

#: frontend/src/components/ConditionsFilter/CFConditions.vue:91
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:70
msgid "Add condition group"
msgstr "Tambahkan grup ketentuan"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:325
msgid "Add description"
msgstr "Tambah deskripsi"

#: frontend/src/components/Modals/EventModal.vue:142
msgid "Add description."
msgstr "Tambah deskripsi."

#: frontend/src/components/Telephony/TaskPanel.vue:17
msgid "Add description..."
msgstr "Tambah deskripsi..."

#: frontend/src/components/Modals/AddExistingUserModal.vue:12
msgid "Add existing system users to this CRM. Assign them a role to grant access with their current credentials."
msgstr "Tambahkan pengguna sistem yang ada ke CRM ini. Berikan peran untuk memberi mereka akses dengan kredensial mereka saat ini."

#: frontend/src/components/ViewControls.vue:107
msgid "Add filter"
msgstr "Tambah filter"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Add note"
msgstr "Tambah catatan"

#: frontend/src/pages/Welcome.vue:24
msgid "Add sample data"
msgstr "Tambah data sampel"

#. Description of the 'Icon' (Code) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Add svg code or use feather icons e.g 'settings'"
msgstr "Tambahkan kode svg atau gunakan ikon feather, misalnya 'settings'"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Add task"
msgstr "Tambah tugas"

#. Label of the add_to_holidays (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add to Holidays"
msgstr "Tambahkan ke Hari Libur"

#: frontend/src/components/Layouts/AppSidebar.vue:439
msgid "Add your first comment"
msgstr "Tambahkan komentar pertama Anda"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:11
msgid "Add, edit, and manage email templates for various CRM communications"
msgstr "Tambah, edit, dan kelola templat email untuk berbagai komunikasi CRM"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:12
msgid "Add, edit, and manage sources for automatic lead syncing to your CRM"
msgstr ""

#. Label of the address (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Address"
msgstr "Alamat"

#: frontend/src/components/Modals/AddExistingUserModal.vue:94
#: frontend/src/components/Settings/InviteUserPage.vue:158
#: frontend/src/components/Settings/InviteUserPage.vue:165
#: frontend/src/components/Settings/Users.vue:86
#: frontend/src/components/Settings/Users.vue:126
#: frontend/src/components/Settings/Users.vue:192
#: frontend/src/components/Settings/Users.vue:242
#: frontend/src/components/Settings/Users.vue:245
msgid "Admin"
msgstr "Admin"

#: crm/integrations/twilio/twilio_handler.py:144
msgid "Agent is unavailable to take the call, please call after some time."
msgstr "Agen saat ini tidak dapat menerima panggilan, silakan hubungi kembali beberapa saat lagi."

#. Name of a role
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:76
#: frontend/src/components/Settings/Users.vue:85
msgid "All"
msgstr "Semua"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:191
#: frontend/src/components/Calendar/CalendarEventPanel.vue:641
#: frontend/src/components/Modals/EventModal.vue:71
#: frontend/src/composables/event.js:122
msgid "All day"
msgstr "Sepanjang hari"

#. Label of the amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
#: frontend/src/pages/Contact.vue:507 frontend/src/pages/MobileContact.vue:507
#: frontend/src/pages/MobileOrganization.vue:451
#: frontend/src/pages/Organization.vue:481
msgid "Amount"
msgstr "Jumlah"

#. Description of the 'Net Amount' (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Amount after discount"
msgstr "Jumlah setelah diskon"

#: frontend/src/data/script.js:50 frontend/src/data/script.js:51
msgid "An error occurred"
msgstr "Terjadi kesalahan"

#: frontend/src/data/document.js:66
msgid "An error occurred while updating the document"
msgstr "Terjadi kesalahan saat memperbarui dokumen"

#. Description of the 'Favicon' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org]"
msgstr "File ikon dengan ekstensi .ico. Harus berukuran 16 x 16 piksel. Dibuat menggunakan generator favicon. [favicon-generator.org]"

#. Description of the 'Logo' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An image with 1:1 & 2:1 ratio is preferred"
msgstr "Gambar dengan rasio 1:1 & 2:1 lebih diutamakan"

#: frontend/src/components/Filter.vue:43 frontend/src/components/Filter.vue:81
msgid "And"
msgstr "Dan"

#. Label of the annual_revenue (Currency) field in DocType 'CRM Deal'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Lead'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Annual Revenue"
msgstr "Pendapatan Tahunan"

#: frontend/src/components/Modals/DealModal.vue:200
#: frontend/src/components/Modals/LeadModal.vue:141
msgid "Annual Revenue should be a number"
msgstr "Pendapatan Tahunan harus berupa angka"

#: frontend/src/components/Settings/BrandSettings.vue:55
msgid "Appears in the left sidebar. Recommended size is 32x32 px in PNG or SVG"
msgstr "Tampil di bilah sisi kiri. Ukuran yang disarankan 32x32 piksel dalam format PNG atau SVG"

#: frontend/src/components/Settings/BrandSettings.vue:90
msgid "Appears next to the title in your browser tab. Recommended size is 32x32 px in PNG or ICO"
msgstr "Tampil di sebelah judul pada tab browser Anda. Ukuran yang disarankan 32x32 piksel dalam format PNG atau ICO"

#: frontend/src/components/Kanban/KanbanSettings.vue:101
#: frontend/src/components/Kanban/KanbanView.vue:46
msgid "Apply"
msgstr "Terapkan"

#. Label of the apply_on (Link) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Apply On"
msgstr "Terapkan Pada"

#. Label of the view (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Apply To"
msgstr "Terapkan Ke"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:125
msgid "Apply on"
msgstr ""

#: frontend/src/components/Apps.vue:19
msgid "Apps"
msgstr "Aplikasi"

#: frontend/src/components/Activities/AttachmentArea.vue:128
msgid "Are you sure you want to delete this attachment?"
msgstr "Apakah Anda yakin ingin menghapus lampiran ini?"

#: frontend/src/pages/MobileContact.vue:260
msgid "Are you sure you want to delete this contact?"
msgstr "Apakah Anda yakin ingin menghapus kontak ini?"

#: frontend/src/components/Modals/EventModal.vue:408
#: frontend/src/pages/Calendar.vue:300
msgid "Are you sure you want to delete this event?"
msgstr "Anda yakin ingin menghapus event ini?"

#: frontend/src/pages/MobileOrganization.vue:261
msgid "Are you sure you want to delete this organization?"
msgstr "Apakah Anda yakin ingin menghapus organisasi ini?"

#: frontend/src/components/Activities/TaskArea.vue:60
msgid "Are you sure you want to delete this task?"
msgstr "Apakah Anda yakin ingin menghapus tugas ini?"

#: frontend/src/components/DeleteLinkedDocModal.vue:231
msgid "Are you sure you want to delete {0} linked item(s)?"
msgstr "Apakah Anda yakin ingin menghapus {0} item tertaut?"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:615
msgid "Are you sure you want to discard unsaved changes to this event?"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:576
msgid "Are you sure you want to go back? Unsaved changes will be lost."
msgstr ""

#: frontend/src/composables/frappecloud.js:24
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "Apakah Anda yakin ingin masuk ke dasbor Frappe Cloud Anda?"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:9
msgid "Are you sure you want to reset 'Create Quotation from CRM Deal' Form Script?"
msgstr "Apakah Anda yakin ingin mereset Skrip Formulir 'Create Quotation from CRM Deal'?"

#: frontend/src/components/Settings/CurrencySettings.vue:165
msgid "Are you sure you want to set the currency as {0}? This cannot be changed later."
msgstr "Apakah Anda yakin ingin mengatur mata uang sebagai {0}? Pengaturan Ini tidak dapat diubah di kemudian hari."

#: frontend/src/components/DeleteLinkedDocModal.vue:244
msgid "Are you sure you want to unlink {0} linked item(s)?"
msgstr "Apakah Anda yakin ingin melepas tautan {0} item tertaut?"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:193
msgid "Ask your manager to set up the Exchange Rate Provider, as default provider does not support currency conversion for {0} to {1}."
msgstr "Minta manajer Anda untuk mengatur Penyedia Nilai Tukar, karena penyedia default tidak mendukung konversi mata uang untuk {0} ke {1}."

#: frontend/src/components/ListBulkActions.vue:186
#: frontend/src/components/Modals/AssignmentModal.vue:4
msgid "Assign To"
msgstr "Tugaskan Ke"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:435
msgid "Assign condition is required"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:438
msgid "Assign conditions are invalid"
msgstr ""

#: frontend/src/components/AssignTo.vue:11
#: frontend/src/components/AssignToBody.vue:5
msgid "Assign to"
msgstr "Tugaskan ke"

#: frontend/src/components/AssignToBody.vue:63
msgid "Assign to me"
msgstr "Tetapkan ke saya"

#. Label of the assigned_to (Link) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Assigned To"
msgstr "Ditugaskan Kepada"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:5
msgid "Assignee Rules"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:81
msgid "Assignees"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Assignment"
msgstr "Penugasan"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Assignment Rule"
msgstr "Aturan Penugasan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:302
msgid "Assignment Schedule"
msgstr ""

#: frontend/src/components/ListBulkActions.vue:154
msgid "Assignment cleared successfully"
msgstr "Penugasan berhasil dibersihkan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:145
msgid "Assignment condition"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:471
msgid "Assignment days are required"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:582
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:19
msgid "Assignment rule"
msgstr "Aturan penugasan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:667
msgid "Assignment rule created"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:111
msgid "Assignment rule deleted"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:156
msgid "Assignment rule duplicated"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:749
msgid "Assignment rule updated"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:188
msgid "Assignment rule {0} updated"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:7
#: frontend/src/components/Settings/Settings.vue:164
msgid "Assignment rules"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:11
msgid "Assignment rules automatically assign lead/deal to the right sales user based on predefined conditions"
msgstr ""

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:173
msgid "At least one field is required"
msgstr "Setidaknya satu bidang wajib diisi"

#: frontend/src/components/FilesUploader/FilesUploader.vue:5
#: frontend/src/components/FilesUploader/FilesUploader.vue:73
msgid "Attach"
msgstr "Lampirkan"

#: frontend/src/components/CommentBox.vue:65
#: frontend/src/components/EmailEditor.vue:146 frontend/src/pages/Deal.vue:105
#: frontend/src/pages/Lead.vue:151
msgid "Attach a file"
msgstr "Lampirkan file"

#: frontend/src/pages/Deal.vue:577 frontend/src/pages/Lead.vue:424
#: frontend/src/pages/MobileDeal.vue:466 frontend/src/pages/MobileLead.vue:372
msgid "Attachments"
msgstr "Lampiran"

#: frontend/src/components/Modals/EventModal.vue:120
msgid "Attendees"
msgstr "Peserta"

#. Label of the auth_token (Password) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Auth Token"
msgstr "Auth Token"

#. Label of the auto_update_expected_deal_value (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Auto Update Expected Deal Value"
msgstr ""

#: frontend/src/components/Settings/ForecastingSettings.vue:42
msgid "Auto update expected deal value"
msgstr ""

#: frontend/src/components/Settings/ForecastingSettings.vue:88
msgid "Auto update of expected deal value disabled"
msgstr ""

#: frontend/src/components/Settings/ForecastingSettings.vue:87
msgid "Auto update of expected deal value enabled"
msgstr ""

#. Description of the 'Auto Update Expected Deal Value' (Check) field in
#. DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/ForecastingSettings.vue:46
msgid "Automatically update \"Expected Deal Value\" based on the total value of associated products in a deal"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:161
msgid "Automation & Rules"
msgstr ""

#: crm/api/dashboard.py:250
msgid "Average deal value of non won/lost deals"
msgstr "Nilai rata-rata kesepakatan yang sedang berjalan"

#: crm/api/dashboard.py:429
msgid "Average deal value of ongoing & won deals"
msgstr "Nilai rata-rata kesepakatan yang sedang berjalan & berhasil"

#: crm/api/dashboard.py:370
msgid "Average deal value of won deals"
msgstr "Nilai rata-rata kesepakatan yang berhasil"

#: crm/api/dashboard.py:534
msgid "Average time taken from deal creation to deal closure"
msgstr "Waktu rata-rata yang dibutuhkan dari pembuatan kesepakatan hingga penutupan kesepakatan"

#: crm/api/dashboard.py:481
msgid "Average time taken from lead creation to deal closure"
msgstr "Waktu rata-rata yang dibutuhkan dari pembuatan prospek hingga penutupan kesepakatan"

#: frontend/src/components/Dashboard/AddChartModal.vue:81
msgid "Avg deal value"
msgstr "Nilai rata-rata kesepakatan"

#: frontend/src/components/Dashboard/AddChartModal.vue:78
msgid "Avg ongoing deal value"
msgstr "Rerata nilai kesepakatan yang sedang berjalan"

#: frontend/src/components/Dashboard/AddChartModal.vue:87
msgid "Avg time to close a deal"
msgstr "Rerata waktu untuk menutup kesepakatan"

#: frontend/src/components/Dashboard/AddChartModal.vue:83
msgid "Avg time to close a lead"
msgstr "Rerata waktu menutup prospek"

#: frontend/src/components/Dashboard/AddChartModal.vue:80
msgid "Avg won deal value"
msgstr "Rerata nilai kesepakatan yang berhasil"

#: crm/api/dashboard.py:428
msgid "Avg. deal value"
msgstr "Rerata nilai kesepakatan"

#: crm/api/dashboard.py:249
msgid "Avg. ongoing deal value"
msgstr "Rerata nilai kesepakatan yang sedang berjalan"

#: crm/api/dashboard.py:533
msgid "Avg. time to close a deal"
msgstr "Rerata waktu untuk menutup kesepakatan"

#: crm/api/dashboard.py:480
msgid "Avg. time to close a lead"
msgstr "Rerata waktu untuk menutup prospek"

#: crm/api/dashboard.py:369
msgid "Avg. won deal value"
msgstr "Rerata nilai kesepakatan yang berhasil"

#: frontend/src/components/Dashboard/AddChartModal.vue:26
#: frontend/src/components/Dashboard/AddChartModal.vue:70
msgid "Axis chart"
msgstr "Bagan sumbu"

#: frontend/src/components/Activities/EmailArea.vue:62
#: frontend/src/components/EmailEditor.vue:45
#: frontend/src/components/EmailEditor.vue:71
msgid "BCC"
msgstr "BCC"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
#: frontend/src/components/Settings/EmailAdd.vue:79
#: frontend/src/components/Settings/EmailEdit.vue:67
msgid "Back"
msgstr "Kembali"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
msgid "Back to file upload"
msgstr "Kembali ke unggahan file"

#. Label of the background_sync_frequency (Select) field in DocType 'Lead Sync
#. Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:80
msgid "Background Sync Frequency"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Backlog"
msgstr "Backlog"

#: frontend/src/components/Filter.vue:350
msgid "Between"
msgstr "Antara"

#: frontend/src/components/Settings/Settings.vue:119
msgid "Brand Settings"
msgstr ""

#: frontend/src/components/Settings/BrandSettings.vue:52
msgid "Brand logo"
msgstr ""

#: frontend/src/components/Settings/BrandSettings.vue:32
msgid "Brand name"
msgstr "Nama brand"

#: frontend/src/components/Settings/BrandSettings.vue:7
msgid "Brand settings"
msgstr "Pengaturan brand"

#. Label of the branding_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Branding"
msgstr "Branding"

#: frontend/src/components/Modals/EditValueModal.vue:2
msgid "Bulk Edit"
msgstr "Edit Massal"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Busy"
msgstr "Sibuk"

#: frontend/src/components/Activities/EmailArea.vue:57
#: frontend/src/components/EmailEditor.vue:35
#: frontend/src/components/EmailEditor.vue:57
msgid "CC"
msgstr "CC"

#. Name of a DocType
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "CRM Call Log"
msgstr "CRM Log Panggilan"

#. Name of a DocType
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
msgid "CRM Communication Status"
msgstr "CRM Status Komunikasi"

#. Name of a DocType
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
msgid "CRM Contacts"
msgstr "CRM Kontak"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/pages/Dashboard.vue:304
msgid "CRM Dashboard"
msgstr "CRM Dasbor"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "CRM Deal"
msgstr "CRM Kesepakatan"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "CRM Deal Status"
msgstr "CRM Status Kesepakatan"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "CRM Dropdown Item"
msgstr "CRM Item Dropdown"

#. Name of a DocType
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "CRM Exotel Settings"
msgstr "CRM Pengaturan Exotel"

#. Name of a DocType
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "CRM Fields Layout"
msgstr "CRM Layout Bidang"

#. Name of a DocType
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "CRM Form Script"
msgstr "CRM Skrip Formulir"

#. Name of a DocType
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "CRM Global Settings"
msgstr "CRM Pengaturan Global"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "CRM Holiday"
msgstr "CRM Hari Libur"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "CRM Holiday List"
msgstr "CRM Daftar Hari Libur"

#. Name of a DocType
#: crm/fcrm/doctype/crm_industry/crm_industry.json
msgid "CRM Industry"
msgstr "CRM Industri"

#. Name of a DocType
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "CRM Invitation"
msgstr "CRM Undangan"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "CRM Lead"
msgstr "CRM Prospek"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "CRM Lead Source"
msgstr "CRM Sumber Prospek"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "CRM Lead Status"
msgstr "CRM Status Prospek"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "CRM Lost Reason"
msgstr "CRM Alasan Kegagalan"

#. Name of a DocType
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "CRM Notification"
msgstr "CRM Notifikasi"

#. Name of a DocType
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "CRM Organization"
msgstr "CRM Organisasi"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "CRM Portal Page"
msgstr "CRM Halaman Portal"

#. Name of a DocType
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "CRM Product"
msgstr "CRM Produk"

#. Name of a DocType
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "CRM Products"
msgstr "CRM Produk"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "CRM Service Day"
msgstr "CRM Hari Pelayanan"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "CRM Service Level Agreement"
msgstr "CRM Perjanjian Tingkat Layanan"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "CRM Service Level Priority"
msgstr "CRM Prioritas Tingkat Layanan"

#. Name of a DocType
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "CRM Status Change Log"
msgstr "CRM Log Perubahan Status"

#. Name of a DocType
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "CRM Task"
msgstr "CRM Tugas"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "CRM Telephony Agent"
msgstr "CRM Agen Telepon"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "CRM Telephony Phone"
msgstr "CRM Telepon"

#. Name of a DocType
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "CRM Territory"
msgstr "CRM Wilayah"

#. Name of a DocType
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "CRM Twilio Settings"
msgstr "CRM Pengaturan Twilio"

#. Name of a DocType
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "CRM View Settings"
msgstr "CRM Pengaturan Tampilan"

#: frontend/src/components/Settings/CurrencySettings.vue:35
msgid "CRM currency for all monetary values. Once set, cannot be edited."
msgstr "Mata uang CRM untuk semua nilai moneter. Setelah ditetapkan, tidak dapat diubah lagi."

#: frontend/src/components/ViewControls.vue:272
msgid "CSV"
msgstr "CSV"

#: frontend/src/components/Modals/CallLogDetailModal.vue:8
msgid "Call Details"
msgstr "Detail Panggilan"

#. Label of the receiver (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call Received By"
msgstr "Panggilan Diterima Oleh"

#. Description of the 'Duration' (Duration) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call duration in seconds"
msgstr "Durasi panggilan dalam detik"

#: frontend/src/components/Layouts/AppSidebar.vue:556
msgid "Call log"
msgstr "Log Panggilan"

#: frontend/src/components/Telephony/CallUI.vue:10
msgid "Call using {0}"
msgstr "Panggil menggunakan {0}"

#: frontend/src/components/Modals/EventModal.vue:63
#: frontend/src/components/Modals/NoteModal.vue:28
#: frontend/src/components/Modals/TaskModal.vue:30
msgid "Call with John Doe"
msgstr "Panggilan dengan John Doe"

#. Label of the caller (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Caller"
msgstr "Penelepon"

#: frontend/src/components/Telephony/CallUI.vue:27
msgid "Calling Medium"
msgstr "Media Panggilan"

#: frontend/src/components/Telephony/TwilioCallUI.vue:40
#: frontend/src/components/Telephony/TwilioCallUI.vue:140
msgid "Calling..."
msgstr "Memanggil..."

#: frontend/src/pages/Deal.vue:562 frontend/src/pages/Lead.vue:409
#: frontend/src/pages/MobileDeal.vue:450 frontend/src/pages/MobileLead.vue:357
msgid "Calls"
msgstr "Panggilan"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:51
msgid "Camera"
msgstr "Kamera"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:81
#: frontend/src/components/Calendar/CalendarEventPanel.vue:620
#: frontend/src/components/ColumnSettings.vue:123
#: frontend/src/components/Dashboard/AddChartModal.vue:40
#: frontend/src/components/DeleteLinkedDocModal.vue:115
#: frontend/src/components/Modals/AssignmentModal.vue:69
#: frontend/src/components/Modals/EventModal.vue:151
#: frontend/src/components/Modals/LostReasonModal.vue:43
#: frontend/src/components/Telephony/TwilioCallUI.vue:73
#: frontend/src/components/ViewControls.vue:57
#: frontend/src/components/ViewControls.vue:154
#: frontend/src/pages/Dashboard.vue:32
msgid "Cancel"
msgstr "Batalkan"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Canceled"
msgstr "Dibatalkan"

#: frontend/src/components/Settings/Users.vue:124
msgid "Cannot change role of user with Admin access"
msgstr "Peran pengguna dengan akses Admin tidak dapat diubah"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:34
msgid "Cannot delete standard items {0}"
msgstr "Tidak dapat menghapus item standar {0}"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:171
msgid "Cannot enable rule without adding users in it"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:91
msgid "Capture"
msgstr "Ambil Gambar"

#: frontend/src/components/Layouts/AppSidebar.vue:561
msgid "Capturing leads"
msgstr "Menangkap prospek"

#. Label of the category (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Category"
msgstr "Kategori"

#: frontend/src/components/Controls/ImageUploader.vue:19
#: frontend/src/components/Layouts/AppSidebar.vue:490
msgid "Change"
msgstr "Ubah"

#: frontend/src/components/Modals/ChangePasswordModal.vue:2
#: frontend/src/components/Settings/ProfileSettings.vue:65
msgid "Change Password"
msgstr "Ubah Kata Sandi"

#: frontend/src/components/Layouts/AppSidebar.vue:481
#: frontend/src/components/Layouts/AppSidebar.vue:489
msgid "Change deal status"
msgstr "Ubah status kesepakatan"

#: frontend/src/components/Settings/ProfileSettings.vue:26
#: frontend/src/pages/Contact.vue:47 frontend/src/pages/Lead.vue:88
#: frontend/src/pages/MobileContact.vue:37
#: frontend/src/pages/MobileOrganization.vue:37
#: frontend/src/pages/Organization.vue:47
msgid "Change image"
msgstr "Ubah gambar"

#: frontend/src/components/Activities/TaskArea.vue:45
msgid "Change status"
msgstr ""

#: frontend/src/pages/Dashboard.vue:22
msgid "Chart"
msgstr "Bagan"

#: frontend/src/components/Dashboard/AddChartModal.vue:12
msgid "Chart Type"
msgstr "Jenis Bagan"

#: frontend/src/components/Modals/ConvertToDealModal.vue:29
#: frontend/src/components/Modals/ConvertToDealModal.vue:55
#: frontend/src/pages/MobileLead.vue:122 frontend/src/pages/MobileLead.vue:149
msgid "Choose Existing"
msgstr "Pilih yang Ada"

#: frontend/src/components/Modals/DealModal.vue:44
msgid "Choose Existing Contact"
msgstr "Pilih Kontak yang Ada"

#: frontend/src/components/Modals/DealModal.vue:37
msgid "Choose Existing Organization"
msgstr "Pilih Organisasi yang Ada"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:9
msgid "Choose how {0} are assigned among salespeople."
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:306
msgid "Choose the days of the week when this rule should be active."
msgstr ""

#: frontend/src/components/Settings/EmailAdd.vue:9
msgid "Choose the email service provider you want to configure."
msgstr "Pilih penyedia layanan email yang ingin Anda konfigurasikan."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:229
msgid "Choose which {0} are affected by this un-assignment rule."
msgstr ""

#: frontend/src/components/Controls/Link.vue:59
msgid "Clear"
msgstr "Kosongkan"

#: frontend/src/components/ListBulkActions.vue:136
#: frontend/src/components/ListBulkActions.vue:144
#: frontend/src/components/ListBulkActions.vue:190
msgid "Clear Assignment"
msgstr "Hapus Penugasan"

#: frontend/src/components/SortBy.vue:153
msgid "Clear Sort"
msgstr "Hapus Urutan"

#. Label of the clear_table (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Clear Table"
msgstr "Kosongkan Tabel"

#: frontend/src/components/Filter.vue:21 frontend/src/components/Filter.vue:146
msgid "Clear all Filter"
msgstr "Hapus semua Filter"

#: crm/templates/emails/helpdesk_invitation.html:7
msgid "Click on the link below to complete your registration and set a new password"
msgstr "Klik link di bawah ini untuk melengkapi pendaftaran Anda dan mengatur sandi baru"

#: frontend/src/components/Notifications.vue:26
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:54
msgid "Close"
msgstr "Tutup"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:38
msgid "Close panel"
msgstr ""

#. Label of the closed_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Closed Date"
msgstr "Tanggal Penutupan"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Collapse"
msgstr "Ciutkan"

#: frontend/src/components/FieldLayoutEditor.vue:344
msgid "Collapsible"
msgstr "Dapat diciutkan"

#. Label of the color (Select) field in DocType 'CRM Deal Status'
#. Label of the color (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Color"
msgstr "Warna"

#: frontend/src/components/FieldLayoutEditor.vue:417
msgid "Column"
msgstr "Kolom"

#. Label of the column_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:12
msgid "Column Field"
msgstr "Bidang Kolom"

#. Label of the columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:4
msgid "Columns"
msgstr "Kolom"

#. Label of the comment (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/CommentBox.vue:78
#: frontend/src/components/CommunicationArea.vue:16
#: frontend/src/components/Layouts/AppSidebar.vue:579
msgid "Comment"
msgstr "Komentar"

#: frontend/src/pages/Deal.vue:547 frontend/src/pages/Lead.vue:394
#: frontend/src/pages/MobileDeal.vue:440 frontend/src/pages/MobileLead.vue:347
msgid "Comments"
msgstr "Komentar"

#: crm/api/dashboard.py:920
msgid "Common reasons for losing deals"
msgstr "Alasan umum gagalnya kesepakatan"

#. Label of the communication_status (Link) field in DocType 'CRM Deal'
#. Label of the communication_status (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Communication Status"
msgstr "Status Komunikasi"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Communication Statuses"
msgstr "Status Komunikasi"

#. Label of the erpnext_company (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Company in ERPNext Site"
msgstr "Perusahaan di Situs ERPNext"

#: crm/templates/emails/helpdesk_invitation.html:10
msgid "Complete Registration"
msgstr "Pendaftaran Lengkap"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Completed"
msgstr "Selesai"

#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Computer"
msgstr "Komputer"

#. Label of the condition (Code) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Condition"
msgstr "Kondisi"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:188
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:272
msgid "Conditions for this rule were created from"
msgstr ""

#: frontend/src/components/Settings/HomeActions.vue:10
msgid "Configure actions that appear on the home dropdown"
msgstr ""

#: frontend/src/components/Settings/ForecastingSettings.vue:9
msgid "Configure forecasting feature to help predict sales performance and growth"
msgstr ""

#: frontend/src/components/Settings/TelephonySettings.vue:17
msgid "Configure telephony settings for your CRM"
msgstr "Konfigurasikan pengaturan telepon untuk CRM Anda"

#: frontend/src/components/Settings/CurrencySettings.vue:11
msgid "Configure the currency and exchange rate provider for your CRM"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:63
msgid "Configure the exchange rate provider for your CRM"
msgstr "Konfigurasikan penyedia nilai tukar untuk CRM Anda"

#: frontend/src/components/Settings/BrandSettings.vue:10
msgid "Configure your brand name, logo, and favicon"
msgstr ""

#: frontend/src/composables/frappecloud.js:29
msgid "Confirm"
msgstr "Konfirmasi"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:139
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:239
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:209
msgid "Confirm Delete"
msgstr "Konfirmasi Hapus"

#: frontend/src/components/Modals/ChangePasswordModal.vue:18
msgid "Confirm Password"
msgstr "Konfirmasi Password"

#: frontend/src/components/Settings/Users.vue:228
msgid "Confirm Remove"
msgstr "Konfirmasi Hapus"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:609
msgid "Confirm overwrite"
msgstr ""

#: frontend/src/pages/Welcome.vue:35
msgid "Connect your email"
msgstr "Hubungkan email anda"

#. Label of the contact (Link) field in DocType 'CRM Contacts'
#. Label of the contact (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:552
#: frontend/src/components/Modals/ConvertToDealModal.vue:51
#: frontend/src/pages/MobileLead.vue:145
msgid "Contact"
msgstr "Kontak"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:259
msgid "Contact Already Exists"
msgstr "Kontak Sudah Ada"

#: frontend/src/components/Modals/AboutModal.vue:77
msgid "Contact Support"
msgstr "Hubungi Dukungan"

#: frontend/src/components/Modals/EditValueModal.vue:20
msgid "Contact Us"
msgstr "Hubungi Kami"

#: frontend/src/pages/Deal.vue:657 frontend/src/pages/MobileDeal.vue:544
msgid "Contact added"
msgstr "Kontak ditambahkan"

#: frontend/src/pages/Deal.vue:647 frontend/src/pages/MobileDeal.vue:534
msgid "Contact already added"
msgstr "Kontak sudah ditambahkan"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:258
msgid "Contact already exists with {0}"
msgstr "Kontak dengan {0} sudah ada"

#: frontend/src/pages/Contact.vue:287 frontend/src/pages/MobileContact.vue:252
msgid "Contact image updated"
msgstr "Gambar kontak diperbarui"

#: frontend/src/pages/Deal.vue:668 frontend/src/pages/MobileDeal.vue:555
msgid "Contact removed"
msgstr "Kontak dihapus"

#: frontend/src/pages/Contact.vue:432 frontend/src/pages/Contact.vue:445
#: frontend/src/pages/Contact.vue:458 frontend/src/pages/Contact.vue:468
#: frontend/src/pages/MobileContact.vue:432
#: frontend/src/pages/MobileContact.vue:445
#: frontend/src/pages/MobileContact.vue:458
#: frontend/src/pages/MobileContact.vue:468
msgid "Contact updated"
msgstr "Kontak diperbarui"

#. Label of the contacts_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the contacts (Table) field in DocType 'CRM Deal'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Contact.vue:242 frontend/src/pages/MobileContact.vue:212
#: frontend/src/pages/MobileOrganization.vue:331
msgid "Contacts"
msgstr "Kontak"

#. Label of the content (Text Editor) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:33
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:89
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:102
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:89
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:102
msgid "Content"
msgstr "Konten"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:78
msgid "Content Type"
msgstr "Tipe Konten"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:159
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:163
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:162
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:166
msgid "Content is required"
msgstr "Konten wajib diisi"

#: frontend/src/components/Layouts/AppSidebar.vue:380
#: frontend/src/components/ListBulkActions.vue:88
#: frontend/src/components/Modals/ConvertToDealModal.vue:83
#: frontend/src/pages/MobileLead.vue:54 frontend/src/pages/MobileLead.vue:108
msgid "Convert"
msgstr "Konversi"

#: frontend/src/components/Layouts/AppSidebar.vue:371
#: frontend/src/components/Layouts/AppSidebar.vue:379
msgid "Convert lead to deal"
msgstr "Konversi prospek menjadi kesepakatan"

#: frontend/src/components/ListBulkActions.vue:80
#: frontend/src/components/ListBulkActions.vue:197
#: frontend/src/components/Modals/ConvertToDealModal.vue:7
#: frontend/src/pages/Lead.vue:38 frontend/src/pages/MobileLead.vue:104
msgid "Convert to Deal"
msgstr "Konversi menjadi Kesepakatan"

#. Label of the converted (Check) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Converted"
msgstr "Terkonversi"

#: frontend/src/components/ListBulkActions.vue:96
msgid "Converted successfully"
msgstr "Berhasil dikonversi"

#: frontend/src/utils/index.js:338
msgid "Copied to clipboard"
msgstr "Disalin ke papan klip"

#: crm/api/dashboard.py:626 crm/api/dashboard.py:763 crm/api/dashboard.py:824
#: crm/api/dashboard.py:927
msgid "Count"
msgstr "Jumlah"

#: frontend/src/components/Modals/AddressModal.vue:98
#: frontend/src/components/Modals/CallLogModal.vue:101
#: frontend/src/components/Modals/ContactModal.vue:40
#: frontend/src/components/Modals/CreateDocumentModal.vue:92
#: frontend/src/components/Modals/DealModal.vue:66
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:20
#: frontend/src/components/Modals/EventModal.vue:159
#: frontend/src/components/Modals/LeadModal.vue:37
#: frontend/src/components/Modals/NoteModal.vue:52
#: frontend/src/components/Modals/OrganizationModal.vue:41
#: frontend/src/components/Modals/TaskModal.vue:106
#: frontend/src/components/Modals/ViewModal.vue:43
#: frontend/src/components/Settings/EmailAdd.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:23
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:30
#: frontend/src/pages/Calendar.vue:10 frontend/src/pages/CallLogs.vue:13
#: frontend/src/pages/Contacts.vue:13 frontend/src/pages/Contacts.vue:60
#: frontend/src/pages/Deals.vue:13 frontend/src/pages/Deals.vue:236
#: frontend/src/pages/Leads.vue:13 frontend/src/pages/Leads.vue:262
#: frontend/src/pages/Notes.vue:9 frontend/src/pages/Notes.vue:96
#: frontend/src/pages/Organizations.vue:13
#: frontend/src/pages/Organizations.vue:60 frontend/src/pages/Tasks.vue:13
#: frontend/src/pages/Tasks.vue:186
msgid "Create"
msgstr "Buat"

#: frontend/src/components/Modals/DealModal.vue:8
msgid "Create Deal"
msgstr "Buat Kesepakatan"

#: frontend/src/components/Modals/LeadModal.vue:8
msgid "Create Lead"
msgstr "Buat Prospek"

#: frontend/src/components/Controls/Link.vue:50
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:69
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:45
#: frontend/src/components/PrimaryDropdown.vue:41
msgid "Create New"
msgstr "Buat Baru"

#: frontend/src/components/Activities/Activities.vue:388
#: frontend/src/components/Modals/NoteModal.vue:6
msgid "Create Note"
msgstr "Buat Catatan"

#: frontend/src/components/Activities/Activities.vue:403
#: frontend/src/components/Modals/TaskModal.vue:6
msgid "Create Task"
msgstr "Buat Tugas"

#: frontend/src/components/Modals/ViewModal.vue:9
#: frontend/src/components/ViewControls.vue:687
msgid "Create View"
msgstr "Buat Tampilan"

#: frontend/src/components/Modals/EventModal.vue:12
msgid "Create an event"
msgstr ""

#. Label of the create_customer_on_status_change (Check) field in DocType
#. 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Create customer on status change"
msgstr "Buat pelanggan saat perubahan status"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:348
#: frontend/src/pages/Calendar.vue:7
msgid "Create event"
msgstr ""

#: frontend/src/components/Modals/CallLogDetailModal.vue:151
msgid "Create lead"
msgstr "Buat prospek"

#: frontend/src/components/Layouts/AppSidebar.vue:349
msgid "Create your first lead"
msgstr "Buat prospek pertama Anda"

#: frontend/src/components/Layouts/AppSidebar.vue:419
msgid "Create your first note"
msgstr "Buat catatan pertama Anda"

#: frontend/src/components/Layouts/AppSidebar.vue:399
msgid "Create your first task"
msgstr "Buat tugas pertama Anda"

#. Label of the currency (Link) field in DocType 'CRM Deal'
#. Label of the currency (Link) field in DocType 'CRM Organization'
#. Label of the currency (Link) field in DocType 'FCRM Settings'
#. Label of the currency_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/CurrencySettings.vue:31
msgid "Currency"
msgstr "Mata Uang"

#: frontend/src/components/Settings/Settings.vue:114
msgid "Currency & Exchange Rate"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:7
msgid "Currency & Exchange rate provider"
msgstr "Mata Uang & Penyedia nilai tukar"

#: frontend/src/components/Settings/CurrencySettings.vue:179
msgid "Currency set as {0} successfully"
msgstr "Mata uang berhasil diatur sebagai {0}"

#: crm/api/dashboard.py:872
msgid "Current pipeline distribution"
msgstr "Distribusi pipeline saat ini"

#: frontend/src/components/Layouts/AppSidebar.vue:591
msgid "Custom actions"
msgstr "Tindakan kustom"

#: frontend/src/components/Layouts/AppSidebar.vue:541
msgid "Custom branding"
msgstr "Branding kustom"

#: frontend/src/components/Layouts/AppSidebar.vue:590
msgid "Custom fields"
msgstr "Bidang kustom"

#: frontend/src/components/Layouts/AppSidebar.vue:593
msgid "Custom list actions"
msgstr "Daftar tindakan custom"

#: frontend/src/components/Layouts/AppSidebar.vue:592
msgid "Custom statuses"
msgstr "Status kustom"

#: frontend/src/pages/Deal.vue:476
msgid "Customer created successfully"
msgstr "Pelanggan berhasil dibuat"

#: frontend/src/components/Layouts/AppSidebar.vue:587
#: frontend/src/components/Settings/Settings.vue:171
msgid "Customization"
msgstr "Kustomisasi"

#: frontend/src/components/ViewControls.vue:211
msgid "Customize quick filters"
msgstr "Sesuaikan filter cepat"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Daily"
msgstr "Harian"

#: crm/api/dashboard.py:618
msgid "Daily performance of leads, deals, and wins"
msgstr "Kinerja harian prospek, kesepakatan, dan Ketercapaian Target"

#: frontend/src/components/Activities/DataFields.vue:6
#: frontend/src/components/Layouts/AppSidebar.vue:580
#: frontend/src/pages/Deal.vue:552 frontend/src/pages/Lead.vue:399
#: frontend/src/pages/MobileDeal.vue:445 frontend/src/pages/MobileLead.vue:352
msgid "Data"
msgstr "Data"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Data Fields"
msgstr "Bidang Data"

#. Label of the date (Date) field in DocType 'CRM Holiday'
#: crm/api/dashboard.py:620 crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:202
msgid "Date"
msgstr "Tanggal"

#: frontend/src/components/Modals/EventModal.vue:78
msgid "Date & Time"
msgstr ""

#: frontend/src/components/Calendar/CalendarEventPanel.vue:267
#: frontend/src/components/Calendar/CalendarEventPanel.vue:288
#: frontend/src/components/Layouts/AppSidebar.vue:551
#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:20
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:59
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:59
#: frontend/src/components/Telephony/ExotelCallUI.vue:200
#: frontend/src/pages/Tasks.vue:132
msgid "Deal"
msgstr "Kesepakatan"

#. Label of the deal_owner (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Owner"
msgstr "Pemilik Kesepakatan"

#. Label of the deal_status (Link) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Deal Status"
msgstr "Status Kesepakatan"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Deal Statuses"
msgstr "Status Kesepakatan"

#. Label of the deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Value"
msgstr "Nilai Kesepakatan"

#: crm/api/dashboard.py:1019
msgid "Deal generation channel analysis"
msgstr "Analisis saluran perolehan kesepakatan"

#: frontend/src/pages/Contact.vue:528 frontend/src/pages/MobileContact.vue:528
#: frontend/src/pages/MobileOrganization.vue:472
#: frontend/src/pages/Organization.vue:502
msgid "Deal owner"
msgstr "Pemilik kesepakatan"

#: crm/api/dashboard.py:1075 crm/api/dashboard.py:1135
msgid "Deal value"
msgstr "Nilai kesepakatan"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Deal.vue:490 frontend/src/pages/MobileContact.vue:288
#: frontend/src/pages/MobileDeal.vue:384
#: frontend/src/pages/MobileOrganization.vue:325
msgid "Deals"
msgstr "Kesepakatan"

#: crm/api/dashboard.py:818
#: frontend/src/components/Dashboard/AddChartModal.vue:97
msgid "Deals by ongoing & won stage"
msgstr "Kesepakatan berdasarkan tahap sedang berjalan & tahap berhasil"

#: crm/api/dashboard.py:1124
#: frontend/src/components/Dashboard/AddChartModal.vue:100
msgid "Deals by salesperson"
msgstr "Kesepakatan berdasarkan tenaga penjualan"

#: crm/api/dashboard.py:1018
#: frontend/src/components/Dashboard/AddChartModal.vue:107
msgid "Deals by source"
msgstr "Kesepakatan berdasarkan sumber"

#: crm/api/dashboard.py:871
#: frontend/src/components/Dashboard/AddChartModal.vue:105
msgid "Deals by stage"
msgstr "Kesepakatan berdasarkan tahap"

#: crm/api/dashboard.py:1064
#: frontend/src/components/Dashboard/AddChartModal.vue:99
msgid "Deals by territory"
msgstr "Kesepakatan berdasarkan wilayah"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:112
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:112
msgid "Dear {{ lead_name }}, \\n\\nThis is a reminder for the payment of {{ grand_total }}. \\n\\nThanks, \\nFrappé"
msgstr "Yang Terhormat {{ lead_name }},\\n\\nDengan ini, kami ingin mengingatkan mengenai pembayaran sebesar {{ grand_total }}.\\n\\nHormat kami,\\nFrappé"

#. Label of the default (Check) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Default"
msgstr "Default"

#: frontend/src/components/Settings/EmailAccountCard.vue:41
msgid "Default Inbox"
msgstr "Kotak Masuk Default"

#: frontend/src/components/Settings/emailConfig.js:44
msgid "Default Incoming"
msgstr "Email Masuk Default"

#. Label of the default_medium (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Default Medium"
msgstr "Media Default"

#: frontend/src/components/Settings/emailConfig.js:52
msgid "Default Outgoing"
msgstr "Email Keluar Default"

#. Label of the default_priority (Check) field in DocType 'CRM Service Level
#. Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "Default Priority"
msgstr "Prioritas Default"

#: frontend/src/components/Settings/EmailAccountCard.vue:43
msgid "Default Sending"
msgstr "Pengiriman Email Default"

#: frontend/src/components/Settings/EmailAccountCard.vue:39
msgid "Default Sending and Inbox"
msgstr "Pengiriman Email dan Kotak Masuk Default"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:33
msgid "Default Service Level Agreement already exists for {0}"
msgstr "Perjanjian Tingkat Layanan Default sudah ada untuk {0}"

#: frontend/src/components/Settings/TelephonySettings.vue:44
msgid "Default calling medium for logged in user"
msgstr "Media panggilan default untuk pengguna yang sedang masuk"

#: frontend/src/components/Telephony/CallUI.vue:112
msgid "Default calling medium set successfully to {0}"
msgstr "Media panggilan default berhasil diatur ke {0}"

#: frontend/src/components/Settings/TelephonySettings.vue:280
msgid "Default calling medium updated successfully"
msgstr "Media panggilan default berhasil diperbarui"

#: frontend/src/components/Settings/TelephonySettings.vue:37
msgid "Default medium"
msgstr "Media default"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:18
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:30
msgid "Default statuses, custom fields and layouts restored successfully."
msgstr "Status default, bidang kustom, dan layout berhasil dipulihkan."

#: frontend/src/components/Activities/AttachmentArea.vue:131
#: frontend/src/components/Activities/NoteArea.vue:12
#: frontend/src/components/Activities/TaskArea.vue:55
#: frontend/src/components/Activities/TaskArea.vue:63
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:8
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:48
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:121
#: frontend/src/components/Controls/Grid.vue:329
#: frontend/src/components/DeleteLinkedDocModal.vue:10
#: frontend/src/components/DeleteLinkedDocModal.vue:90
#: frontend/src/components/Kanban/KanbanView.vue:222
#: frontend/src/components/ListBulkActions.vue:179
#: frontend/src/components/Modals/EventModal.vue:407
#: frontend/src/components/Modals/EventModal.vue:411
#: frontend/src/components/PrimaryDropdownItem.vue:42
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:129
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:229
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:199
#: frontend/src/components/ViewControls.vue:1116
#: frontend/src/components/ViewControls.vue:1127
#: frontend/src/pages/Calendar.vue:299 frontend/src/pages/Calendar.vue:303
#: frontend/src/pages/Contact.vue:106 frontend/src/pages/Deal.vue:111
#: frontend/src/pages/Lead.vue:157 frontend/src/pages/MobileContact.vue:79
#: frontend/src/pages/MobileContact.vue:263
#: frontend/src/pages/MobileDeal.vue:515
#: frontend/src/pages/MobileOrganization.vue:72
#: frontend/src/pages/MobileOrganization.vue:264
#: frontend/src/pages/Notes.vue:43 frontend/src/pages/Organization.vue:89
#: frontend/src/pages/Tasks.vue:371
msgid "Delete"
msgstr "Hapus"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:26
msgid "Delete & Restore"
msgstr "Hapus & Pulihkan"

#: frontend/src/components/Activities/TaskArea.vue:59
msgid "Delete Task"
msgstr "Hapus Tugas"

#: frontend/src/components/ViewControls.vue:1112
#: frontend/src/components/ViewControls.vue:1120
msgid "Delete View"
msgstr "Hapus Tampilan"

#: frontend/src/components/DeleteLinkedDocModal.vue:66
msgid "Delete all"
msgstr "Hapus semua"

#: frontend/src/components/Activities/AttachmentArea.vue:58
#: frontend/src/components/Activities/AttachmentArea.vue:127
msgid "Delete attachment"
msgstr "Hapus lampiran"

#: frontend/src/pages/MobileContact.vue:259
msgid "Delete contact"
msgstr "Hapus kontak"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:25
msgid "Delete event"
msgstr ""

#: frontend/src/components/Settings/InviteUserPage.vue:79
msgid "Delete invitation"
msgstr ""

#: frontend/src/components/DeleteLinkedDocModal.vue:230
msgid "Delete linked item"
msgstr "Hapus item tertaut"

#: frontend/src/components/DeleteLinkedDocModal.vue:11
msgid "Delete or unlink linked documents"
msgstr "Hapus atau lepas dokumen tertaut"

#: frontend/src/components/DeleteLinkedDocModal.vue:23
msgid "Delete or unlink these linked documents before deleting this document"
msgstr "Hapus atau lepas dokumen tertaut ini sebelum menghapus dokumen ini"

#: frontend/src/pages/MobileOrganization.vue:260
msgid "Delete organization"
msgstr "Hapus organisasi"

#: frontend/src/components/DeleteLinkedDocModal.vue:67
msgid "Delete {0} item(s)"
msgstr "Hapus {0} item"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:28
msgid "Delete {0} items"
msgstr "Hapus {0} item"

#. Label of the description (Text Editor) field in DocType 'CRM Holiday'
#. Label of the description (Text Editor) field in DocType 'CRM Lost Reason'
#. Label of the description (Text Editor) field in DocType 'CRM Product'
#. Label of the description (Text Editor) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:152
#: frontend/src/components/Modals/EventModal.vue:134
#: frontend/src/components/Modals/TaskModal.vue:36
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:112
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:113
msgid "Description"
msgstr "Deskripsi"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:426
msgid "Description is required"
msgstr ""

#: frontend/src/components/Apps.vue:63
msgid "Desk"
msgstr "Area Kerja"

#. Label of the details (Tab Break) field in DocType 'CRM Lead'
#. Label of the details (Text Editor) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/pages/MobileContact.vue:283
#: frontend/src/pages/MobileDeal.vue:424 frontend/src/pages/MobileLead.vue:331
#: frontend/src/pages/MobileOrganization.vue:320
msgid "Details"
msgstr "Detail"

#. Label of the call_receiving_device (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:39
msgid "Device"
msgstr "Perangkat"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Disable"
msgstr "Nonaktifkan"

#. Label of the disabled (Check) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Disabled"
msgstr "Dinonaktifkan"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:626
#: frontend/src/components/CommentBox.vue:74
#: frontend/src/components/EmailEditor.vue:161
msgid "Discard"
msgstr "Batalkan"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:614
msgid "Discard unsaved changes?"
msgstr ""

#. Label of the discount_percentage (Percent) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount %"
msgstr "Diskon %"

#. Label of the discount_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount Amount"
msgstr "Jumlah Diskon"

#. Label of the dt (Link) field in DocType 'CRM Form Script'
#. Label of the dt (Link) field in DocType 'CRM Global Settings'
#. Label of the dt (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "DocType"
msgstr "DocType"

#. Label of the dt (Link) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Document Type"
msgstr "Tipe Dokumen"

#: frontend/src/data/document.js:30
msgid "Document does not exist"
msgstr "Dokumen tidak ada"

#: crm/api/activities.py:19
msgid "Document not found"
msgstr "Dokumen tidak ditemukan"

#: frontend/src/data/document.js:45
msgid "Document updated successfully"
msgstr "Dokumen berhasil diperbarui"

#: frontend/src/components/Modals/AboutModal.vue:62
msgid "Documentation"
msgstr "Dokumentasi"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Done"
msgstr "Selesai"

#: frontend/src/components/Dashboard/AddChartModal.vue:33
#: frontend/src/components/Dashboard/AddChartModal.vue:71
msgid "Donut chart"
msgstr "Bagan Donat"

#: frontend/src/components/Activities/AudioPlayer.vue:166
#: frontend/src/components/ViewControls.vue:254
msgid "Download"
msgstr "Unduh"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:24
msgid "Drag and drop files here or upload from"
msgstr "Seret dan lepas file di sini atau unggah dari"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:56
msgid "Drop files here"
msgstr "Letakkan file di sini"

#. Label of the dropdown_items_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Dropdown Items"
msgstr "Item Dropdown"

#. Label of the due_date (Datetime) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Due Date"
msgstr "Tanggal Jatuh Tempo"

#. Option for the 'Type' (Select) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: frontend/src/components/Modals/EventModal.vue:158
#: frontend/src/components/Modals/ViewModal.vue:42
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:57
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:119
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:224
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:23
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:194
#: frontend/src/components/ViewControls.vue:1068
msgid "Duplicate"
msgstr "Duplikat"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:38
msgid "Duplicate Assignment Rule"
msgstr ""

#: frontend/src/components/Modals/ViewModal.vue:8
msgid "Duplicate View"
msgstr "Duplikasi Tampilan"

#: frontend/src/components/Modals/EventModal.vue:11
msgid "Duplicate an event"
msgstr ""

#: frontend/src/components/Calendar/CalendarEventPanel.vue:33
#: frontend/src/components/Calendar/CalendarEventPanel.vue:347
#: frontend/src/components/Calendar/CalendarEventPanel.vue:432
msgid "Duplicate event"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "Duplicate template"
msgstr "Duplikasi templat"

#. Label of the duration (Duration) field in DocType 'CRM Call Log'
#. Label of the duration (Duration) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Duration"
msgstr "Durasi"

#: frontend/src/components/Layouts/AppSidebar.vue:604
#: frontend/src/components/Settings/Settings.vue:197
msgid "ERPNext"
msgstr "ERPNext"

#. Name of a DocType
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext CRM Settings"
msgstr "Pengaturan CRM ERPNext"

#. Label of the section_break_oubd (Section Break) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site API's"
msgstr "API Situs ERPNext"

#. Label of the erpnext_site_url (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site URL"
msgstr "URL Situs ERPNext"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:25
msgid "ERPNext is not installed in the current site"
msgstr "ERPNext tidak terpasang di situs ini"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:98
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:124
msgid "ERPNext is not integrated with the CRM"
msgstr "ERPNext tidak terintegrasi dengan CRM"

#: frontend/src/components/Settings/ERPNextSettings.vue:4
msgid "ERPNext settings"
msgstr "Pengaturan ERPNext"

#: frontend/src/components/Settings/ERPNextSettings.vue:5
msgid "ERPNext settings updated"
msgstr "Pengaturan ERPNext diperbarui"

#: frontend/src/components/FieldLayout/Field.vue:91
#: frontend/src/components/FieldLayoutEditor.vue:313
#: frontend/src/components/FieldLayoutEditor.vue:339
#: frontend/src/components/ListBulkActions.vue:172
#: frontend/src/components/PrimaryDropdownItem.vue:35
#: frontend/src/components/ViewControls.vue:1086
#: frontend/src/pages/Dashboard.vue:16
msgid "Edit"
msgstr "Edit"

#: frontend/src/components/Modals/CallLogModal.vue:97
msgid "Edit Call Log"
msgstr "Edit Log Panggilan"

#: frontend/src/components/Modals/DataFieldsModal.vue:7
msgid "Edit Data Fields Layout"
msgstr "Edit Layout Bidang Data"

#: frontend/src/components/Settings/EmailEdit.vue:6
msgid "Edit Email"
msgstr "Edit Email"

#: frontend/src/components/Modals/SidePanelModal.vue:7
msgid "Edit Field Layout"
msgstr "Edit Layout Bidang"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:7
msgid "Edit Grid Fields Layout"
msgstr "Edit Layout Bidang Grid"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:7
msgid "Edit Grid Row Fields Layout"
msgstr "Edit Layout Bidang Baris Grid"

#: frontend/src/components/Modals/NoteModal.vue:6
msgid "Edit Note"
msgstr "Edit Catatan"

#: frontend/src/components/Modals/QuickEntryModal.vue:7
msgid "Edit Quick Entry Layout"
msgstr "Edit Layout Entri Cepat"

#: frontend/src/components/Modals/TaskModal.vue:6
msgid "Edit Task"
msgstr "Edit Tugas"

#: frontend/src/components/Modals/ViewModal.vue:6
msgid "Edit View"
msgstr "Edit Tampilan"

#: frontend/src/components/Modals/EventModal.vue:9
msgid "Edit an event"
msgstr ""

#: frontend/src/components/Modals/CallLogDetailModal.vue:39
msgid "Edit call log"
msgstr ""

#: frontend/src/components/Calendar/CalendarEventPanel.vue:18
msgid "Edit event"
msgstr ""

#: frontend/src/components/Activities/DataFields.vue:17
#: frontend/src/components/Controls/GridRowModal.vue:14
#: frontend/src/components/Modals/AddressModal.vue:14
#: frontend/src/components/Modals/CallLogModal.vue:16
#: frontend/src/components/Modals/ContactModal.vue:16
#: frontend/src/components/Modals/CreateDocumentModal.vue:16
#: frontend/src/components/Modals/DealModal.vue:16
#: frontend/src/components/Modals/LeadModal.vue:16
#: frontend/src/components/Modals/OrganizationModal.vue:16
msgid "Edit fields layout"
msgstr ""

#: frontend/src/components/Controls/Grid.vue:57
msgid "Edit grid fields"
msgstr ""

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Edit note"
msgstr "Edit catatan"

#: frontend/src/components/Controls/Grid.vue:297
msgid "Edit row"
msgstr ""

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Edit task"
msgstr "Edit tugas"

#: frontend/src/components/Controls/GridRowModal.vue:8
msgid "Editing Row {0}"
msgstr "Mengedit Baris {0}"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:430
msgid "Editing event"
msgstr ""

#. Label of the email (Data) field in DocType 'CRM Contacts'
#. Label of the email (Data) field in DocType 'CRM Invitation'
#. Label of the email (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json frontend/src/pages/Contact.vue:518
#: frontend/src/pages/MobileContact.vue:518
#: frontend/src/pages/MobileOrganization.vue:462
#: frontend/src/pages/MobileOrganization.vue:490
#: frontend/src/pages/Organization.vue:492
#: frontend/src/pages/Organization.vue:520
msgid "Email"
msgstr "Email"

#: frontend/src/components/Settings/Settings.vue:148
msgid "Email Accounts"
msgstr "Akun Email"

#: frontend/src/components/Settings/emailConfig.js:168
msgid "Email ID is required"
msgstr "ID Email wajib diisi"

#. Label of the email_sent_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Email Sent At"
msgstr "Email Terkirim Pada"

#: frontend/src/components/Settings/Settings.vue:145
msgid "Email Settings"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:4
#: frontend/src/components/Settings/Settings.vue:154
msgid "Email Templates"
msgstr "Templat Email"

#: frontend/src/components/Settings/EmailAdd.vue:141
msgid "Email account created successfully"
msgstr "Akun email berhasil dibuat"

#: frontend/src/components/Settings/EmailEdit.vue:208
msgid "Email account updated successfully"
msgstr "Akun email berhasil diperbarui"

#: frontend/src/components/Settings/EmailAccountList.vue:7
msgid "Email accounts"
msgstr "Akun email"

#: frontend/src/components/Layouts/AppSidebar.vue:578
msgid "Email communication"
msgstr "Komunikasi email"

#: frontend/src/components/EmailEditor.vue:209
msgid "Email from Lead"
msgstr "Email dari Prospek"

#: frontend/src/components/Layouts/AppSidebar.vue:557
msgid "Email template"
msgstr "Templat email"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:7
msgid "Email templates"
msgstr "Templat email"

#: frontend/src/pages/Deal.vue:542 frontend/src/pages/Lead.vue:389
#: frontend/src/pages/MobileDeal.vue:435 frontend/src/pages/MobileLead.vue:342
msgid "Emails"
msgstr "Email"

#: frontend/src/components/ListViews/ListRows.vue:12
msgid "Empty"
msgstr "Kosong"

#: frontend/src/components/Filter.vue:123
msgid "Empty - Choose a field to filter by"
msgstr "Kosong - Pilih sebuah bidang untuk memfilter"

#: frontend/src/components/SortBy.vue:130
msgid "Empty - Choose a field to sort by"
msgstr "Kosong - Pilih sebuah bidang untuk mengurutkan"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Enable"
msgstr "Aktifkan"

#. Label of the enable_forecasting (Check) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Enable Forecasting"
msgstr "Aktifkan Forecasting"

#: frontend/src/components/Settings/emailConfig.js:28
msgid "Enable Incoming"
msgstr "Aktifkan Email Masuk"

#: frontend/src/components/Settings/emailConfig.js:36
msgid "Enable Outgoing"
msgstr "Aktifkan Email Keluar"

#: frontend/src/components/Settings/ForecastingSettings.vue:20
msgid "Enable forecasting"
msgstr "Aktifkan forecasting"

#. Label of the enabled (Check) field in DocType 'CRM Exotel Settings'
#. Label of the enabled (Check) field in DocType 'CRM Form Script'
#. Label of the enabled (Check) field in DocType 'CRM Service Level Agreement'
#. Label of the enabled (Check) field in DocType 'CRM Twilio Settings'
#. Label of the enabled (Check) field in DocType 'ERPNext CRM Settings'
#. Label of the enabled (Check) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:32
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:21
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:18
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:20
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:18
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:61
msgid "Enabled"
msgstr "Diaktifkan"

#. Label of the enabled (Check) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Enabled?"
msgstr "Diaktifkan?"

#. Label of the end_date (Date) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "End Date"
msgstr "Tanggal Akhir"

#. Label of the end_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the end_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:242
#: frontend/src/components/Modals/EventModal.vue:112
msgid "End Time"
msgstr "Waktu Selesai"

#: frontend/src/composables/event.js:206
msgid "End time should be after start time"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:88
msgid "Enter Access Token"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:70
msgid "Enter Source Name"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:115
msgid "Enter access key"
msgstr "Masukkan kunci akses"

#: frontend/src/components/Settings/BrandSettings.vue:33
msgid "Enter brand name"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:30
msgid "Enter your Facebook Access Token"
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:347
msgid "Enter {0}"
msgstr "Masukkan {0}"

#: frontend/src/components/Filter.vue:66 frontend/src/components/Filter.vue:99
#: frontend/src/components/Filter.vue:267
#: frontend/src/components/Filter.vue:288
#: frontend/src/components/Filter.vue:305
#: frontend/src/components/Filter.vue:316
#: frontend/src/components/Filter.vue:327
#: frontend/src/components/Filter.vue:343
msgid "Equals"
msgstr "Sama dengan"

#: frontend/src/components/Modals/ConvertToDealModal.vue:176
msgid "Error converting to deal: {0}"
msgstr "Terjadi kesalahan saat mengonversi menjadi kesepakatan: {0}"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:245
msgid "Error creating Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:362
msgid "Error syncing leads"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:226
msgid "Error updating Lead Sync Source"
msgstr ""

#: frontend/src/pages/Deal.vue:741 frontend/src/pages/Lead.vue:469
#: frontend/src/pages/MobileDeal.vue:612 frontend/src/pages/MobileLead.vue:412
msgid "Error updating field"
msgstr "Kesalahan saat memperbarui bidang"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:279
msgid "Error while creating customer in ERPNext, check error log for more details"
msgstr "Terjadi kesalahan ketika membuat pelanggan di ERPNext, periksa log kesalahan untuk detail lebih lanjut"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:191
msgid "Error while creating prospect in ERPNext, check error log for more details"
msgstr "Terjadi kesalahan ketika membuat prospek di ERPNext, periksa log kesalahan untuk detail lebih lanjut"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:117
msgid "Error while fetching customer in ERPNext, check error log for more details"
msgstr "Terjadi kesalahan saat mengambil data pelanggan di ERPNext, periksa log kesalahan untuk detail lebih lanjut"

#: frontend/src/components/Modals/EventModal.vue:368
msgid "Event ID is required"
msgstr ""

#: frontend/src/components/Calendar/CalendarEventPanel.vue:429
msgid "Event details"
msgstr ""

#: frontend/src/components/Calendar/CalendarEventPanel.vue:182
msgid "Event title"
msgstr ""

#: frontend/src/pages/Deal.vue:557 frontend/src/pages/Lead.vue:404
msgid "Events"
msgstr ""

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 10 Minutes"
msgstr ""

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 15 Minutes"
msgstr ""

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 5 Minutes"
msgstr ""

#: frontend/src/components/ViewControls.vue:268
#: frontend/src/components/ViewControls.vue:277
msgid "Excel"
msgstr "Excel"

#. Label of the exchange_rate (Float) field in DocType 'CRM Deal'
#. Label of the exchange_rate (Float) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Exchange Rate"
msgstr "Kurs"

#. Label of the exchange_rate_provider_section (Section Break) field in DocType
#. 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Exchange Rate Provider"
msgstr "Penyedia Nilai Tukar"

#: frontend/src/components/Settings/CurrencySettings.vue:60
msgid "Exchange rate provider"
msgstr "Penyedia nilai tukar"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the exotel (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:602
#: frontend/src/components/Settings/TelephonySettings.vue:41
#: frontend/src/components/Settings/TelephonySettings.vue:63
msgid "Exotel"
msgstr "Exotel"

#: crm/integrations/exotel/handler.py:114
msgid "Exotel Exception"
msgstr "Pengecualian Exotel"

#. Label of the exotel_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Exotel Number"
msgstr "Nomor Exotel"

#: crm/integrations/exotel/handler.py:85
msgid "Exotel Number Missing"
msgstr "Nomor Exotel Hilang"

#: crm/integrations/exotel/handler.py:89
msgid "Exotel Number {0} is not valid"
msgstr "Nomor Exotel {0} tidak valid"

#: frontend/src/components/Settings/TelephonySettings.vue:293
msgid "Exotel is not enabled"
msgstr "Exotel tidak diaktifkan"

#: frontend/src/components/Settings/TelephonySettings.vue:140
msgid "Exotel settings updated successfully"
msgstr "Pengaturan Exotel berhasil diperbarui"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Expand"
msgstr "Perluas"

#. Label of the expected_closure_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Closure Date"
msgstr "Tanggal Perkiraan Penutupan"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:173
msgid "Expected Closure Date is required."
msgstr "Tanggal Perkiraan Penutupan wajib diisi."

#. Label of the expected_deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Deal Value"
msgstr "Nilai Perkiraan Kesepakatan"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:171
msgid "Expected Deal Value is required."
msgstr "Nilai Perkiraan Kesepakatan wajib diisi."

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Expired"
msgstr "Kedaluwarsa"

#: frontend/src/components/ViewControls.vue:203
#: frontend/src/components/ViewControls.vue:251
msgid "Export"
msgstr "Ekspor"

#: frontend/src/components/ViewControls.vue:282
msgid "Export All {0} Record(s)"
msgstr "Ekspor Semua {0} Catatan"

#: frontend/src/components/ViewControls.vue:264
msgid "Export Type"
msgstr "Tipe Ekspor"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "FCRM Note"
msgstr "Catatan FCRM"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "FCRM Settings"
msgstr "Pengaturan FCRM"

#. Option for the 'Type' (Select) field in DocType 'Lead Sync Source'
#. Label of the facebook_section (Section Break) field in DocType 'Lead Sync
#. Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook"
msgstr ""

#. Label of the facebook_form_id (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Facebook Form ID"
msgstr ""

#. Name of a DocType
#. Label of the facebook_lead_form (Link) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook Lead Form"
msgstr ""

#. Name of a DocType
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Facebook Lead Form Question"
msgstr ""

#. Label of the facebook_lead_id (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Facebook Lead ID"
msgstr ""

#. Name of a DocType
#. Label of the facebook_page (Link) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook Page"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Failed"
msgstr "Gagal"

#. Name of a DocType
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Failed Lead Sync Log"
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:111
msgid "Failed to add users"
msgstr "Gagal menambahkan pengguna"

#: crm/integrations/twilio/api.py:130
msgid "Failed to capture Twilio recording"
msgstr "Gagal menangkap rekaman Twilio"

#: frontend/src/components/Settings/EmailAdd.vue:145
msgid "Failed to create email account, Invalid credentials"
msgstr "Gagal membuat akun email, kredensial tidak valid"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:179
msgid "Failed to create template"
msgstr "Gagal membuat templat"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:186
msgid "Failed to delete Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:216
msgid "Failed to delete template"
msgstr "Gagal menghapus templat"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:205
msgid "Failed to fetch exchange rate from {0} to {1} on {2}. Please check your internet connection or try again later."
msgstr "Gagal mengambil nilai tukar dari {0} ke {1} pada {2}. Harap periksa koneksi internet Anda atau coba lagi nanti."

#: frontend/src/data/script.js:106
msgid "Failed to load form controller: {0}"
msgstr "Gagal memuat kontroler formulir: {0}"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:242
msgid "Failed to rename template"
msgstr "Gagal mengganti nama templat"

#: crm/integrations/twilio/api.py:152
msgid "Failed to update Twilio call status"
msgstr "Gagal memperbarui status panggilan Twilio"

#: frontend/src/components/Settings/EmailEdit.vue:213
msgid "Failed to update email account, Invalid credentials"
msgstr "Gagal memperbarui akun email, kredensial tidak valid"

#: frontend/src/components/Modals/ChangePasswordModal.vue:95
msgid "Failed to update password"
msgstr "Gagal memperbarui kata sandi"

#: frontend/src/components/Settings/ProfileSettings.vue:151
msgid "Failed to update profile"
msgstr "Gagal memperbarui profil"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:171
msgid "Failed to update source"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:208
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:201
msgid "Failed to update template"
msgstr "Gagal memperbarui templat"

#. Option for the 'Type' (Select) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Failure"
msgstr ""

#. Label of the favicon (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/BrandSettings.vue:87
msgid "Favicon"
msgstr "Favicon"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:38
#: frontend/src/components/Modals/EditValueModal.vue:5
msgid "Field"
msgstr "Bidang"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:19
#: frontend/src/components/Kanban/KanbanSettings.vue:48
msgid "Fields Order"
msgstr "Urutan Bidang"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:333
msgid "File \"{0}\" was skipped because of invalid file type"
msgstr "File \"{0}\" dilewati karena tipe file tidak valid"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:354
msgid "File \"{0}\" was skipped because only {1} uploads are allowed"
msgstr "File \"{0}\" dilewati karena hanya {1} unggahan yang diizinkan"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:359
msgid "File \"{0}\" was skipped because only {1} uploads are allowed for DocType \"{2}\""
msgstr "File \"{0}\" dilewati karena hanya {1} unggahan yang diizinkan untuk DocType \"{2}\""

#: frontend/src/components/Filter.vue:6
msgid "Filter"
msgstr "Filter"

#. Label of the filters (Code) field in DocType 'CRM View Settings'
#. Label of the filters_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Filters"
msgstr "Filter"

#. Label of the first_name (Data) field in DocType 'CRM Deal'
#. Label of the first_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/ColumnSettings.vue:103
#: frontend/src/components/Filter.vue:57 frontend/src/components/Filter.vue:88
#: frontend/src/components/SortBy.vue:6 frontend/src/components/SortBy.vue:103
#: frontend/src/components/SortBy.vue:136
msgid "First Name"
msgstr "Nama Depan"

#: frontend/src/components/Modals/LeadModal.vue:134
msgid "First Name is mandatory"
msgstr "Nama Depan wajib diisi"

#. Label of the first_responded_on (Datetime) field in DocType 'CRM Deal'
#. Label of the first_responded_on (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Responded On"
msgstr "Pertama Kali Direspon Pada"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Due"
msgstr "Respon Pertama Jatuh Tempo"

#. Label of the first_response_time (Duration) field in DocType 'CRM Service
#. Level Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "First Response TIme"
msgstr "Waktu Respon Pertama"

#. Label of the first_response_time (Duration) field in DocType 'CRM Deal'
#. Label of the first_response_time (Duration) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Time"
msgstr "Waktu Respon Pertama"

#: frontend/src/components/Filter.vue:130
#: frontend/src/components/Settings/ProfileSettings.vue:78
msgid "First name"
msgstr "Nama depan"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:48
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:48
msgid "For"
msgstr "Untuk"

#: crm/api/dashboard.py:688
#: frontend/src/components/Dashboard/AddChartModal.vue:95
msgid "Forecasted revenue"
msgstr "Pendapatan yang diperkirakan"

#: frontend/src/components/Settings/ForecastingSettings.vue:5
#: frontend/src/components/Settings/Settings.vue:109
msgid "Forecasting"
msgstr ""

#: frontend/src/components/Settings/ForecastingSettings.vue:76
msgid "Forecasting disabled successfully"
msgstr "Forecasting berhasil dinonaktifkan"

#: frontend/src/components/Settings/ForecastingSettings.vue:75
msgid "Forecasting enabled successfully"
msgstr "Forecasting berhasil diaktifkan"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Form"
msgstr "Formulir"

#. Label of the form_name (Data) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Form Name"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:19
msgid "Form Script updated successfully"
msgstr "Skrip formulir berhasil diperbarui"

#. Name of a Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Frappe CRM"
msgstr "Frappe CRM"

#: frontend/src/components/Layouts/AppSidebar.vue:608
msgid "Frappe CRM mobile"
msgstr "Frappe CRM mobile"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Friday"
msgstr "Jumat"

#. Label of the from (Data) field in DocType 'CRM Call Log'
#. Label of the from (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From"
msgstr "Dari"

#. Label of the from_date (Date) field in DocType 'CRM Holiday List'
#. Label of the from_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Date"
msgstr "Tanggal Mulai"

#. Label of the from_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Type"
msgstr "Tipe Asal"

#. Label of the from_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "From User"
msgstr "Dari Pengguna"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Fulfilled"
msgstr "Terpenuhi"

#. Label of the full_name (Data) field in DocType 'CRM Contacts'
#. Label of the lead_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Full Name"
msgstr "Nama Lengkap"

#: crm/api/dashboard.py:755
#: frontend/src/components/Dashboard/AddChartModal.vue:96
msgid "Funnel conversion"
msgstr "Konversi Funnel"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:196
msgid "GMT+5:30"
msgstr ""

#. Label of the gender (Link) field in DocType 'CRM Contacts'
#. Label of the gender (Link) field in DocType 'CRM Deal'
#. Label of the gender (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Gender"
msgstr "Jenis Kelamin"

#: crm/api/dashboard.py:1065
msgid "Geographic distribution of deals and revenue"
msgstr "Distribusi geografis kesepakatan dan penerimaan"

#: frontend/src/components/Modals/AboutModal.vue:57
msgid "GitHub Repository"
msgstr "Repositori Github"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:582
msgid "Go back"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:156
msgid "Go to invite page"
msgstr ""

#: frontend/src/pages/Deal.vue:95 frontend/src/pages/Lead.vue:141
msgid "Go to website"
msgstr "Buka situs web"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Grid Row"
msgstr "Baris Grid"

#. Label of the group_by_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:379
#: frontend/src/components/ViewControls.vue:611 frontend/src/utils/view.js:16
msgid "Group By"
msgstr "Grup Berdasarkan"

#. Label of the group_by_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Group By Field"
msgstr "Grup Berdasarkan Bidang"

#: frontend/src/components/GroupBy.vue:8
msgid "Group By: "
msgstr "Grup Berdasarkan: "

#: frontend/src/components/Telephony/TwilioCallUI.vue:63
msgid "Hang up"
msgstr ""

#: crm/templates/emails/helpdesk_invitation.html:2
msgid "Hello"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:93
msgid "Help"
msgstr "Bantuan"

#: frontend/src/components/Settings/Settings.vue:203
msgid "Helpdesk"
msgstr ""

#. Name of a DocType
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk CRM Settings"
msgstr ""

#. Label of the helpdesk_site_apis_section (Section Break) field in DocType
#. 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk Site API's"
msgstr ""

#. Label of the helpdesk_site_url (Data) field in DocType 'Helpdesk CRM
#. Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk Site URL"
msgstr ""

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:18
msgid "Helpdesk is not installed in the current site"
msgstr ""

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:40
msgid "Helpdesk is not integrated with the CRM"
msgstr ""

#: frontend/src/components/Settings/HelpdeskSettings.vue:4
msgid "Helpdesk settings"
msgstr ""

#: frontend/src/components/Settings/HelpdeskSettings.vue:5
msgid "Helpdesk settings updated"
msgstr ""

#: frontend/src/components/CommunicationArea.vue:56
msgid "Hi John, \\n\\nCan you please provide more details on this..."
msgstr "Halo John,\\n\\nBisakah Anda memberikan detail lebih lanjut mengenai ini..."

#. Label of the hidden (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Hidden"
msgstr "Tersembunyi"

#: frontend/src/components/Activities/Activities.vue:237
msgid "Hide"
msgstr "Sembunyikan"

#: frontend/src/components/Controls/Password.vue:19
msgid "Hide Password"
msgstr "Sembunyikan Kata Sandi"

#: frontend/src/components/Activities/CallArea.vue:75
msgid "Hide Recording"
msgstr "Sembunyikan Rekaman"

#: frontend/src/components/FieldLayoutEditor.vue:354
msgid "Hide border"
msgstr "Sembunyikan border"

#: frontend/src/components/FieldLayoutEditor.vue:349
msgid "Hide label"
msgstr "Sembunyikan label"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Hide preview"
msgstr "Sembunyikan pratinjau"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "High"
msgstr "Tinggi"

#. Label of the holiday_list (Link) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Holiday List"
msgstr "Daftar Hari Libur"

#. Label of the holiday_list_name (Data) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holiday List Name"
msgstr "Nama Daftar Hari Libur"

#. Label of the holidays_section (Section Break) field in DocType 'CRM Holiday
#. List'
#. Label of the holidays (Table) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holidays"
msgstr "Hari Libur"

#: frontend/src/components/Settings/Settings.vue:174
msgid "Home Actions"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:542
#: frontend/src/components/Settings/HomeActions.vue:7
msgid "Home actions"
msgstr "Menu Cepat Beranda"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Hourly"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:199
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:283
msgid "I understand, add conditions"
msgstr ""

#. Label of the id (Data) field in DocType 'CRM Call Log'
#. Label of the id (Data) field in DocType 'Facebook Lead Form'
#. Label of the id (Data) field in DocType 'Facebook Lead Form Question'
#. Label of the id (Data) field in DocType 'Facebook Page'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "ID"
msgstr "ID"

#. Label of the icon (Code) field in DocType 'CRM Dropdown Item'
#. Label of the icon (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Icon"
msgstr "Ikon"

#: frontend/src/components/Settings/emailConfig.js:55
msgid "If enabled, all outgoing emails will be sent from this account. Note: Only one account can be default outgoing."
msgstr "Jika diaktifkan, semua email keluar akan dikirim dari akun ini. Catatan: Hanya satu akun yang dapat menjadi akun pengirim default."

#: frontend/src/components/Settings/emailConfig.js:47
msgid "If enabled, all replies to your company (eg: <EMAIL>) will come to this account. Note: Only one account can be default incoming."
msgstr "Jika diaktifkan, semua balasan ke perusahaan Anda (misalnya: <EMAIL>) akan masuk ke akun ini. Catatan: Hanya satu akun yang dapat menjadi akun penerima default."

#: frontend/src/components/Settings/emailConfig.js:39
msgid "If enabled, outgoing emails can be sent from this account."
msgstr "Jika diaktifkan, email keluar dapat dikirim dari akun ini."

#: frontend/src/components/Settings/emailConfig.js:31
msgid "If enabled, records can be created from the incoming emails on this account."
msgstr "Jika diaktifkan, catatan dapat dibuat dari email yang masuk di akun ini."

#. Label of the image (Attach Image) field in DocType 'CRM Lead'
#. Label of the image (Attach Image) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Image"
msgstr "Gambar"

#: frontend/src/components/Filter.vue:271
#: frontend/src/components/Filter.vue:292
#: frontend/src/components/Filter.vue:307
#: frontend/src/components/Filter.vue:320
#: frontend/src/components/Filter.vue:334
msgid "In"
msgstr "Di Dalam"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "In Progress"
msgstr "Sedang Berjalan"

#: frontend/src/components/SLASection.vue:68
msgid "In less than a minute"
msgstr "Dalam waktu kurang dari satu menit"

#: frontend/src/components/Activities/CallArea.vue:36
msgid "Inbound Call"
msgstr "Panggilan Masuk"

#: frontend/src/components/Settings/EmailAccountCard.vue:45
msgid "Inbox"
msgstr "Kotak Masuk"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Incoming"
msgstr "Masuk"

#: frontend/src/components/Telephony/TwilioCallUI.vue:41
msgid "Incoming call..."
msgstr "Panggilan masuk..."

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Industries"
msgstr "Industri"

#. Label of the industry (Link) field in DocType 'CRM Deal'
#. Label of the industry (Data) field in DocType 'CRM Industry'
#. Label of the industry (Link) field in DocType 'CRM Lead'
#. Label of the industry (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Industry"
msgstr "Industri"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Initiated"
msgstr "Diinisiasi"

#: frontend/src/components/Telephony/TwilioCallUI.vue:36
msgid "Initiating call..."
msgstr "Memulai panggilan..."

#: frontend/src/components/EmailEditor.vue:154
msgid "Insert Email Template"
msgstr ""

#: frontend/src/components/CommentBox.vue:49
#: frontend/src/components/EmailEditor.vue:130
msgid "Insert Emoji"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:598
msgid "Integration"
msgstr "Integrasi"

#: crm/integrations/exotel/handler.py:73
msgid "Integration Not Enabled"
msgstr "Integrasi Tidak Diaktifkan"

#: frontend/src/components/Settings/Settings.vue:182
msgctxt "FCRM"
msgid "Integrations"
msgstr "Integrasi"

#: frontend/src/components/Layouts/AppSidebar.vue:529
#: frontend/src/components/Layouts/AppSidebar.vue:532
msgid "Introduction"
msgstr "Pengantar"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:33
msgid "Invalid Account SID or Auth Token."
msgstr "Account SID atau Auth Token tidak valid."

#: frontend/src/components/Modals/DealModal.vue:212
#: frontend/src/components/Modals/LeadModal.vue:153
msgid "Invalid Email"
msgstr "Email Tidak Valid"

#: crm/integrations/exotel/handler.py:89
msgid "Invalid Exotel Number"
msgstr "Nomor Exotel Tidak Valid"

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:137
msgid "Invalid access token provided for Facebook."
msgstr ""

#: crm/api/dashboard.py:77
msgid "Invalid chart name"
msgstr "Nama bagan tidak valid"

#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.py:25
msgid "Invalid credentials"
msgstr "Kredensial tidak valid"

#: frontend/src/components/Settings/emailConfig.js:172
msgid "Invalid email ID"
msgstr "Alamat email tidak valid"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:601
msgid "Invalid fields, check if all are filled in and values are correct."
msgstr ""

#: frontend/src/pages/InvalidPage.vue:6
msgid "Invalid page or not permitted to access"
msgstr ""

#: frontend/src/composables/event.js:203
msgid "Invalid start or end time"
msgstr ""

#: frontend/src/components/Settings/Users.vue:25
msgid "Invite New User"
msgstr "Undang Pengguna Baru"

#: frontend/src/components/Settings/Settings.vue:136
msgid "Invite User"
msgstr "Undang Pengguna"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:78
#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:149
msgid "Invite agent"
msgstr ""

#: frontend/src/components/Settings/InviteUserPage.vue:51
msgid "Invite as"
msgstr "Undan sebagai"

#: frontend/src/components/Layouts/AppSidebar.vue:543
msgid "Invite users"
msgstr "Undang pengguna"

#: frontend/src/components/Settings/InviteUserPage.vue:10
msgid "Invite users to access CRM. Specify their roles to control access and permissions"
msgstr "Undang pengguna untuk mengakses CRM. Tentukan peran mereka untuk mengontrol akses dan izin"

#: frontend/src/components/Layouts/AppSidebar.vue:359
msgid "Invite your team"
msgstr "Undang tim Anda"

#. Label of the invited_by (Link) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Invited By"
msgstr "Diundang Sebagai"

#: frontend/src/components/Filter.vue:273
#: frontend/src/components/Filter.vue:282
#: frontend/src/components/Filter.vue:294
#: frontend/src/components/Filter.vue:309
#: frontend/src/components/Filter.vue:322
#: frontend/src/components/Filter.vue:336
#: frontend/src/components/Filter.vue:345
msgid "Is"
msgstr "Berstatus"

#. Label of the is_default (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Default"
msgstr "Default"

#. Label of the is_erpnext_in_different_site (Check) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Is ERPNext installed on a different site?"
msgstr "ERPNext terpasang di situs berbeda?"

#. Label of the is_group (Check) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Is Group"
msgstr "Grup"

#. Label of the is_helpdesk_in_different_site (Check) field in DocType
#. 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Is Helpdesk installed on a different site?"
msgstr ""

#. Label of the is_primary (Check) field in DocType 'CRM Contacts'
#. Label of the is_primary (Check) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Is Primary"
msgstr "Utama"

#. Label of the is_standard (Check) field in DocType 'CRM Dropdown Item'
#. Label of the is_standard (Check) field in DocType 'CRM Form Script'
#. Label of the is_standard (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Standard"
msgstr "Standar"

#. Description of the 'Enable Forecasting' (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "It will make deal's \"Expected Closure Date\" & \"Expected Deal Value\" mandatory to get accurate forecasting insights"
msgstr "Mengaktifkan ini akan mewajibkan pengisian \"Tanggal Perkiraan Penutupan\" & \"Nilai Perkiraan Kesepakatan\" untuk mendapatkan gambaran perkiraan yang akurat"

#. Label of the json (JSON) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "JSON"
msgstr "JSON"

#. Label of the job_title (Data) field in DocType 'CRM Deal'
#. Label of the job_title (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Job Title"
msgstr "Jabatan"

#: frontend/src/components/AssignToBody.vue:11
#: frontend/src/components/Filter.vue:74 frontend/src/components/Filter.vue:107
#: frontend/src/components/Modals/AssignmentModal.vue:13
#: frontend/src/components/Modals/TaskModal.vue:63
#: frontend/src/components/Telephony/TaskPanel.vue:47
msgid "John Doe"
msgstr "John Doe"

#. Label of the kanban_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:384
#: frontend/src/components/ViewControls.vue:600 frontend/src/utils/view.js:20
msgid "Kanban"
msgstr "Kanban"

#. Label of the kanban_columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Columns"
msgstr "Kolom Kanban"

#. Label of the kanban_fields (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Fields"
msgstr "Bidang Kanban"

#: frontend/src/components/Kanban/KanbanSettings.vue:3
#: frontend/src/components/Kanban/KanbanSettings.vue:8
msgid "Kanban Settings"
msgstr "Pengaturan Kanban"

#. Label of the key (Data) field in DocType 'CRM Invitation'
#. Label of the key (Data) field in DocType 'Facebook Lead Form Question'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Key"
msgstr "Kunci"

#. Label of the label (Data) field in DocType 'CRM Dropdown Item'
#. Label of the label (Data) field in DocType 'CRM View Settings'
#. Label of the label (Data) field in DocType 'Facebook Lead Form Question'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: frontend/src/components/ColumnSettings.vue:100
msgid "Label"
msgstr "Label"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:108
msgid "Last"
msgstr ""

#: frontend/src/components/Filter.vue:615
msgid "Last 6 Months"
msgstr "6 Bulan Terakhir"

#: frontend/src/components/Filter.vue:607
msgid "Last Month"
msgstr "Bulan Lalu"

#. Label of the last_name (Data) field in DocType 'CRM Deal'
#. Label of the last_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Last Name"
msgstr "Nama Belakang"

#: frontend/src/components/Filter.vue:611
msgid "Last Quarter"
msgstr "Kuartal Lalu"

#. Label of the last_status_change_log (Link) field in DocType 'CRM Status
#. Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Last Status Change Log"
msgstr "Log Perubahan Status Terakhir"

#. Label of the last_synced_at (Datetime) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:102
msgid "Last Synced At"
msgstr "Terakhir Disinkronkan Pada"

#: frontend/src/components/Filter.vue:603
msgid "Last Week"
msgstr "Minggu Lalu"

#: frontend/src/components/Filter.vue:619
msgid "Last Year"
msgstr "Tahun Lalu"

#: frontend/src/pages/Contact.vue:533 frontend/src/pages/MobileContact.vue:533
#: frontend/src/pages/MobileOrganization.vue:477
#: frontend/src/pages/MobileOrganization.vue:505
#: frontend/src/pages/Organization.vue:507
#: frontend/src/pages/Organization.vue:535
msgid "Last modified"
msgstr "Terakhir diubah"

#: frontend/src/components/Settings/ProfileSettings.vue:83
msgid "Last name"
msgstr "Nama belakang"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:101
msgid "Last user assigned by this rule"
msgstr ""

#. Label of the layout (Code) field in DocType 'CRM Dashboard'
#. Label of the layout (Code) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Layout"
msgstr "Layout"

#. Label of the lead (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:263
#: frontend/src/components/Calendar/CalendarEventPanel.vue:288
#: frontend/src/components/Layouts/AppSidebar.vue:550
#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:19
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:55
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:77
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:55
#: frontend/src/components/Telephony/ExotelCallUI.vue:200
#: frontend/src/pages/Tasks.vue:133
msgid "Lead"
msgstr "Prospek"

#. Label of the lead_data (Code) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Lead Data"
msgstr ""

#. Label of the lead_details_tab (Tab Break) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Details"
msgstr "Detail Prospek"

#. Label of the lead_name (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Name"
msgstr "Nama Prospek"

#. Label of the lead_owner (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Lead Owner"
msgstr "Pemilik Prospek"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:120
msgid "Lead Owner cannot be same as the Lead Email Address"
msgstr "Pemilik Prospek tidak boleh sama dengan Alamat Email Prospek"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Sources"
msgstr "Sumber Prospek"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Statuses"
msgstr "Status Prospek"

#. Name of a DocType
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:240
msgid "Lead Sync Source created successfully"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:183
msgid "Lead Sync Source deleted successfully"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:209
msgid "Lead Syncing"
msgstr ""

#: crm/api/dashboard.py:974
msgid "Lead generation channel analysis"
msgstr "Analisis saluran perolehan prospek"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:7
msgid "Lead sources"
msgstr ""

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.js:8
msgid "Lead sync initiated."
msgstr ""

#: crm/api/dashboard.py:756
msgid "Lead to deal conversion pipeline"
msgstr "Pipeline konversi prospek menjadi kesepakatan"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/InvalidPage.vue:9 frontend/src/pages/Lead.vue:340
#: frontend/src/pages/MobileLead.vue:291
msgid "Leads"
msgstr "Prospek"

#: crm/api/dashboard.py:973
#: frontend/src/components/Dashboard/AddChartModal.vue:106
msgid "Leads by source"
msgstr "Prospek berdasarkan sumber"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:158
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:238
msgid "Learn about conditions"
msgstr ""

#. Label of the lft (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Left"
msgstr "Left"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:43
msgid "Library"
msgstr "Pustaka"

#: frontend/src/components/Filter.vue:269
#: frontend/src/components/Filter.vue:280
#: frontend/src/components/Filter.vue:290
#: frontend/src/components/Filter.vue:318
#: frontend/src/components/Filter.vue:332
msgid "Like"
msgstr "Mirip"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:252
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:47
msgid "Link"
msgstr "Tautan"

#. Label of the links (Table) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Links"
msgstr "Tautan"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#. Label of the list_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:374
#: frontend/src/components/ViewControls.vue:589 frontend/src/utils/view.js:12
msgid "List"
msgstr "Daftar"

#: frontend/src/components/Activities/CallArea.vue:75
msgid "Listen"
msgstr "Dengarkan"

#. Label of the load_default_columns (Check) field in DocType 'CRM View
#. Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Load Default Columns"
msgstr "Muat Kolom Default"

#: frontend/src/components/Kanban/KanbanView.vue:140
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:142
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:120
#: frontend/src/components/Settings/Users.vue:155
msgid "Load More"
msgstr "Muat lebih banyak"

#: frontend/src/components/Activities/Activities.vue:22
#: frontend/src/components/Activities/DataFields.vue:35
#: frontend/src/pages/Deal.vue:176 frontend/src/pages/MobileDeal.vue:117
msgid "Loading..."
msgstr "Memuat..."

#. Label of the log_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the log_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Log"
msgstr "Log"

#: frontend/src/components/Activities/Activities.vue:814
#: frontend/src/components/Activities/ActivityHeader.vue:133
#: frontend/src/components/Activities/ActivityHeader.vue:176
msgid "Log a Call"
msgstr "Catat Panggilan"

#: frontend/src/composables/frappecloud.js:23
msgid "Login to Frappe Cloud?"
msgstr "Masuk ke Frappe Cloud?"

#. Label of the brand_logo (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Logo"
msgstr "Logo"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Lost"
msgstr "Gagal"

#. Label of the lost_notes (Text) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lost Notes"
msgstr "Catatan Kegagalan"

#. Label of the lost_reason (Link) field in DocType 'CRM Deal'
#. Label of the lost_reason (Data) field in DocType 'CRM Lost Reason'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "Lost Reason"
msgstr "Alasan Kegagalan"

#: crm/api/dashboard.py:919
#: frontend/src/components/Dashboard/AddChartModal.vue:98
msgid "Lost deal reasons"
msgstr "Alasan gagalnya kesepakatan"

#: frontend/src/components/Modals/LostReasonModal.vue:27
msgid "Lost notes"
msgstr "Catatan kegagalan"

#: frontend/src/components/Modals/LostReasonModal.vue:83
msgid "Lost notes are required when lost reason is \"Other\""
msgstr "Catatan kegagalan wajib diisi jika alasan gagal adalah \"Lainnya\""

#: frontend/src/components/Modals/LostReasonModal.vue:4
#: frontend/src/components/Modals/LostReasonModal.vue:14
msgid "Lost reason"
msgstr "Alasan gagal"

#: frontend/src/components/Modals/LostReasonModal.vue:79
msgid "Lost reason is required"
msgstr "Alasan gagal wajib diisi"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Low"
msgstr "Rendah"

#: frontend/src/pages/Contact.vue:100 frontend/src/pages/MobileContact.vue:73
msgid "Make Call"
msgstr "Lakukan Panggilan"

#: frontend/src/components/ViewControls.vue:1101
msgid "Make Private"
msgstr "Jadikan Privat"

#: frontend/src/components/ViewControls.vue:1101
msgid "Make Public"
msgstr "Jadikan Publik"

#: frontend/src/components/Activities/Activities.vue:818
#: frontend/src/components/Activities/ActivityHeader.vue:138
#: frontend/src/components/Activities/ActivityHeader.vue:181
#: frontend/src/pages/Deals.vue:505 frontend/src/pages/Leads.vue:532
msgid "Make a Call"
msgstr "Lakukan Panggilan"

#: frontend/src/pages/Deal.vue:81 frontend/src/pages/Lead.vue:123
msgid "Make a call"
msgstr "Lakukan panggilan"

#: frontend/src/components/Activities/AttachmentArea.vue:98
msgid "Make attachment {0}"
msgstr "Buat lampiran {0}"

#: frontend/src/components/Telephony/CallUI.vue:7
msgid "Make call"
msgstr "Lakukan panggilan"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make private"
msgstr "Jadikan privat"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make public"
msgstr "Jadikan publik"

#: frontend/src/components/Activities/AttachmentArea.vue:107
msgid "Make {0}"
msgstr "Buat {0}"

#: frontend/src/components/Telephony/CallUI.vue:34
msgid "Make {0} as default calling medium"
msgstr "Jadikan {0} sebagai media panggilan default"

#: frontend/src/components/Settings/ForecastingSettings.vue:24
msgid "Makes \"Expected Closure Date\" and \"Expected Deal Value\" mandatory for deal value forecasting"
msgstr "Menjadikan \"Tanggal Penutupan yang Diharapkan\" dan \"Nilai Kesepakatan yang Diharapkan\" wajib diisi untuk prakiraan nilai kesepakatan"

#: frontend/src/components/Settings/Users.vue:11
msgid "Manage CRM users by adding or inviting them, and assign roles to control their access and permissions"
msgstr "Kelola pengguna CRM dengan menambah atau mengundang mereka, dan berikan peran untuk mengontrol akses dan izin mereka"

#: frontend/src/components/Settings/EmailAccountList.vue:11
msgid "Manage your email accounts to send and receive emails directly from CRM. You can add multiple accounts and set one as default for incoming and outgoing emails."
msgstr "Kelola akun email Anda untuk mengirim dan menerima email langsung dari CRM. Anda dapat menambahkan beberapa akun dan menetapkan salah satunya sebagai default untuk email masuk dan keluar."

#: frontend/src/components/Modals/AddExistingUserModal.vue:93
#: frontend/src/components/Settings/InviteUserPage.vue:157
#: frontend/src/components/Settings/InviteUserPage.vue:164
#: frontend/src/components/Settings/Users.vue:87
#: frontend/src/components/Settings/Users.vue:193
#: frontend/src/components/Settings/Users.vue:253
#: frontend/src/components/Settings/Users.vue:256
msgid "Manager"
msgstr "Manajer"

#: frontend/src/data/document.js:57
msgid "Mandatory field error: {0}"
msgstr "Kesalahan bidang wajib: {0}"

#: frontend/src/data/document.js:187 frontend/src/data/document.js:190
msgid "Mandatory fields required: {0}"
msgstr ""

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Manual"
msgstr "Manual"

#. Label of the mapped_to_crm_field (Autocomplete) field in DocType 'Facebook
#. Lead Form Question'
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Mapped to CRM Field"
msgstr ""

#: frontend/src/components/Notifications.vue:20
#: frontend/src/pages/MobileNotification.vue:12
#: frontend/src/pages/MobileNotification.vue:13
msgid "Mark all as read"
msgstr "Tandai semua sebagai telah dibaca"

#: frontend/src/components/Layouts/AppSidebar.vue:547
msgid "Masters"
msgstr "Data Master"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:209
#: frontend/src/components/Modals/EventModal.vue:86
msgid "May 1, 2025"
msgstr ""

#. Label of the medium (Data) field in DocType 'CRM Call Log'
#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Medium"
msgstr "Media"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Mention"
msgstr "Sebutan"

#. Label of the message (HTML Editor) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Message"
msgstr "Pesan"

#. Label of the middle_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Middle Name"
msgstr "Nama Tengah"

#: frontend/src/components/Telephony/ExotelCallUI.vue:127
msgid "Minimize"
msgstr ""

#. Label of the mobile_no (Data) field in DocType 'CRM Contacts'
#. Label of the mobile_no (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Mobile No"
msgstr "Nomor Ponsel"

#: frontend/src/components/Modals/DealModal.vue:208
#: frontend/src/components/Modals/LeadModal.vue:149
msgid "Mobile No should be a number"
msgstr "Nomor Ponsel harus berupa angka"

#. Label of the mobile_no (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Mobile No."
msgstr "No. Ponsel"

#: frontend/src/components/Telephony/CallUI.vue:22
msgid "Mobile Number"
msgstr "Nomor Ponsel"

#: crm/integrations/exotel/handler.py:93
msgid "Mobile Number Missing"
msgstr "Nomor Ponsel Hilang"

#: frontend/src/components/Layouts/AppSidebar.vue:611
msgid "Mobile app installation"
msgstr "Instalasi aplikasi seluler"

#: frontend/src/pages/Contact.vue:523 frontend/src/pages/MobileContact.vue:523
#: frontend/src/pages/MobileOrganization.vue:467
#: frontend/src/pages/Organization.vue:497
msgid "Mobile no"
msgstr "Nomor Ponsel"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Monday"
msgstr "Senin"

#: crm/api/dashboard.py:691
msgid "Month"
msgstr "Bulan"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Monthly"
msgstr "Bulanan"

#: frontend/src/components/ViewControls.vue:221
msgid "More Options"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:448
msgid "Move to next section"
msgstr "Pindah ke bagian berikutnya"

#: frontend/src/components/FieldLayoutEditor.vue:401
msgid "Move to next tab"
msgstr "Pindah ke tab berikutnya"

#: frontend/src/components/FieldLayoutEditor.vue:458
msgid "Move to previous section"
msgstr "Pindah ke bagian sebelumnya"

#: frontend/src/components/FieldLayoutEditor.vue:387
msgid "Move to previous tab"
msgstr "Pindah ke tab sebelumnya"

#: frontend/src/components/Modals/ViewModal.vue:29
msgid "My Open Deals"
msgstr "Kesepakatan Terbuka Saya"

#. Label of the title (Data) field in DocType 'CRM Dashboard'
#. Label of the name1 (Data) field in DocType 'CRM Dropdown Item'
#. Label of the brand_name (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:51
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:52
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:39
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:39
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:59
#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:22
#: frontend/src/components/ViewControls.vue:779
#: frontend/src/pages/MobileOrganization.vue:485
#: frontend/src/pages/Organization.vue:515
msgid "Name"
msgstr "Nama"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:417
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:151
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:154
msgid "Name is required"
msgstr "Nama wajib diisi"

#. Label of the naming_series (Select) field in DocType 'CRM Deal'
#. Label of the naming_series (Select) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Naming Series"
msgstr "Seri Penamaan"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:99
msgid "Nested conditions"
msgstr ""

#. Label of the net_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Net Amount"
msgstr "Jumlah Bersih"

#. Label of the net_total (Currency) field in DocType 'CRM Deal'
#. Label of the net_total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Net Total"
msgstr "Total Bersih"

#: frontend/src/components/Activities/ActivityHeader.vue:76
#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:19
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:19
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:20
#: frontend/src/components/Settings/Users.vue:30
msgid "New"
msgstr "Baru"

#: frontend/src/components/Modals/AddressModal.vue:93
msgid "New Address"
msgstr "Alamat baru"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:12
msgid "New Assignment Rule"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:44
msgid "New Assignment Rule Name"
msgstr ""

#: frontend/src/components/Modals/CallLogModal.vue:97
msgid "New Call Log"
msgstr "Log Panggilan Baru"

#: frontend/src/components/Activities/Activities.vue:398
#: frontend/src/components/Activities/ActivityHeader.vue:19
#: frontend/src/components/Activities/ActivityHeader.vue:123
msgid "New Comment"
msgstr "Komentar Baru"

#: frontend/src/components/Modals/ContactModal.vue:8
msgid "New Contact"
msgstr "Kontak baru"

#: frontend/src/components/Activities/Activities.vue:393
#: frontend/src/components/Activities/ActivityHeader.vue:12
#: frontend/src/components/Activities/ActivityHeader.vue:118
msgid "New Email"
msgstr "Email Baru"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:9
msgid "New Lead Sync Source"
msgstr ""

#: frontend/src/components/Activities/ActivityHeader.vue:66
msgid "New Message"
msgstr "Pesan Baru"

#: frontend/src/components/Activities/ActivityHeader.vue:41
#: frontend/src/components/Activities/ActivityHeader.vue:144
#: frontend/src/pages/Deals.vue:511 frontend/src/pages/Leads.vue:538
msgid "New Note"
msgstr "Catatan Baru"

#: frontend/src/components/Modals/OrganizationModal.vue:8
msgid "New Organization"
msgstr "Organisasi Baru"

#: frontend/src/components/Modals/ChangePasswordModal.vue:6
msgid "New Password"
msgstr "Kata Sandi Baru"

#: frontend/src/components/FieldLayoutEditor.vue:201
#: frontend/src/components/SidePanelLayoutEditor.vue:131
msgid "New Section"
msgstr "Bagian Baru"

#: frontend/src/components/FieldLayoutEditor.vue:293
#: frontend/src/components/FieldLayoutEditor.vue:298
msgid "New Tab"
msgstr "Tab Baru"

#: frontend/src/components/Activities/ActivityHeader.vue:48
#: frontend/src/components/Activities/ActivityHeader.vue:149
#: frontend/src/pages/Deals.vue:516 frontend/src/pages/Leads.vue:543
msgid "New Task"
msgstr "Tugas Baru"

#: frontend/src/components/Activities/ActivityHeader.vue:159
msgid "New WhatsApp Message"
msgstr "Pesan WhatsApp Baru"

#: frontend/src/components/Modals/ConvertToDealModal.vue:67
#: frontend/src/pages/MobileLead.vue:162
msgid "New contact will be created based on the person's details"
msgstr "Kontak baru akan dibuat berdasarkan detail orang tersebut"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:431
msgid "New event"
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:42
#: frontend/src/pages/MobileLead.vue:136
msgid "New organization will be created based on the data in details section"
msgstr "Organisasi baru akan dibuat berdasarkan data di bagian detail"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "New template"
msgstr "Templat baru"

#: frontend/src/components/Modals/CreateDocumentModal.vue:88
msgid "New {0}"
msgstr "Baru {0}"

#: frontend/src/components/Filter.vue:663
msgid "Next 6 Months"
msgstr "6 Bulan ke Depan"

#: frontend/src/components/Filter.vue:655
msgid "Next Month"
msgstr "Bulan Depan"

#: frontend/src/components/Filter.vue:659
msgid "Next Quarter"
msgstr "Kuartal Depan"

#. Label of the next_step (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Next Step"
msgstr "Langkah Selanjutnya"

#: frontend/src/components/Filter.vue:651
msgid "Next Week"
msgstr "Minggu Depan"

#: frontend/src/components/Filter.vue:667
msgid "Next Year"
msgstr "Tahun Depan"

#: frontend/src/components/Controls/Grid.vue:27
msgid "No"
msgstr "Tidak"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "No Answer"
msgstr "Tidak Dijawab"

#: frontend/src/components/Controls/Grid.vue:322
msgid "No Data"
msgstr "Tidak Ada Data"

#: frontend/src/components/Activities/EventArea.vue:78
msgid "No Events Scheduled"
msgstr ""

#: frontend/src/components/Kanban/KanbanView.vue:103
#: frontend/src/pages/Deals.vue:105 frontend/src/pages/Leads.vue:121
#: frontend/src/pages/Tasks.vue:71
msgid "No Title"
msgstr "Tanpa Judul"

#: frontend/src/components/Settings/EmailEdit.vue:150
msgid "No changes made"
msgstr "Tidak ada perubahan yang dibuat"

#: frontend/src/components/Modals/SidePanelModal.vue:51
#: frontend/src/pages/Deal.vue:271 frontend/src/pages/MobileDeal.vue:205
msgid "No contacts added"
msgstr "Tidak ada kontak yang ditambahkan"

#: frontend/src/pages/Deal.vue:257
msgid "No details added"
msgstr ""

#: frontend/src/pages/Deal.vue:90 frontend/src/pages/Lead.vue:137
msgid "No email set"
msgstr "Tidak ada email yang diatur"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:48
msgid "No email templates found"
msgstr "Tidak ada templat email yang ditemukan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:14
msgid "No items in the list"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:92
msgid "No label"
msgstr "Tanpa label"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:49
msgid "No lead sources found"
msgstr ""

#: frontend/src/pages/Deal.vue:707
msgid "No mobile number set"
msgstr "Tidak ada nomor seluler yang diatur"

#: frontend/src/components/Notifications.vue:77
#: frontend/src/pages/MobileNotification.vue:61
msgid "No new notifications"
msgstr "Tidak ada notifikasi baru"

#: frontend/src/pages/Lead.vue:129
msgid "No phone number set"
msgstr "Tidak ada nomor telepon yang diatur"

#: frontend/src/pages/Deal.vue:702
msgid "No primary contact set"
msgstr "Tidak ada kontak utama yang diatur"

#: frontend/src/components/Calendar/Attendee.vue:232
#: frontend/src/components/Controls/EmailMultiSelect.vue:133
#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:70
msgid "No results found"
msgstr "Hasil tidak ditemukan"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:66
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:42
msgid "No templates found"
msgstr "Templat tidak ditemukan"

#: frontend/src/components/Modals/AddExistingUserModal.vue:39
#: frontend/src/components/Settings/Users.vue:57
msgid "No users found"
msgstr "Pengguna tidak ditemukan"

#: frontend/src/pages/MobileOrganization.vue:281
#: frontend/src/pages/Organization.vue:318
msgid "No website found"
msgstr "Situs web tidak ditemukan"

#: frontend/src/pages/Deal.vue:100 frontend/src/pages/Lead.vue:146
msgid "No website set"
msgstr "Situs web belum diatur"

#: frontend/src/components/PrimaryDropdown.vue:33
msgid "No {0} Available"
msgstr "Tidak Ada {0} yang Tersedia"

#: frontend/src/pages/CallLogs.vue:59 frontend/src/pages/Contact.vue:162
#: frontend/src/pages/Contacts.vue:58 frontend/src/pages/Deals.vue:234
#: frontend/src/pages/Leads.vue:260 frontend/src/pages/MobileContact.vue:147
#: frontend/src/pages/MobileOrganization.vue:139
#: frontend/src/pages/Notes.vue:95 frontend/src/pages/Organization.vue:158
#: frontend/src/pages/Organizations.vue:58 frontend/src/pages/Tasks.vue:184
msgid "No {0} Found"
msgstr "{0} tidak ditemukan"

#. Label of the no_of_employees (Select) field in DocType 'CRM Deal'
#. Label of the no_of_employees (Select) field in DocType 'CRM Lead'
#. Label of the no_of_employees (Select) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "No. of Employees"
msgstr "Jumlah Karyawan"

#: frontend/src/components/Activities/AudioPlayer.vue:148
msgid "Normal"
msgstr "Normal"

#: crm/utils/__init__.py:263
msgid "Not Allowed"
msgstr "Tidak Diizinkan"

#: frontend/src/components/Filter.vue:268
#: frontend/src/components/Filter.vue:289
#: frontend/src/components/Filter.vue:306
#: frontend/src/components/Filter.vue:317
#: frontend/src/components/Filter.vue:344
msgid "Not Equals"
msgstr "Tidak Sama Dengan"

#: frontend/src/components/Filter.vue:272
#: frontend/src/components/Filter.vue:293
#: frontend/src/components/Filter.vue:308
#: frontend/src/components/Filter.vue:321
#: frontend/src/components/Filter.vue:335
msgid "Not In"
msgstr "Tidak Di Dalam"

#: frontend/src/components/Filter.vue:270
#: frontend/src/components/Filter.vue:281
#: frontend/src/components/Filter.vue:291
#: frontend/src/components/Filter.vue:319
#: frontend/src/components/Filter.vue:333
msgid "Not Like"
msgstr "Tidak Cocok"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:10
#: frontend/src/components/Controls/GridRowFieldsModal.vue:10
#: frontend/src/components/Modals/DataFieldsModal.vue:10
#: frontend/src/components/Modals/QuickEntryModal.vue:10
#: frontend/src/components/Modals/SidePanelModal.vue:10
#: frontend/src/components/Settings/SettingsPage.vue:11
#: frontend/src/components/Settings/TelephonySettings.vue:11
msgid "Not Saved"
msgstr "Tidak Disimpan"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:133
msgid "Not Synced"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:272
msgid "Not allowed to add contact to Deal"
msgstr "Tidak diizinkan menambahkan kontak ke Kesepakatan"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:455
msgid "Not allowed to convert Lead to Deal"
msgstr "Tidak diizinkan mengonversi Prospek menjadi Kesepakatan"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:283
msgid "Not allowed to remove contact from Deal"
msgstr "Tidak diizinkan menghapus kontak dari Kesepakatan"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:294
msgid "Not allowed to set primary contact for Deal"
msgstr "Tidak diizinkan mengatur kontak utama untuk Kesepakatan"

#. Label of the note (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: frontend/src/components/Layouts/AppSidebar.vue:554
msgid "Note"
msgstr "Catatan"

#: frontend/src/pages/Deal.vue:572 frontend/src/pages/Lead.vue:419
#: frontend/src/pages/MobileDeal.vue:461 frontend/src/pages/MobileLead.vue:367
msgid "Notes"
msgstr "Catatan"

#: frontend/src/pages/Notes.vue:23
msgid "Notes View"
msgstr "Tampilan Catatan"

#: frontend/src/components/Activities/EmailArea.vue:15
#: frontend/src/components/Layouts/AppSidebar.vue:583
msgid "Notification"
msgstr "Notifikasi"

#. Label of the notification_text (Text) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Text"
msgstr "Teks Notifikasi"

#. Label of the notification_type_doc (Dynamic Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doc"
msgstr "Dokumen Tipe Notifikasi"

#. Label of the notification_type_doctype (Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doctype"
msgstr "DocType Tipe Notifikasi"

#: frontend/src/components/Layouts/AppSidebar.vue:13
#: frontend/src/components/Mobile/MobileSidebar.vue:23
#: frontend/src/components/Notifications.vue:17
#: frontend/src/pages/MobileNotification.vue:6
msgid "Notifications"
msgstr "Notifikasi"

#. Label of the number (Data) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Number"
msgstr "Nomor"

#: frontend/src/components/Dashboard/AddChartModal.vue:19
#: frontend/src/components/Dashboard/AddChartModal.vue:69
msgid "Number chart"
msgstr "Bagan Angka"

#: crm/api/dashboard.py:1072 crm/api/dashboard.py:1132
msgid "Number of deals"
msgstr "Jumlah kesepakatan"

#: crm/api/dashboard.py:1125
msgid "Number of deals and total value per salesperson"
msgstr "Jumlah kesepakatan dan nilai total per tenaga penjualan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:167
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:251
msgid "Old Condition"
msgstr ""

#. Label of the old_parent (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Old Parent"
msgstr "Induk Lama"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "On Hold"
msgstr "Ditunda"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Ongoing"
msgstr "Sedang Berjalan"

#: crm/api/dashboard.py:190
#: frontend/src/components/Dashboard/AddChartModal.vue:77
msgid "Ongoing deals"
msgstr "Kesepakatan yang sedang berjalan"

#: frontend/src/utils/index.js:474
msgid "Only image files are allowed"
msgstr "Hanya file gambar yang diizinkan"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:60
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.py:23
msgid "Only one {0} can be set as primary."
msgstr "Hanya satu {0} yang dapat dijadikan utama."

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Open"
msgstr "Buka"

#: frontend/src/components/Modals/NoteModal.vue:13
#: frontend/src/components/Modals/TaskModal.vue:13
msgid "Open Deal"
msgstr "Buka Kesepakatan"

#: frontend/src/components/Modals/NoteModal.vue:14
#: frontend/src/components/Modals/TaskModal.vue:14
msgid "Open Lead"
msgstr "Buka Prospek"

#: crm/fcrm/doctype/crm_deal/crm_deal.js:6
#: crm/fcrm/doctype/crm_lead/crm_lead.js:6
msgid "Open in Portal"
msgstr "Buka di Portal"

#. Label of the open_in_new_window (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Open in new window"
msgstr "Buka di jendela baru"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:88
msgid "Open nested conditions"
msgstr ""

#: frontend/src/pages/Organization.vue:96
msgid "Open website"
msgstr "Buka situs web"

#: frontend/src/components/Kanban/KanbanView.vue:218
#: frontend/src/components/Modals/CallLogDetailModal.vue:15
#: frontend/src/components/ViewControls.vue:199
msgid "Options"
msgstr "Opsi"

#: frontend/src/pages/Welcome.vue:40
msgid "Or create leads manually"
msgstr "Atau buat prospek secara manual"

#. Label of the order_by_tab (Tab Break) field in DocType 'CRM View Settings'
#. Label of the order_by (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Order By"
msgstr "Urutkan Berdasarkan"

#. Label of the organization (Link) field in DocType 'CRM Deal'
#. Label of the organization_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the organization (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Layouts/AppSidebar.vue:553
#: frontend/src/components/Modals/ConvertToDealModal.vue:25
#: frontend/src/pages/Contact.vue:502 frontend/src/pages/MobileContact.vue:502
#: frontend/src/pages/MobileLead.vue:118
#: frontend/src/pages/MobileOrganization.vue:446
#: frontend/src/pages/MobileOrganization.vue:500
#: frontend/src/pages/Organization.vue:476
#: frontend/src/pages/Organization.vue:530
msgid "Organization"
msgstr "Organisasi"

#. Label of the organization_details_section (Section Break) field in DocType
#. 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Organization Details"
msgstr "Detail Organisasi"

#. Label of the organization_logo (Attach Image) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Logo"
msgstr "Logo Organisasi"

#. Label of the organization_name (Data) field in DocType 'CRM Deal'
#. Label of the organization_name (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Name"
msgstr "Nama Organisasi"

#: frontend/src/pages/Deal.vue:62
msgid "Organization logo"
msgstr "Logo organisasi"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/MobileOrganization.vue:205
#: frontend/src/pages/Organization.vue:243
msgid "Organizations"
msgstr "Organisasi"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:107
msgid "Organizer"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:575
msgid "Other features"
msgstr "Fitur Lainnya"

#. Label of the organization_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Others"
msgstr "Lainnya"

#: frontend/src/components/Activities/CallArea.vue:37
msgid "Outbound Call"
msgstr "Panggilan Keluar"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Outgoing"
msgstr "Keluar"

#. Label of the log_owner (Link) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Owner"
msgstr "Pemilik"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:102
msgid "Owner: {0}"
msgstr ""

#. Label of the page (Link) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Page"
msgstr "Halaman"

#. Label of the page_name (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Page Name"
msgstr ""

#. Label of the parent_crm_territory (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Parent CRM Territory"
msgstr "Wilayah CRM Induk"

#: frontend/src/components/Settings/emailConfig.js:64
msgid "Password"
msgstr "Kata Sandi"

#: crm/api/demo.py:21 crm/api/demo.py:29
msgid "Password cannot be reset by Demo User {}"
msgstr "Kata sandi tidak dapat direset oleh Pengguna Demo {}"

#: frontend/src/components/Settings/emailConfig.js:175
msgid "Password is required"
msgstr "Kata sandi wajib diisi"

#: frontend/src/components/Modals/ChangePasswordModal.vue:88
msgid "Password updated successfully"
msgstr "Kata sandi berhasil diperbarui"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:13
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:38
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:38
msgid "Payment Reminder"
msgstr "Pengingat Pembayaran"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:69
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:69
msgid "Payment Reminder from Frappé - (#{{ name }})"
msgstr "Pengingat Pembayaran dari Frappé - (#{{ name }})"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Pending"
msgstr "Tertunda"

#: frontend/src/components/Settings/InviteUserPage.vue:61
msgid "Pending Invites"
msgstr "Undangan Tertunda"

#: frontend/src/pages/Dashboard.vue:66
msgid "Period"
msgstr "Periode"

#. Label of the person_section (Section Break) field in DocType 'CRM Deal'
#. Label of the person_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Person"
msgstr "Orang"

#: frontend/src/components/Settings/Settings.vue:90
msgid "Personal Settings"
msgstr ""

#. Label of the phone (Data) field in DocType 'CRM Contacts'
#. Label of the phone (Data) field in DocType 'CRM Lead'
#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/pages/MobileOrganization.vue:495
#: frontend/src/pages/Organization.vue:525
msgid "Phone"
msgstr "Telepon"

#. Label of the phone_nos (Table) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Phone Numbers"
msgstr "Nomor Telepon"

#: frontend/src/components/ViewControls.vue:1093
msgid "Pin View"
msgstr "Sematkan Tampilan"

#. Label of the pinned (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Pinned"
msgstr "Disematkan"

#: frontend/src/components/ViewControls.vue:677
msgid "Pinned Views"
msgstr "Tampilan yang Disematkan"

#: frontend/src/components/Layouts/AppSidebar.vue:571
msgid "Pinned view"
msgstr "Tampilan yang disematkan"

#: frontend/src/components/Activities/AudioPlayer.vue:176
msgid "Playback speed"
msgstr "Kecepatan pemutaran"

#: frontend/src/components/Settings/EmailAccountList.vue:49
msgid "Please add an email account to continue."
msgstr "Cantumkan akun email untuk melanjutkan."

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:157
msgid "Please check your access token"
msgstr ""

#: crm/integrations/twilio/twilio_handler.py:119
msgid "Please enable twilio settings before making a call."
msgstr "Harap aktifkan pengaturan Twilio sebelum melakukan panggilan."

#: frontend/src/components/FilesUploader/FilesUploader.vue:165
msgid "Please enter a valid URL"
msgstr "Harap masukkan URL yang valid"

#: frontend/src/components/Settings/CurrencySettings.vue:150
msgid "Please enter the Exchangerate Host access key."
msgstr "Harap masukkan kunci akses Exchangerate Host."

#: frontend/src/components/Modals/LostReasonModal.vue:9
msgid "Please provide a reason for marking this deal as lost"
msgstr "Mohon berikan alasan mengapa kesepakatan ini ditandai gagal"

#: frontend/src/components/Settings/CurrencySettings.vue:143
msgid "Please select a currency before saving."
msgstr "Harap pilih mata uang sebelum menyimpan."

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.py:65
msgid "Please select a lead gen form before syncing!"
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:136
#: frontend/src/pages/MobileLead.vue:431
msgid "Please select an existing contact"
msgstr "Harap pilih kontak yang sudah ada"

#: frontend/src/components/Modals/ConvertToDealModal.vue:141
#: frontend/src/pages/MobileLead.vue:436
msgid "Please select an existing organization"
msgstr "Harap pilih organisasi yang sudah ada"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:126
msgid "Please set Email Address"
msgstr "Silahkan tetapkan Alamat Email"

#: crm/integrations/exotel/handler.py:73
msgid "Please setup Exotel intergration"
msgstr "Harap atur integrasi Exotel"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:181
msgid "Please specify a reason for losing the deal."
msgstr "Mohon sebutkan alasan kesepakatan ini gagal."

#: crm/fcrm/doctype/crm_deal/crm_deal.py:183
msgid "Please specify the reason for losing the deal."
msgstr "Mohon sebutkan alasan utama kesepakatan ini gagal."

#. Label of the position (Int) field in DocType 'CRM Deal Status'
#. Label of the position (Int) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Position"
msgstr "Posisi"

#: frontend/src/pages/Deal.vue:205 frontend/src/pages/MobileDeal.vue:149
msgid "Primary"
msgstr "Utama"

#. Label of the email (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Email"
msgstr "Email Utama"

#. Label of the mobile_no (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Mobile No"
msgstr "No Ponsel Utama"

#. Label of the phone (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Phone"
msgstr "Nomor Telepon Utama"

#: frontend/src/pages/Deal.vue:679 frontend/src/pages/MobileDeal.vue:566
msgid "Primary contact set"
msgstr "Kontak utama ditetapkan"

#. Label of the priorities (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Priorities"
msgstr "Prioritas"

#. Label of the priority (Link) field in DocType 'CRM Service Level Priority'
#. Label of the priority (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:64
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:20
msgid "Priority"
msgstr "Prioritas"

#. Label of the private (Check) field in DocType 'CRM Dashboard'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:89
msgid "Private"
msgstr "Privat"

#. Label of the probability (Percent) field in DocType 'CRM Deal'
#. Label of the probability (Percent) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Probability"
msgstr "Probabilitas"

#. Label of the product_code (Link) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product"
msgstr "Produk"

#. Label of the product_code (Data) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Product Code"
msgstr "Kode Produk"

#. Label of the product_name (Data) field in DocType 'CRM Product'
#. Label of the product_name (Data) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product Name"
msgstr "Nama Produk"

#. Label of the products_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the products (Table) field in DocType 'CRM Deal'
#. Label of the products_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the products (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Products"
msgstr "Produk"

#: frontend/src/components/Layouts/AppSidebar.vue:540
#: frontend/src/components/Settings/Settings.vue:94
msgid "Profile"
msgstr "Profil"

#: frontend/src/components/Settings/ProfileSettings.vue:147
msgid "Profile updated successfully"
msgstr "Profil berhasil diperbarui"

#: crm/api/dashboard.py:689
msgid "Projected vs actual revenue based on deal probability"
msgstr "Pendapatan yang diproyeksikan vs pendapatan aktual berdasarkan probabilitas kesepakatan"

#. Label of the public (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Public"
msgstr "Publik"

#: frontend/src/components/ViewControls.vue:672
msgid "Public Views"
msgstr "Tampilan Publik"

#: frontend/src/components/Layouts/AppSidebar.vue:570
msgid "Public view"
msgstr "Tampilan Publik"

#. Label of the qty (Float) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Quantity"
msgstr "Kuantitas"

#. Label of the questions (Table) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Questions"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Queued"
msgstr "Dalam Antrian"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Quick Entry"
msgstr "Entri Cepat"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Quick Filters"
msgstr "Filter Cepat"

#: frontend/src/components/ViewControls.vue:731
msgid "Quick Filters updated successfully"
msgstr "Filter Cepat berhasil diperbarui"

#: frontend/src/components/Layouts/AppSidebar.vue:594
msgid "Quick entry layout"
msgstr "Layout entri cepat"

#. Label of the rate (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Rate"
msgstr "Harga"

#. Label of the read (Check) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Read"
msgstr "Dibaca"

#: crm/api/dashboard.py:922
msgid "Reason"
msgstr "Alasan"

#. Label of the record_calls (Check) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Record Calls"
msgstr "Rekam Panggilan"

#. Label of the record_call (Check) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Record Outgoing Calls"
msgstr "Rekam Panggilan Keluar"

#. Label of the recording_url (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Recording URL"
msgstr "URL Rekaman"

#. Label of the reference_name (Dynamic Link) field in DocType 'CRM
#. Notification'
#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Task'
#. Label of the reference_docname (Dynamic Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Doc"
msgstr "Dokumen Referensi"

#. Label of the reference_doctype (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Reference Doctype"
msgstr "Doctype Referensi"

#. Label of the reference_doctype (Link) field in DocType 'CRM Call Log'
#. Label of the reference_doctype (Link) field in DocType 'CRM Task'
#. Label of the reference_doctype (Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Document Type"
msgstr "Tipe Dokumen Referensi"

#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Call
#. Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Reference Name"
msgstr "Nama Referensi"

#: frontend/src/components/ViewControls.vue:26
#: frontend/src/components/ViewControls.vue:159
#: frontend/src/pages/Dashboard.vue:10
msgid "Refresh"
msgstr "Segarkan"

#: frontend/src/components/Telephony/TwilioCallUI.vue:97
msgid "Reject"
msgstr "Tolak"

#: frontend/src/components/Telephony/TwilioCallUI.vue:163
msgid "Reject call"
msgstr ""

#: frontend/src/components/ConditionsFilter/CFCondition.vue:192
#: frontend/src/components/Controls/ImageUploader.vue:25
#: frontend/src/components/Settings/Users.vue:218
#: frontend/src/pages/Deal.vue:628
msgid "Remove"
msgstr "Hapus"

#: frontend/src/components/FilesUploader/FilesUploader.vue:23
msgid "Remove all"
msgstr "Hapus semua"

#: frontend/src/components/FieldLayoutEditor.vue:438
msgid "Remove and move fields to previous column"
msgstr "Hapus dan pindahkan bidang ke kolom sebelumnya"

#: frontend/src/components/FieldLayoutEditor.vue:432
msgid "Remove column"
msgstr "Hapus kolom"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:200
msgid "Remove group"
msgstr ""

#: frontend/src/components/Settings/ProfileSettings.vue:32
#: frontend/src/pages/Contact.vue:53 frontend/src/pages/Lead.vue:94
#: frontend/src/pages/MobileContact.vue:43
#: frontend/src/pages/MobileOrganization.vue:43
#: frontend/src/pages/Organization.vue:53
msgid "Remove image"
msgstr "Hapus gambar"

#: frontend/src/components/FieldLayoutEditor.vue:359
msgid "Remove section"
msgstr "Hapus bagian"

#: frontend/src/components/FieldLayoutEditor.vue:318
msgid "Remove tab"
msgstr "Hapus tab"

#: frontend/src/components/Activities/EmailArea.vue:34
#: frontend/src/components/CommunicationArea.vue:10
msgid "Reply"
msgstr "Balas"

#: frontend/src/components/Activities/EmailArea.vue:41
msgid "Reply All"
msgstr "Balas Semua"

#: frontend/src/components/Modals/AboutModal.vue:72
msgid "Report an Issue"
msgstr "Laporkan masalah"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Required Fields"
msgstr "Bidang Wajib"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:79
#: frontend/src/components/Controls/GridRowFieldsModal.vue:30
#: frontend/src/components/Modals/DataFieldsModal.vue:30
#: frontend/src/components/Modals/QuickEntryModal.vue:30
#: frontend/src/components/Modals/SidePanelModal.vue:30
msgid "Reset"
msgstr "Reset"

#: frontend/src/components/ColumnSettings.vue:78
msgid "Reset Changes"
msgstr "Reset Perubahan"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:7
msgid "Reset ERPNext Form Script"
msgstr "Reset Skrip Formulir ERPNext"

#: frontend/src/components/ColumnSettings.vue:86
msgid "Reset to Default"
msgstr "Reset ke Pengaturan Default"

#: frontend/src/pages/Dashboard.vue:28
msgid "Reset to default"
msgstr "Atur ulang ke setelan default"

#. Label of the response_by (Datetime) field in DocType 'CRM Deal'
#. Label of the response_by (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response By"
msgstr "Batas Respon"

#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Deal'
#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response Details"
msgstr "Detail Respon"

#. Label of the section_break_ufaf (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Response and Follow Up"
msgstr "Respon dan Tindak Lanjut"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:14
msgid "Restore"
msgstr "Pulihkan"

#. Label of the restore_defaults (Button) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:13
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Restore Defaults"
msgstr "Kembalikan ke Pengaturan Default"

#: frontend/src/components/FilesUploader/FilesUploader.vue:51
msgid "Retake"
msgstr "Ambil ulang"

#: crm/api/dashboard.py:697
msgid "Revenue"
msgstr "Pendapatan"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:81
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:81
msgid "Rich Text"
msgstr "Teks Berformat"

#. Label of the rgt (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Right"
msgstr "Right"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Ringing"
msgstr "Berdering"

#: frontend/src/components/Telephony/TwilioCallUI.vue:38
#: frontend/src/components/Telephony/TwilioCallUI.vue:140
msgid "Ringing..."
msgstr "Berdering..."

#. Label of the role (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:46
msgid "Role"
msgstr "Peran"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#. Label of the route (Data) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Route"
msgstr "Route"

#. Label of the route_name (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Route Name"
msgstr "Nama Route"

#. Label of the rows (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Rows"
msgstr "Baris"

#. Label of the sla_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the sla (Link) field in DocType 'CRM Deal'
#. Label of the sla_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the sla (Link) field in DocType 'CRM Lead'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "SLA"
msgstr "SLA"

#. Label of the sla_creation (Datetime) field in DocType 'CRM Deal'
#. Label of the sla_creation (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Creation"
msgstr "Pembuatan SLA"

#. Label of the sla_name (Data) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "SLA Name"
msgstr "Nama SLA"

#. Label of the sla_status (Select) field in DocType 'CRM Deal'
#. Label of the sla_status (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Status"
msgstr "Status SLA"

#: frontend/src/components/EmailEditor.vue:85
msgid "SUBJECT"
msgstr "SUBJEK"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Sales Manager"
msgstr "Manajer Penjualan"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:92
#: frontend/src/components/Settings/InviteUserPage.vue:156
#: frontend/src/components/Settings/InviteUserPage.vue:163
#: frontend/src/components/Settings/Users.vue:88
#: frontend/src/components/Settings/Users.vue:194
#: frontend/src/components/Settings/Users.vue:264
#: frontend/src/components/Settings/Users.vue:267
msgid "Sales User"
msgstr "Pengguna Penjualan"

#: crm/api/dashboard.py:617
#: frontend/src/components/Dashboard/AddChartModal.vue:94
msgid "Sales trend"
msgstr "Tren Penjualan"

#: frontend/src/pages/Dashboard.vue:93
msgid "Sales user"
msgstr "Pengguna penjualan"

#: crm/api/dashboard.py:1127
msgid "Salesperson"
msgstr "Tenaga Penjualan"

#. Label of the salutation (Link) field in DocType 'CRM Deal'
#. Label of the salutation (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Salutation"
msgstr "Sapaan"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Saturday"
msgstr "Sabtu"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:345
#: frontend/src/components/Controls/GridFieldsEditorModal.vue:84
#: frontend/src/components/Controls/GridRowFieldsModal.vue:26
#: frontend/src/components/Modals/AddressModal.vue:98
#: frontend/src/components/Modals/CallLogModal.vue:101
#: frontend/src/components/Modals/DataFieldsModal.vue:26
#: frontend/src/components/Modals/LostReasonModal.vue:44
#: frontend/src/components/Modals/QuickEntryModal.vue:26
#: frontend/src/components/Modals/SidePanelModal.vue:26
#: frontend/src/components/PrimaryDropdownItem.vue:21
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:36
#: frontend/src/components/Settings/CurrencySettings.vue:173
#: frontend/src/components/Telephony/ExotelCallUI.vue:222
#: frontend/src/components/ViewControls.vue:125
#: frontend/src/pages/Dashboard.vue:36
msgid "Save"
msgstr "Simpan"

#: frontend/src/components/Modals/ViewModal.vue:40
#: frontend/src/components/ViewControls.vue:58
#: frontend/src/components/ViewControls.vue:155
msgid "Save Changes"
msgstr "Simpan Perubahan"

#: frontend/src/components/ViewControls.vue:667
msgid "Saved Views"
msgstr "Tampilan Tersimpan"

#: frontend/src/components/Layouts/AppSidebar.vue:569
msgid "Saved view"
msgstr "Tampilan tersimpan"

#: frontend/src/components/Telephony/TaskPanel.vue:8
msgid "Schedule a task..."
msgstr "Jadwalkan tugas..."

#: frontend/src/components/Activities/EventArea.vue:79
msgid "Schedule an Event"
msgstr ""

#: frontend/src/components/Activities/ActivityHeader.vue:36
#: frontend/src/components/Activities/ActivityHeader.vue:128
msgid "Schedule an event"
msgstr ""

#. Label of the script (Code) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Script"
msgstr "Skrip"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:22
msgid "Search"
msgstr "Pencarian"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:64
msgid "Search template"
msgstr "Cari templat"

#: frontend/src/components/Settings/Users.vue:73
msgid "Search user"
msgstr "Cari pengguna"

#: frontend/src/components/FieldLayoutEditor.vue:336
msgid "Section"
msgstr "Bagian"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:130
msgid "See all participants"
msgstr ""

#: frontend/src/pages/Dashboard.vue:50
msgid "Select Range"
msgstr "Pilih Rentang"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:47
msgid "Select Source Type"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:51
msgid "Select currency"
msgstr "Pilih mata uang"

#: frontend/src/components/Settings/CurrencySettings.vue:75
msgid "Select provider"
msgstr "Pilih penyedia"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:84
msgid "Select the assignees for {0}."
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:345
msgid "Select {0}"
msgstr "Pilih {0}"

#: frontend/src/components/EmailEditor.vue:165
msgid "Send"
msgstr "Kirim"

#: frontend/src/components/Settings/InviteUserPage.vue:18
msgid "Send Invites"
msgstr "Kirim Undangan"

#: frontend/src/components/Activities/ActivityHeader.vue:61
msgid "Send Template"
msgstr "Kirim Templat"

#: frontend/src/pages/Deal.vue:87 frontend/src/pages/Lead.vue:134
msgid "Send an email"
msgstr "Kirim email"

#: frontend/src/components/Layouts/AppSidebar.vue:460
msgid "Send email"
msgstr "Kirim email"

#: frontend/src/components/Settings/InviteUserPage.vue:6
msgid "Send invites to"
msgstr "Kirim undangan ke"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Separator"
msgstr "Pemisah"

#. Label of the naming_series (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Series"
msgstr "Seri Penamaan"

#. Label of the service_provider (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Service Provider"
msgstr "Penyedia Layanan"

#: frontend/src/components/Layouts/AppSidebar.vue:581
msgid "Service level agreement"
msgstr "Persetujuan tingkat layanan"

#: frontend/src/components/PrimaryDropdownItem.vue:27
msgid "Set As Primary"
msgstr ""

#: frontend/src/components/FilesUploader/FilesUploader.vue:66
msgid "Set all as private"
msgstr "Atur semua sebagai privat"

#: frontend/src/components/FilesUploader/FilesUploader.vue:59
msgid "Set all as public"
msgstr "Atur semua sebagai publik"

#: frontend/src/pages/Deal.vue:73
msgid "Set an organization"
msgstr "Atur organisasi"

#: frontend/src/pages/Deal.vue:636 frontend/src/pages/MobileDeal.vue:523
msgid "Set as Primary Contact"
msgstr "Atur sebagai Kontak Utama"

#: frontend/src/components/ViewControls.vue:1078
msgid "Set as default"
msgstr "Atur sebagai default"

#: frontend/src/components/Settings/CurrencySettings.vue:164
msgid "Set currency"
msgstr "Atur mata uang"

#: frontend/src/pages/Lead.vue:115
msgid "Set first name"
msgstr "Atur nama depan"

#: frontend/src/components/Layouts/AppSidebar.vue:533
msgid "Setting up"
msgstr "Penyiapan"

#: frontend/src/components/Settings/emailConfig.js:145
msgid "Setting up Frappe Mail requires you to have an API key and API Secret of your email account. Read more "
msgstr "Untuk mengatur Frappe Mail, Anda harus memiliki API key dan API Secret dari akun email Anda. Baca selengkapnya "

#: frontend/src/components/Settings/emailConfig.js:97
msgid "Setting up GMail requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "Untuk mengatur GMail, Anda harus mengaktifkan autentikasi dua faktor dan kata sandi spesifik aplikasi. Baca selengkapnya"

#: frontend/src/components/Settings/emailConfig.js:105
msgid "Setting up Outlook requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "Untuk mengatur Outlook, Anda harus mengaktifkan autentikasi dua faktor dan kata sandi spesifik aplikasi. Baca selengkapnya"

#: frontend/src/components/Settings/emailConfig.js:113
msgid "Setting up Sendgrid requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Untuk mengatur Sendgrid, Anda harus mengaktifkan autentikasi dua faktor dan kata sandi spesifik aplikasi. Baca selengkapnya "

#: frontend/src/components/Settings/emailConfig.js:121
msgid "Setting up SparkPost requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Untuk mengatur SparkPost, Anda harus mengaktifkan autentikasi dua faktor dan kata sandi spesifik aplikasi. Baca selengkapnya "

#: frontend/src/components/Settings/emailConfig.js:129
msgid "Setting up Yahoo requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Untuk mengatur Yahoo, Anda harus mengaktifkan autentikasi dua faktor dan kata sandi spesifik aplikasi. Baca selengkapnya "

#: frontend/src/components/Settings/emailConfig.js:137
msgid "Setting up Yandex requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "Untuk mengatur Yandex, Anda harus mengaktifkan autentikasi dua faktor dan kata sandi spesifik aplikasi. Baca selengkapnya "

#. Label of the defaults_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Layouts/AppSidebar.vue:537
#: frontend/src/components/Settings/Settings.vue:12
msgid "Settings"
msgstr "Pengaturan"

#: frontend/src/components/Settings/EmailAdd.vue:6
msgid "Setup Email"
msgstr "Atur Email"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:199
msgid "Setup the Exchange Rate Provider as 'Exchangerate Host' in settings, as default provider does not support currency conversion for {0} to {1}."
msgstr "Siapkan Penyedia Nilai Tukar sebagai 'Exchangerate Host' di pengaturan, karena penyedia default tidak mendukung konversi mata uang untuk {0} ke {1}."

#: frontend/src/components/Layouts/AppSidebar.vue:339
msgid "Setup your password"
msgstr "Atur kata sandi Anda"

#: frontend/src/components/Activities/Activities.vue:237
msgid "Show"
msgstr "Tampilkan"

#: frontend/src/components/Controls/Password.vue:19
msgid "Show Password"
msgstr "Tampilkan Kata Sandi"

#: frontend/src/components/FieldLayoutEditor.vue:354
msgid "Show border"
msgstr "Tampilkan border"

#: frontend/src/components/FieldLayoutEditor.vue:349
msgid "Show label"
msgstr "Tampilkan label"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:138
msgid "Show less"
msgstr ""

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Show preview"
msgstr "Tampilkan pratinjau"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Side Panel"
msgstr "Panel Samping"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Sidebar Items"
msgstr "Item Bilah Sisi"

#. Description of the 'Condition' (Code) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Simple Python Expression, Example: doc.status == 'Open' and doc.lead_source == 'Ads'"
msgstr "Ekspresi Python Sederhana, Contoh: doc.status == 'Open' and doc.lead_source == 'Ads'"

#: frontend/src/components/AssignTo.vue:83
msgid "Since you removed {0} from the assignee, the {0} has also been removed."
msgstr "Karena Anda menghapus {0} dari penerima tugas, {0} juga telah dihapus."

#: frontend/src/components/AssignTo.vue:76
msgid "Since you removed {0} from the assignee, the {0} has been changed to the next available assignee {1}."
msgstr "Karena Anda menghapus {0} dari penanggung jawab, maka {0} telah dialihkan ke penanggung jawab berikutnya yang tersedia, yaitu {1}."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:735
msgid "Some error occurred while renaming assignment rule"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:720
msgid "Some error occurred while updating assignment rule"
msgstr ""

#: frontend/src/components/SortBy.vue:10 frontend/src/components/SortBy.vue:24
#: frontend/src/components/SortBy.vue:232
msgid "Sort"
msgstr "Urutkan"

#. Label of the source (Link) field in DocType 'CRM Deal'
#. Label of the source (Link) field in DocType 'CRM Lead'
#. Label of the source (Link) field in DocType 'Failed Lead Sync Log'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: frontend/src/components/Modals/EditValueModal.vue:10
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:60
msgid "Source"
msgstr "Sumber"

#. Label of the source_name (Data) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:69
msgid "Source Name"
msgstr "Nama Sumber"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:46
msgid "Source Type"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:167
msgid "Source disabled successfully"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:166
msgid "Source enabled successfully"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:68
#: frontend/src/components/Dashboard/DashboardItem.vue:21
msgid "Spacer"
msgstr "Pemberi Jarak"

#: crm/api/dashboard.py:758 crm/api/dashboard.py:820
msgid "Stage"
msgstr "Tahap"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:15
msgid "Standard Form Scripts can not be modified, duplicate the Form Script instead."
msgstr "Skrip Formulir Standar tidak dapat diubah, silakan duplikasi untuk melakukan perubahan."

#. Label of the standard_rate (Currency) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Standard Selling Rate"
msgstr "Harga Jual Standar"

#: frontend/src/components/ViewControls.vue:634
msgid "Standard Views"
msgstr "Tampilan Standar"

#. Label of the start_date (Date) field in DocType 'CRM Service Level
#. Agreement'
#. Label of the start_date (Date) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Start Date"
msgstr "Tanggal Mulai"

#. Label of the start_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the start_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:234
#: frontend/src/components/Modals/EventModal.vue:103
msgid "Start Time"
msgstr "Waktu Mulai"

#: frontend/src/composables/event.js:198
msgid "Start and end time are required"
msgstr ""

#: frontend/src/pages/Welcome.vue:21
msgid "Start with sample 10 leads"
msgstr "Mulai dengan 10 contoh prospek"

#. Label of the status (Select) field in DocType 'CRM Call Log'
#. Label of the status (Data) field in DocType 'CRM Communication Status'
#. Label of the status (Link) field in DocType 'CRM Deal'
#. Label of the deal_status (Data) field in DocType 'CRM Deal Status'
#. Label of the status (Select) field in DocType 'CRM Invitation'
#. Label of the status (Link) field in DocType 'CRM Lead'
#. Label of the lead_status (Data) field in DocType 'CRM Lead Status'
#. Label of the status (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_task/crm_task.json frontend/src/pages/Contact.vue:513
#: frontend/src/pages/MobileContact.vue:513
#: frontend/src/pages/MobileOrganization.vue:457
#: frontend/src/pages/Organization.vue:487
msgid "Status"
msgstr "Status"

#. Label of the status_change_log (Table) field in DocType 'CRM Deal'
#. Label of the status_change_log (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Status Change Log"
msgstr "Log Perubahan Status"

#: frontend/src/components/Modals/DealModal.vue:216
#: frontend/src/components/Modals/LeadModal.vue:157
msgid "Status is required"
msgstr "Status wajib diisi"

#. Label of the subdomain (Data) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Subdomain"
msgstr "Subdomain"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:68
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:68
msgid "Subject"
msgstr "Subjek"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:155
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:158
msgid "Subject is required"
msgstr "Subjek wajib diisi"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:45
msgid "Subject: {0}"
msgstr "Subjek {0}"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Sunday"
msgstr "Minggu"

#: frontend/src/components/Settings/emailConfig.js:16
msgid "Support / Sales"
msgstr "Dukungan / Penjualan"

#: frontend/src/components/FilesUploader/FilesUploader.vue:46
msgid "Switch camera"
msgstr "Ganti kamera"

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.js:6
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:23
msgid "Sync Now"
msgstr ""

#: frontend/src/pages/Welcome.vue:32
msgid "Sync your contacts,email and calenders"
msgstr "Sinkronkan kontak, email, dan kalender Anda"

#. Label of the syncing_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Syncing"
msgstr "Sinkronisasi"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:359
msgid "Syncing started in background"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:106
msgid "System Configuration"
msgstr ""

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "System Manager"
msgstr "System Manager"

#: frontend/src/components/EmailEditor.vue:22
msgid "TO"
msgstr "KEPADA"

#: frontend/src/components/Telephony/ExotelCallUI.vue:149
msgid "Take a note..."
msgstr "Tulis catatan..."

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:555
msgid "Task"
msgstr "Tugas"

#: frontend/src/pages/Deal.vue:567 frontend/src/pages/Lead.vue:414
#: frontend/src/pages/MobileDeal.vue:456 frontend/src/pages/MobileLead.vue:362
msgid "Tasks"
msgstr "Tugas"

#: frontend/src/components/Modals/AboutModal.vue:67
msgid "Telegram Channel"
msgstr "Kanal Telegram"

#: frontend/src/components/Settings/Settings.vue:185
msgid "Telephony"
msgstr "Telepon"

#. Label of the telephony_medium (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Telephony Medium"
msgstr "Media Telepon"

#: frontend/src/components/Settings/TelephonySettings.vue:8
msgid "Telephony settings"
msgstr "Pengaturan telepon"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:175
msgid "Template created successfully"
msgstr "Templat berhasil dibuat"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:213
msgid "Template deleted successfully"
msgstr "Templat berhasil dihapus"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:197
msgid "Template disabled successfully"
msgstr "Templat berhasil dinonaktifkan"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:196
msgid "Template enabled successfully"
msgstr "Templat berhasil diaktifkan"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:83
msgid "Template name"
msgstr "Nama templat"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:239
msgid "Template renamed successfully"
msgstr "Templat berhasil diganti nama"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:204
msgid "Template updated successfully"
msgstr "Tempalt berhasil diperbarui"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Territories"
msgstr "Wilayah"

#. Label of the territory (Link) field in DocType 'CRM Deal'
#. Label of the territory (Link) field in DocType 'CRM Lead'
#. Label of the territory (Link) field in DocType 'CRM Organization'
#: crm/api/dashboard.py:1067 crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Territory"
msgstr "Wilayah"

#. Label of the territory_manager (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Manager"
msgstr "Manajer Wilayah"

#. Label of the territory_name (Data) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Name"
msgstr "Nama Wilayah"

#: crm/templates/emails/helpdesk_invitation.html:16
msgid "Thanks"
msgstr ""

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:46
msgid "The Condition '{0}' is invalid: {1}"
msgstr "Kondisi '{0}' tidak valid: {1}"

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "The rate used to convert the deal’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "Kurs yang digunakan untuk mengonversi mata uang kesepakatan ke mata uang dasar CRM Anda (diatur di Pengaturan CRM). Nilai ini ditetapkan sekali saat mata uang pertama kali ditambahkan dan tidak berubah secara otomatis."

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "The rate used to convert the organization’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "Kurs yang digunakan untuk mengonversi mata uang organisasi ke mata uang dasar CRM Anda (diatur di Pengaturan CRM). Nilai ini ditetapkan sekali saat mata uang pertama kali ditambahkan dan tidak berubah secara otomatis."

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.js:14
msgid "There can only be one default priority in Priorities table"
msgstr "Tabel Prioritas hanya boleh memiliki satu prioritas default"

#: frontend/src/components/Modals/AddressModal.vue:128
#: frontend/src/components/Modals/CallLogModal.vue:131
msgid "These fields are required: {0}"
msgstr "Bidang berikut wajib diisi: {0}"

#: frontend/src/components/Filter.vue:639
msgid "This Month"
msgstr "Bulan Ini"

#: frontend/src/components/Filter.vue:643
msgid "This Quarter"
msgstr "Kuartal Ini"

#: frontend/src/components/Filter.vue:635
msgid "This Week"
msgstr "Minggu Ini"

#: frontend/src/components/Filter.vue:647
msgid "This Year"
msgstr "Tahun Ini"

#: frontend/src/components/SidePanelLayoutEditor.vue:116
msgid "This section is not editable"
msgstr "Bagian ini tidak bisa diedit"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:59
msgid "This will delete selected items and items linked to it, are you sure?"
msgstr "Tindakan ini akan menghapus item terpilih beserta item lain yang tertaut. Apakah Anda yakin?"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:62
msgid "This will delete selected items and unlink linked items to it, are you sure?"
msgstr "Tindakan ini akan menghapus item terpilih dan melepas item lain yang tertaut dengannya, Anda yakin?"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:9
msgid "This will restore (if not exist) all the default statuses, custom fields and layouts. Delete & Restore will delete default layouts and then restore them."
msgstr "Ini akan memulihkan (jika belum ada) semua status default, bidang kustom, dan layout. Tindakan \"Hapus & Pulihkan\" akan menghapus layout default lalu memulihkannya kembali."

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Thursday"
msgstr "Kamis"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:227
msgid "Time"
msgstr "Waktu"

#: frontend/src/components/Filter.vue:351
msgid "Timespan"
msgstr "Rentang Waktu"

#. Label of the title (Data) field in DocType 'CRM Task'
#. Label of the title (Data) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/EventModal.vue:43
#: frontend/src/components/Modals/NoteModal.vue:26
#: frontend/src/components/Modals/TaskModal.vue:25
msgid "Title"
msgstr "Judul"

#. Label of the title_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:29
msgid "Title Field"
msgstr "Bidang Judul"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:545
#: frontend/src/components/Modals/EventModal.vue:320
msgid "Title is required"
msgstr ""

#. Label of the to (Data) field in DocType 'CRM Call Log'
#. Label of the to (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
#: frontend/src/components/Activities/EmailArea.vue:53
msgid "To"
msgstr "Ke"

#. Label of the to_date (Date) field in DocType 'CRM Holiday List'
#. Label of the to_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Date"
msgstr "Tanggal Selesai"

#. Label of the to_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Type"
msgstr "Tipe Baru"

#. Label of the to_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "To User"
msgstr "Kepada Pengguna"

#: frontend/src/components/Settings/EmailEdit.vue:118
msgid "To know more about setting up email accounts, click"
msgstr "Untuk mengetahui lebih lanjut tentang pengaturan akun email, klik"

#: frontend/src/components/Filter.vue:627 frontend/src/pages/Calendar.vue:79
msgid "Today"
msgstr "Hari Ini"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Todo"
msgstr "Akan Dikerjakan"

#: frontend/src/components/Modals/SidePanelModal.vue:59
msgid "Toggle on for preview"
msgstr "Aktifkan untuk pratinjau"

#: frontend/src/components/Filter.vue:631
msgid "Tomorrow"
msgstr "Besok"

#: frontend/src/components/Modals/NoteModal.vue:42
#: frontend/src/components/Modals/TaskModal.vue:46
msgid "Took a call with John Doe and discussed the new project."
msgstr "Melakukan panggilan dengan John Doe dan membahas proyek baru."

#. Label of the total (Currency) field in DocType 'CRM Deal'
#. Label of the total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total"
msgstr "Total"

#. Label of the total_holidays (Int) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Total Holidays"
msgstr "Total Hari Libur"

#. Description of the 'Net Total' (Currency) field in DocType 'CRM Deal'
#. Description of the 'Net Total' (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total after discount"
msgstr "Total setelah diskon"

#: crm/api/dashboard.py:129
#: frontend/src/components/Dashboard/AddChartModal.vue:76
msgid "Total leads"
msgstr "Total prospek"

#: crm/api/dashboard.py:130
msgid "Total number of leads"
msgstr "Jumlah total prospek"

#: crm/api/dashboard.py:191
msgid "Total number of non won/lost deals"
msgstr "Jumlah total kesepakatan yang sedang berjalan"

#: crm/api/dashboard.py:311
msgid "Total number of won deals based on its closure date"
msgstr "Jumlah total kesepakatan yang berhasil berdasarkan tanggal penutupannya"

#. Label of the traceback (Code) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Traceback"
msgstr ""

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Tuesday"
msgstr "Selasa"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:173
msgid "Turn into a group"
msgstr ""

#. Label of the twiml_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "TwiML SID"
msgstr "TwiML SID"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the twilio (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:601
#: frontend/src/components/Settings/TelephonySettings.vue:40
#: frontend/src/components/Settings/TelephonySettings.vue:50
msgid "Twilio"
msgstr "Twilio"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:59
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:60
msgid "Twilio API credential creation error."
msgstr "Kesalahan pembuatan kredensial API Twilio."

#. Label of the twilio_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Twilio Number"
msgstr "Nomor Twilio"

#: frontend/src/components/Settings/TelephonySettings.vue:289
msgid "Twilio is not enabled"
msgstr "Twilio tidak diaktifkan"

#: frontend/src/components/Settings/TelephonySettings.vue:125
msgid "Twilio settings updated successfully"
msgstr "Pengaturan Twilio berhasil diperbarui"

#. Label of the type (Select) field in DocType 'CRM Call Log'
#. Label of the type (Select) field in DocType 'CRM Deal Status'
#. Label of the type (Select) field in DocType 'CRM Dropdown Item'
#. Label of the type (Select) field in DocType 'CRM Fields Layout'
#. Label of the type (Select) field in DocType 'CRM Global Settings'
#. Label of the type (Select) field in DocType 'CRM Notification'
#. Label of the type (Select) field in DocType 'CRM View Settings'
#. Label of the type (Data) field in DocType 'Facebook Lead Form Question'
#. Label of the type (Select) field in DocType 'Failed Lead Sync Log'
#. Label of the type (Select) field in DocType 'Lead Sync Source'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Type"
msgstr "Tipe"

#: frontend/src/components/Calendar/Attendee.vue:233
msgid "Type an email address to add attendee"
msgstr ""

#: frontend/src/components/Activities/WhatsAppBox.vue:85
msgid "Type your message here..."
msgstr "Ketik pesan Anda di sini..."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:454
msgid "Unassign conditions are invalid"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:224
msgid "Unassignment condition"
msgstr ""

#: crm/integrations/exotel/handler.py:170
msgid "Unauthorized request"
msgstr "Permintaan yang tidak sah"

#: frontend/src/components/FieldLayoutEditor.vue:344
msgid "Uncollapsible"
msgstr "Tidak Dapat Diciutkan"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:183
msgid "Ungroup conditions"
msgstr ""

#: frontend/src/components/Telephony/TwilioCallUI.vue:24
#: frontend/src/components/Telephony/TwilioCallUI.vue:123
msgid "Unknown"
msgstr "Tidak diketahui"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:132
msgid "Unlink"
msgstr "Lepas tautan"

#: frontend/src/components/DeleteLinkedDocModal.vue:78
msgid "Unlink all"
msgstr "Lepas semua tautan"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
msgid "Unlink and delete"
msgstr "Lepas tautan dan hapus"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:35
msgid "Unlink and delete {0} items"
msgstr "Lepas tautan dan hapus {0} item"

#: frontend/src/components/DeleteLinkedDocModal.vue:243
msgid "Unlink linked item"
msgstr "Lepaskan item tertaut"

#: frontend/src/components/DeleteLinkedDocModal.vue:79
msgid "Unlink {0} item(s)"
msgstr "Lepas tautan {0} item"

#: frontend/src/components/ViewControls.vue:1093
msgid "Unpin View"
msgstr "Lepas Sematan Tampilan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:22
msgid "Unsaved"
msgstr ""

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:575
msgid "Unsaved changes"
msgstr ""

#: frontend/src/components/FieldLayoutEditor.vue:26
#: frontend/src/components/Modals/AddressModal.vue:8
#: frontend/src/components/Modals/CallLogModal.vue:8
#: frontend/src/components/Modals/CreateDocumentModal.vue:8
#: frontend/src/components/Section.vue:21
#: frontend/src/components/SidePanelLayoutEditor.vue:19
msgid "Untitled"
msgstr "Tanpa Judul"

#: frontend/src/components/ColumnSettings.vue:129
#: frontend/src/components/Modals/AssignmentModal.vue:79
#: frontend/src/components/Modals/ChangePasswordModal.vue:45
#: frontend/src/components/Modals/EventModal.vue:156
#: frontend/src/components/Modals/NoteModal.vue:52
#: frontend/src/components/Modals/TaskModal.vue:106
#: frontend/src/components/Settings/BrandSettings.vue:15
#: frontend/src/components/Settings/CurrencySettings.vue:17
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:21
#: frontend/src/components/Settings/HomeActions.vue:15
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:30
#: frontend/src/components/Settings/ProfileSettings.vue:95
#: frontend/src/components/Settings/SettingsPage.vue:20
#: frontend/src/components/Settings/TelephonySettings.vue:23
#: frontend/src/components/Telephony/ExotelCallUI.vue:210
msgid "Update"
msgstr "Pembaruan"

#: frontend/src/components/Settings/EmailEdit.vue:74
msgid "Update Account"
msgstr "Perbarui Akun"

#: frontend/src/components/Modals/EditValueModal.vue:30
msgid "Update {0} Records"
msgstr "Perbarui {0} Catatan"

#: frontend/src/components/Controls/ImageUploader.vue:20
#: frontend/src/components/FilesUploader/FilesUploader.vue:83
msgid "Upload"
msgstr "Unggah"

#: frontend/src/components/Activities/Activities.vue:408
#: frontend/src/components/Activities/ActivityHeader.vue:55
#: frontend/src/components/Activities/ActivityHeader.vue:154
msgid "Upload Attachment"
msgstr "Unggah Lampiran"

#: frontend/src/components/Activities/WhatsAppBox.vue:132
msgid "Upload Document"
msgstr "Unggah Dokumen"

#: frontend/src/components/Activities/WhatsAppBox.vue:140
msgid "Upload Image"
msgstr "Unggah Gambar"

#: frontend/src/components/Activities/WhatsAppBox.vue:148
msgid "Upload Video"
msgstr "Unggah Video"

#: frontend/src/components/Settings/ProfileSettings.vue:27
#: frontend/src/pages/Contact.vue:48 frontend/src/pages/Lead.vue:89
#: frontend/src/pages/MobileContact.vue:38
#: frontend/src/pages/MobileOrganization.vue:38
#: frontend/src/pages/Organization.vue:48
msgid "Upload image"
msgstr "Unggah gambar"

#: frontend/src/components/Controls/ImageUploader.vue:17
msgid "Uploading {0}%"
msgstr ""

#. Label of the user (Link) field in DocType 'CRM Dashboard'
#. Label of the user (Link) field in DocType 'CRM Telephony Agent'
#. Label of the user (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "User"
msgstr "Pengguna"

#: frontend/src/components/Settings/Settings.vue:127
msgid "User Management"
msgstr ""

#. Label of the user_name (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "User Name"
msgstr "Nama Pengguna"

#: frontend/src/components/Settings/Users.vue:296
msgid "User {0} has been removed"
msgstr "Pengguna {0} berhasil dihapus"

#: frontend/src/components/Modals/AddExistingUserModal.vue:20
#: frontend/src/components/Settings/Settings.vue:130
#: frontend/src/components/Settings/Users.vue:7
msgid "Users"
msgstr "Pengguna"

#: frontend/src/components/Modals/AddExistingUserModal.vue:105
msgid "Users added successfully"
msgstr "Pengguna berhasil ditambahkan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:465
msgid "Users are required"
msgstr ""

#. Label of the section_break_nevd (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Validity"
msgstr "Validitas"

#: frontend/src/components/Modals/EditValueModal.vue:14
msgid "Value"
msgstr "Nilai"

#: frontend/src/components/Modals/ViewModal.vue:14
msgid "View Name"
msgstr "Nama Tampilan"

#: frontend/src/pages/Deal.vue:219
msgid "View contact"
msgstr ""

#: frontend/src/components/Layouts/AppSidebar.vue:566
msgid "Views"
msgstr "Tampilan"

#: frontend/src/components/Layouts/AppSidebar.vue:563
msgid "Web form"
msgstr "Formulir Web"

#. Label of the webhook_verify_token (Data) field in DocType 'CRM Exotel
#. Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Webhook Verify Token"
msgstr "Token Verifikasi Webhook"

#. Label of the website (Data) field in DocType 'CRM Deal'
#. Label of the website (Data) field in DocType 'CRM Lead'
#. Label of the website (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: frontend/src/components/Modals/AboutModal.vue:52
msgid "Website"
msgstr "Situs Web"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Wednesday"
msgstr "Rabu"

#. Label of the weekly_off (Check) field in DocType 'CRM Holiday'
#. Label of the weekly_off (Select) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Weekly Off"
msgstr "Libur Mingguan"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:11
msgid "Welcome Message"
msgstr "Pesan Selamat Datang"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:155
msgid "Welcome to Helpdesk"
msgstr ""

#: frontend/src/pages/Welcome.vue:4
msgid "Welcome {0}, lets add your first lead"
msgstr "Selamat Datang {0}, ayo tambahkan prospek pertama Anda"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:603
#: frontend/src/components/Settings/Settings.vue:191
#: frontend/src/pages/Deal.vue:582 frontend/src/pages/Lead.vue:429
#: frontend/src/pages/MobileDeal.vue:471 frontend/src/pages/MobileLead.vue:377
msgid "WhatsApp"
msgstr "WhatsApp"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:4
msgid "WhatsApp Templates"
msgstr "Templat WhatsApp"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:20
#: frontend/src/components/Filter.vue:43 frontend/src/components/Filter.vue:81
msgid "Where"
msgstr "Di mana"

#: frontend/src/components/ColumnSettings.vue:108
msgid "Width"
msgstr "Lebar"

#: frontend/src/components/ColumnSettings.vue:113
msgid "Width can be in number, pixel or rem (eg. 3, 30px, 10rem)"
msgstr "Lebar bisa dalam satuan angka, piksel, atau rem (misalnya 3, 30px, 10rem)"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Won"
msgstr "Berhasil"

#: crm/api/dashboard.py:310
#: frontend/src/components/Dashboard/AddChartModal.vue:79
msgid "Won deals"
msgstr "Kesepakatan yang berhasil"

#. Label of the workday (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Workday"
msgstr "Hari Kerja"

#. Label of the section_break_rmgo (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#. Label of the working_hours (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Working Hours"
msgstr "Jam Kerja"

#: frontend/src/components/Filter.vue:623
msgid "Yesterday"
msgstr "Kemarin"

#: crm/api/whatsapp.py:43 crm/api/whatsapp.py:223 crm/api/whatsapp.py:237
#: frontend/src/components/Activities/WhatsAppArea.vue:34
#: frontend/src/components/Activities/WhatsAppBox.vue:14
msgid "You"
msgstr "Anda"

#: crm/utils/__init__.py:262
msgid "You are not permitted to access this resource."
msgstr "Anda tidak diizinkan mengakses sumber daya ini."

#: crm/templates/emails/helpdesk_invitation.html:22
msgid "You can also copy-paste following link in your browser"
msgstr ""

#: frontend/src/components/Telephony/CallUI.vue:39
msgid "You can change the default calling medium from the settings"
msgstr "Anda dapat mengubah media panggilan default dari pengaturan"

#: frontend/src/components/Settings/CurrencySettings.vue:100
msgid "You can get your access key from "
msgstr "Anda bisa mendapatkan kunci akses dari "

#: frontend/src/components/Settings/InviteUserPage.vue:36
msgid "You can invite multiple users by comma separating their email addresses"
msgstr ""

#: crm/integrations/exotel/handler.py:85
msgid "You do not have Exotel Number set in your Telephony Agent"
msgstr "Anda belum mengatur Nomor Exotel di Agen Telepon Anda"

#: crm/integrations/exotel/handler.py:93
msgid "You do not have mobile number set in your Telephony Agent"
msgstr "Anda belum mengatur nomor ponsel di Agen Telepon Anda"

#: frontend/src/data/document.js:34
msgid "You do not have permission to access this document"
msgstr "Anda tidak memiliki izin untuk mengakses dokumen ini"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.py:24
msgid "You need to be in developer mode to edit a Standard Form Script"
msgstr "Anda harus berada dalam Mode pengembang untuk mengubah Skrip Formulir Standar"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:150
msgid "You will be redirected to invite user page, unsaved changes will be lost."
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:9
msgid "You will need a Meta developer account and an access token to sync leads from Facebook. Read more "
msgstr ""

#: crm/api/todo.py:100
msgid "Your assignment on task {0} has been removed by {1}"
msgstr "Tugas Anda pada {0} telah dihapus oleh {1}"

#: crm/api/todo.py:37 crm/api/todo.py:78
msgid "Your assignment on {0} {1} has been removed by {2}"
msgstr "Penugasan Anda pada {0} {1} telah dihapus oleh {2}"

#: crm/templates/emails/helpdesk_invitation.html:6
msgid "Your login id is"
msgstr "Id login Anda"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:610
msgid "Your old condition will be overwritten. Are you sure you want to save?"
msgstr ""

#: frontend/src/components/Activities/CommentArea.vue:9
msgid "added a"
msgstr "menambahkan"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "amber"
msgstr "kuning sawo"

#: crm/api/todo.py:109
msgid "assigned a new task {0} to you"
msgstr "menetapkan tugas baru {0} kepada Anda"

#: crm/api/todo.py:89
msgid "assigned a {0} {1} to you"
msgstr "menetapkan tugas {0} {1} kepada Anda"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "black"
msgstr "hitam"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "blue"
msgstr "biru"

#: frontend/src/components/Activities/Activities.vue:239
msgid "changes from"
msgstr "diubah dari"

#: frontend/src/components/Activities/CommentArea.vue:11
msgid "comment"
msgstr "komentar"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:65
#: frontend/src/components/ConditionsFilter/CFCondition.vue:73
msgid "condition"
msgstr "kondisi"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "cyan"
msgstr "biru muda"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:131
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:380
msgid "deals"
msgstr "kesepakatan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:190
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:274
msgid "desk"
msgstr "meja"

#: frontend/src/components/Calendar/Attendee.vue:295
#: frontend/src/components/Controls/EmailMultiSelect.vue:254
msgid "email already exists"
msgstr "email sudah ada"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/CurrencySettings.vue:106
msgid "exchangerate.host"
msgstr "exchangerate.host"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "frankfurter.app"
msgstr "frankfurter.app"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "gray"
msgstr "abu-abu"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "green"
msgstr "hijau"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "group_by"
msgstr "kelompokkan_berdasarkan"

#: frontend/src/components/Activities/CallArea.vue:16
msgid "has made a call"
msgstr "telah melakukan panggilan"

#: frontend/src/components/Activities/CallArea.vue:15
msgid "has reached out"
msgstr "telah menghubungi"

#: frontend/src/components/Settings/EmailAdd.vue:36
#: frontend/src/components/Settings/EmailEdit.vue:25
msgid "here"
msgstr "di sini"

#: frontend/src/utils/index.js:146
msgid "in 1 hour"
msgstr "1 jam lagi"

#: frontend/src/utils/index.js:142
msgid "in 1 minute"
msgstr "1 menit lagi"

#: frontend/src/utils/index.js:160
msgid "in 1 year"
msgstr "1 tahun lagi"

#: frontend/src/utils/index.js:111
msgid "in {0} M"
msgstr "{0} Bln lagi"

#: frontend/src/utils/index.js:107
msgid "in {0} d"
msgstr "{0} hri lagi"

#: frontend/src/utils/index.js:154
msgid "in {0} days"
msgstr "{0} hari lagi"

#: frontend/src/utils/index.js:101
msgid "in {0} h"
msgstr "{0} jm lagi"

#: frontend/src/utils/index.js:148
msgid "in {0} hours"
msgstr "{0} jam lagi"

#: frontend/src/utils/index.js:99
msgid "in {0} m"
msgstr "{0} mnt lagi"

#: frontend/src/utils/index.js:144
msgid "in {0} minutes"
msgstr "{0} menit lagi"

#: frontend/src/utils/index.js:158
msgid "in {0} months"
msgstr "{0} bulan lagi"

#: frontend/src/utils/index.js:109
msgid "in {0} w"
msgstr "{0} mng lagi"

#: frontend/src/utils/index.js:156
msgid "in {0} weeks"
msgstr "{0} minggu lagi"

#: frontend/src/utils/index.js:113
msgid "in {0} y"
msgstr "{0} thn lagi"

#: frontend/src/utils/index.js:162
msgid "in {0} years"
msgstr "{0} tahun lagi"

#: frontend/src/components/Modals/AddExistingUserModal.vue:28
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: frontend/src/utils/index.js:140 frontend/src/utils/index.js:166
msgid "just now"
msgstr "baru saja"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "kanban"
msgstr "kanban"

#: crm/api/doc.py:40 crm/api/doc.py:158 crm/api/doc.py:503
msgid "label"
msgstr "label"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:130
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:379
msgid "leads"
msgstr "lead"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "list"
msgstr "daftar"

#: crm/api/comment.py:36 frontend/src/components/Notifications.vue:59
#: frontend/src/pages/MobileNotification.vue:46
msgid "mentioned you in {0}"
msgstr "menyebutkan Anda di {0}"

#: frontend/src/components/FieldLayoutEditor.vue:368
msgid "next"
msgstr "selanjutnya"

#: frontend/src/utils/index.js:97 frontend/src/utils/index.js:117
msgid "now"
msgstr "sekarang"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:47
msgid "operator"
msgstr "operator"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "orange"
msgstr "oranye"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "pink"
msgstr "merah jambu"

#: frontend/src/components/FieldLayoutEditor.vue:368
msgid "previous"
msgstr "sebelumnya"

#: frontend/src/components/Activities/AttachmentArea.vue:97
msgid "private"
msgstr "privat"

#: frontend/src/components/Activities/AttachmentArea.vue:97
msgid "public"
msgstr "publik"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "purple"
msgstr "ungu kemerahan"

#: crm/api/whatsapp.py:44
msgid "received a whatsapp message in {0}"
msgstr "menerima pesan WhatsApp di {0}"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "red"
msgstr "merah"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "teal"
msgstr "biru kehijauan gelap"

#: frontend/src/components/Activities/Activities.vue:278
#: frontend/src/components/Activities/Activities.vue:341
msgid "to"
msgstr "menjadi"

#: frontend/src/utils/index.js:105 frontend/src/utils/index.js:152
msgid "tomorrow"
msgstr "besok"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "violet"
msgstr "ungu kebiruan"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:193
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:277
msgid "which are not compatible with this UI, you will need to recreate the conditions here if you want to manage and add new conditions from this UI."
msgstr ""

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "yellow"
msgstr "kuning"

#: frontend/src/utils/index.js:179
msgid "yesterday"
msgstr "kemarin"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:94
msgid "{0} Attendees"
msgstr ""

#: frontend/src/utils/index.js:130
msgid "{0} M"
msgstr "{0} Bln"

#: crm/api/todo.py:41
msgid "{0} assigned a {1} {2} to you"
msgstr "{0} menugaskan {1} {2} kepada Anda"

#: frontend/src/utils/index.js:126
msgid "{0} d"
msgstr "{0} h"

#: frontend/src/utils/index.js:181
msgid "{0} days ago"
msgstr "{0} hari yang lalu"

#: frontend/src/utils/index.js:121
msgid "{0} h"
msgstr "{0} j"

#: frontend/src/components/Settings/Users.vue:286
msgid "{0} has been granted {1} access"
msgstr "{0} telah diberikan akses {1}"

#: frontend/src/utils/index.js:174
msgid "{0} hours ago"
msgstr "{0} jam yang lalu"

#: frontend/src/composables/event.js:163
msgid "{0} hrs"
msgstr ""

#: frontend/src/components/Calendar/CalendarEventPanel.vue:309
#: frontend/src/components/EmailEditor.vue:30
#: frontend/src/components/EmailEditor.vue:66
#: frontend/src/components/EmailEditor.vue:80
#: frontend/src/components/Modals/AddExistingUserModal.vue:37
#: frontend/src/components/Modals/EventModal.vue:127
msgid "{0} is an invalid email address"
msgstr "{0} bukan alamat email yang valid"

#: frontend/src/components/Modals/ConvertToDealModal.vue:172
msgid "{0} is required"
msgstr "{0} wajib diisi"

#: frontend/src/utils/index.js:119
msgid "{0} m"
msgstr "{0} mnt"

#: frontend/src/composables/event.js:157
msgid "{0} mins"
msgstr ""

#: frontend/src/utils/index.js:170
msgid "{0} minutes ago"
msgstr "{0} menit yang lalu"

#: frontend/src/utils/index.js:189
msgid "{0} months ago"
msgstr "{0} bulan yang lalu"

#: frontend/src/utils/index.js:128
msgid "{0} w"
msgstr "{0} mgu"

#: frontend/src/utils/index.js:185
msgid "{0} weeks ago"
msgstr "{0} minggu yang lalu"

#: frontend/src/utils/index.js:132
msgid "{0} y"
msgstr "{0} thn"

#: frontend/src/utils/index.js:193
msgid "{0} years ago"
msgstr "{0} tahun yang lalu"

#: frontend/src/data/script.js:326
msgid "⚠️ Avoid using \"trigger\" as a field name — it conflicts with the built-in trigger() method."
msgstr "⚠️ Hindari menggunakan \"trigger\" sebagai nama bidang, karena akan menimbulkan konflik dengan metode bawaan trigger()."

#: frontend/src/data/script.js:338
msgid "⚠️ Method \"{0}\" not found in class."
msgstr "⚠️ Metode \"{0}\" tidak ditemukan pada kelas."

#: frontend/src/data/script.js:83
msgid "⚠️ No class found for doctype: {0}, it is mandatory to have a class for the parent doctype. it can be empty, but it should be present."
msgstr "⚠️ Kelas tidak ditemukan untuk doctype: {0}, wajib ada sebuah kelas untuk doctype induk. Kelas tersebut bisa kosong, namun harus tetap ada."

#: frontend/src/data/script.js:180
msgid "⚠️ No data found for parent field: {0}"
msgstr "⚠️ Data tidak ditemukan untuk bidang induk: {0}"

#: frontend/src/data/script.js:188
msgid "⚠️ No row found for idx: {0} in parent field: {1}"
msgstr "Baris tidak ditemukan untuk idx: {0} di dalam bidang induk: {1}"

