msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-11-09 09:35+0000\n"
"PO-Revision-Date: 2025-11-10 10:46\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Chinese Simplified\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: zh-CN\n"
"X-Crowdin-File: /[frappe.crm] develop/crm/locale/main.pot\n"
"X-Crowdin-File-ID: 97\n"
"Language: zh_CN\n"

#: frontend/src/components/ViewControls.vue:1172
msgid " (New)"
msgstr "（新建）"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:58
msgid "(No title)"
msgstr "(无标题)"

#: frontend/src/components/Modals/TaskModal.vue:87
#: frontend/src/components/Telephony/TaskPanel.vue:70
msgid "01/04/2024 11:30 PM"
msgstr "2024年4月1日 下午11:30"

#: frontend/src/utils/index.js:172
msgid "1 hour ago"
msgstr "1小时前"

#: frontend/src/composables/event.js:163
msgid "1 hr"
msgstr "1小时"

#: frontend/src/utils/index.js:168
msgid "1 minute ago"
msgstr "1分钟前"

#: frontend/src/utils/index.js:187
msgid "1 month ago"
msgstr "一个月前"

#: frontend/src/utils/index.js:183
msgid "1 week ago"
msgstr "一周前"

#: frontend/src/utils/index.js:191
msgid "1 year ago"
msgstr "一年前"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1-10"
msgstr "1-10"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "1000+"
msgstr "1000+"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "11-50"
msgstr "11-50"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "201-500"
msgstr "201-500"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "501-1000"
msgstr "501-1000"

#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Deal'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM Lead'
#. Option for the 'No. of Employees' (Select) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "51-200"
msgstr "51-200"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>META</b>"
msgstr "<b>元数据</b>"

#. Paragraph text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<b>SHORTCUTS</b>"
msgstr "<b>快捷方式</b>"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:95
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:95
msgid "<p>Dear {{ lead_name }},</p>\\n\\n<p>This is a reminder for the payment of {{ grand_total }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappé</p>"
msgstr "msgstr \"<p>尊敬的{{ lead_name }}：</p>\\n\\n<p>特此提醒您尚有{{ grand_total }}款项待支付。</p>\\n\\n<p>此致</p>\\n<p>Frappé团队</p>\""

#. Header text in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "<span class=\"h5\"><b>PORTAL</b></span>"
msgstr "<span class=\"h5\"><b>门户</b></span>"

#: frontend/src/components/CommunicationArea.vue:79
msgid "@John, can you please check this?"
msgstr "@John，请确认此项？"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:103
msgid "A Lead requires either a person's name or an organization's name"
msgstr "线索需填写个人姓名或组织名称"

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.py:47
msgid "A lead sync source is already enabled for this Facebook Lead Form!"
msgstr ""

#: crm/templates/emails/helpdesk_invitation.html:5
msgid "A new account has been created for you at {0}"
msgstr "已为您创建新账户{0}"

#. Label of the api_key (Data) field in DocType 'CRM Exotel Settings'
#. Label of the api_key (Data) field in DocType 'CRM Twilio Settings'
#. Label of the api_key (Data) field in DocType 'ERPNext CRM Settings'
#. Label of the api_key (Data) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "API Key"
msgstr "API密钥"

#: frontend/src/components/Settings/emailConfig.js:179
msgid "API Key is required"
msgstr "必须填写API密钥"

#. Label of the api_secret (Password) field in DocType 'CRM Twilio Settings'
#. Label of the api_secret (Password) field in DocType 'ERPNext CRM Settings'
#. Label of the api_secret (Password) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "API Secret"
msgstr "API密钥"

#. Label of the api_token (Password) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "API Token"
msgstr "API令牌"

#: frontend/src/components/Telephony/TwilioCallUI.vue:88
msgid "Accept"
msgstr "接受"

#: crm/fcrm/doctype/crm_invitation/crm_invitation.js:7
msgid "Accept Invitation"
msgstr "接受邀请"

#: frontend/src/components/Telephony/TwilioCallUI.vue:155
msgid "Accept call"
msgstr "接听来电"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted"
msgstr "已接受"

#. Label of the accepted_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Accepted At"
msgstr "接受时间"

#. Label of the access_key (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Access Key"
msgstr "访问密钥"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:156
msgid "Access Key is required for Service Provider: {0}"
msgstr "服务商{0}必须提供访问密钥"

#. Label of the access_token (Small Text) field in DocType 'Facebook Page'
#. Label of the access_token (Password) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:87
#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:28
msgid "Access Token"
msgstr "访问令牌"

#: frontend/src/components/Settings/CurrencySettings.vue:90
msgid "Access key"
msgstr "访问秘钥"

#: frontend/src/components/Settings/CurrencySettings.vue:94
msgid "Access key for Exchangerate Host. Required for fetching exchange rates."
msgstr "Exchangerate Host服务的访问密钥，用于获取汇率数据"

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:133
msgid "Access token is required"
msgstr ""

#. Label of the account_id (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Account ID"
msgstr "账户ID"

#: frontend/src/components/Settings/emailConfig.js:13
msgid "Account Name"
msgstr "科目名称"

#. Label of the account_sid (Data) field in DocType 'CRM Exotel Settings'
#. Label of the account_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Account SID"
msgstr "账户SID"

#: frontend/src/components/Settings/emailConfig.js:165
msgid "Account name is required"
msgstr "账户名称为必填项。"

#: frontend/src/components/CustomActions.vue:69
#: frontend/src/components/ViewControls.vue:683
#: frontend/src/components/ViewControls.vue:1064
msgid "Actions"
msgstr "操作"

#: frontend/src/pages/Deal.vue:537 frontend/src/pages/Lead.vue:384
#: frontend/src/pages/MobileDeal.vue:430 frontend/src/pages/MobileLead.vue:337
msgid "Activity"
msgstr "活动"

#: frontend/src/components/Dashboard/AddChartModal.vue:41
#: frontend/src/components/Modals/AddExistingUserModal.vue:55
msgid "Add"
msgstr "添加"

#: frontend/src/components/Settings/EmailAccountList.vue:19
msgid "Add Account"
msgstr "添加帐户"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:9
msgid "Add Assignee"
msgstr "添加负责人"

#: frontend/src/components/ColumnSettings.vue:68
#: frontend/src/components/Kanban/KanbanView.vue:157
msgid "Add Column"
msgstr "添加列"

#: frontend/src/components/Modals/AddExistingUserModal.vue:4
#: frontend/src/components/Settings/Users.vue:21
msgid "Add Existing User"
msgstr "添加现有用户"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:57
#: frontend/src/components/FieldLayoutEditor.vue:172
#: frontend/src/components/Kanban/KanbanSettings.vue:80
#: frontend/src/components/SidePanelLayoutEditor.vue:97
msgid "Add Field"
msgstr "添加字段"

#: frontend/src/components/Filter.vue:136
msgid "Add Filter"
msgstr "添加筛选器"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:273
msgid "Add Lead or Deal"
msgstr "添加线索或商机"

#: frontend/src/components/Controls/Grid.vue:334
msgid "Add Row"
msgstr "添加行"

#: frontend/src/components/FieldLayoutEditor.vue:197
#: frontend/src/components/SidePanelLayoutEditor.vue:127
msgid "Add Section"
msgstr "添加区块"

#: frontend/src/components/SortBy.vue:142
msgid "Add Sort"
msgstr "添加排序"

#: frontend/src/components/FieldLayoutEditor.vue:62
msgid "Add Tab"
msgstr "添加标签页"

#. Label of the add_weekly_holidays_section (Section Break) field in DocType
#. 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add Weekly Holidays"
msgstr "添加每周假期"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:20
msgid "Add a condition"
msgstr "添加条件"

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:24
msgid "Add a name for your source"
msgstr ""

#: frontend/src/components/Telephony/ExotelCallUI.vue:183
#: frontend/src/components/Telephony/TwilioCallUI.vue:57
msgid "Add a note"
msgstr "添加备注"

#: frontend/src/components/Telephony/ExotelCallUI.vue:191
msgid "Add a task"
msgstr "添加任务"

#: frontend/src/components/Dashboard/AddChartModal.vue:4
msgid "Add chart"
msgstr "添加图表"

#: frontend/src/components/FieldLayoutEditor.vue:420
msgid "Add column"
msgstr "添加列"

#: frontend/src/components/ConditionsFilter/CFConditions.vue:23
#: frontend/src/components/ConditionsFilter/CFConditions.vue:82
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:28
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:64
msgid "Add condition"
msgstr "添加条件"

#: frontend/src/components/ConditionsFilter/CFConditions.vue:91
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesSection.vue:70
msgid "Add condition group"
msgstr "添加条件组"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:325
msgid "Add description"
msgstr "添加描述"

#: frontend/src/components/Modals/EventModal.vue:142
msgid "Add description."
msgstr "添加描述."

#: frontend/src/components/Telephony/TaskPanel.vue:17
msgid "Add description..."
msgstr "添加描述..."

#: frontend/src/components/Modals/AddExistingUserModal.vue:12
msgid "Add existing system users to this CRM. Assign them a role to grant access with their current credentials."
msgstr "将现有系统用户添加至本CRM系统。通过分配角色，用户可使用当前凭证获得相应访问权限。"

#: frontend/src/components/ViewControls.vue:107
msgid "Add filter"
msgstr "添加筛选器"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Add note"
msgstr "添加备注"

#: frontend/src/pages/Welcome.vue:24
msgid "Add sample data"
msgstr "添加示例数据"

#. Description of the 'Icon' (Code) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Add svg code or use feather icons e.g 'settings'"
msgstr "添加SVG代码或使用Feather图标（如'settings'）"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Add task"
msgstr "添加任务"

#. Label of the add_to_holidays (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Add to Holidays"
msgstr "添加至假期"

#: frontend/src/components/Layouts/AppSidebar.vue:439
msgid "Add your first comment"
msgstr "添加首条评论"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:11
msgid "Add, edit, and manage email templates for various CRM communications"
msgstr "为各类CRM沟通场景添加、编辑及管理邮件模板"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:12
msgid "Add, edit, and manage sources for automatic lead syncing to your CRM"
msgstr ""

#. Label of the address (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Address"
msgstr "地址"

#: frontend/src/components/Modals/AddExistingUserModal.vue:94
#: frontend/src/components/Settings/InviteUserPage.vue:158
#: frontend/src/components/Settings/InviteUserPage.vue:165
#: frontend/src/components/Settings/Users.vue:86
#: frontend/src/components/Settings/Users.vue:126
#: frontend/src/components/Settings/Users.vue:192
#: frontend/src/components/Settings/Users.vue:242
#: frontend/src/components/Settings/Users.vue:245
msgid "Admin"
msgstr "管理员"

#: crm/integrations/twilio/twilio_handler.py:144
msgid "Agent is unavailable to take the call, please call after some time."
msgstr "客服正忙，请稍后再拨。"

#. Name of a role
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:76
#: frontend/src/components/Settings/Users.vue:85
msgid "All"
msgstr "全部"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:191
#: frontend/src/components/Calendar/CalendarEventPanel.vue:641
#: frontend/src/components/Modals/EventModal.vue:71
#: frontend/src/composables/event.js:122
msgid "All day"
msgstr "全天"

#. Label of the amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
#: frontend/src/pages/Contact.vue:507 frontend/src/pages/MobileContact.vue:507
#: frontend/src/pages/MobileOrganization.vue:451
#: frontend/src/pages/Organization.vue:481
msgid "Amount"
msgstr "金额"

#. Description of the 'Net Amount' (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Amount after discount"
msgstr "折后金额"

#: frontend/src/data/script.js:50 frontend/src/data/script.js:51
msgid "An error occurred"
msgstr "发生错误"

#: frontend/src/data/document.js:66
msgid "An error occurred while updating the document"
msgstr "文档更新过程中发生错误"

#. Description of the 'Favicon' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An icon file with .ico extension. Should be 16 x 16 px. Generated using a favicon generator. [favicon-generator.org]"
msgstr "ICO格式图标文件，尺寸16x16像素，需通过favicon生成器创建。[favicon-generator.org]"

#. Description of the 'Logo' (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "An image with 1:1 & 2:1 ratio is preferred"
msgstr "建议使用1:1和2:1比例的图像"

#: frontend/src/components/Filter.vue:43 frontend/src/components/Filter.vue:81
msgid "And"
msgstr "且"

#. Label of the annual_revenue (Currency) field in DocType 'CRM Deal'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Lead'
#. Label of the annual_revenue (Currency) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Annual Revenue"
msgstr "年营业额"

#: frontend/src/components/Modals/DealModal.vue:200
#: frontend/src/components/Modals/LeadModal.vue:141
msgid "Annual Revenue should be a number"
msgstr "年营业额应为数值"

#: frontend/src/components/Settings/BrandSettings.vue:55
msgid "Appears in the left sidebar. Recommended size is 32x32 px in PNG or SVG"
msgstr "显示于左侧栏，推荐PNG或SVG格式，尺寸32x32像素"

#: frontend/src/components/Settings/BrandSettings.vue:90
msgid "Appears next to the title in your browser tab. Recommended size is 32x32 px in PNG or ICO"
msgstr "显示于浏览器标签页标题旁，推荐PNG或ICO格式，尺寸32x32像素"

#: frontend/src/components/Kanban/KanbanSettings.vue:101
#: frontend/src/components/Kanban/KanbanView.vue:46
msgid "Apply"
msgstr "应用"

#. Label of the apply_on (Link) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Apply On"
msgstr "应用于"

#. Label of the view (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Apply To"
msgstr "应用至"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:125
msgid "Apply on"
msgstr "应用于"

#: frontend/src/components/Apps.vue:19
msgid "Apps"
msgstr "应用"

#: frontend/src/components/Activities/AttachmentArea.vue:128
msgid "Are you sure you want to delete this attachment?"
msgstr "确认删除此附件？"

#: frontend/src/pages/MobileContact.vue:260
msgid "Are you sure you want to delete this contact?"
msgstr "确认删除此联系人？"

#: frontend/src/components/Modals/EventModal.vue:408
#: frontend/src/pages/Calendar.vue:300
msgid "Are you sure you want to delete this event?"
msgstr "是否确认删除本事件？"

#: frontend/src/pages/MobileOrganization.vue:261
msgid "Are you sure you want to delete this organization?"
msgstr "确认删除此组织？"

#: frontend/src/components/Activities/TaskArea.vue:60
msgid "Are you sure you want to delete this task?"
msgstr "确认删除此任务？"

#: frontend/src/components/DeleteLinkedDocModal.vue:231
msgid "Are you sure you want to delete {0} linked item(s)?"
msgstr "是否确认删除{0}个关联项？"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:615
msgid "Are you sure you want to discard unsaved changes to this event?"
msgstr "是否确认放弃对本事件的未保存修改？"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:576
msgid "Are you sure you want to go back? Unsaved changes will be lost."
msgstr "是否确认返回？未保存的修改将会丢失。"

#: frontend/src/composables/frappecloud.js:24
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "确定登录Frappe云控制面板？"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:9
msgid "Are you sure you want to reset 'Create Quotation from CRM Deal' Form Script?"
msgstr "确认重置'从CRM商机生成报价单'表单脚本？"

#: frontend/src/components/Settings/CurrencySettings.vue:165
msgid "Are you sure you want to set the currency as {0}? This cannot be changed later."
msgstr "是否确认将货币设为{0}？此设置后续不可更改。"

#: frontend/src/components/DeleteLinkedDocModal.vue:244
msgid "Are you sure you want to unlink {0} linked item(s)?"
msgstr "是否确认取消{0}个关联项的链接？"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:193
msgid "Ask your manager to set up the Exchange Rate Provider, as default provider does not support currency conversion for {0} to {1}."
msgstr "默认汇率服务商不支持{0}至{1}的货币转换，请联络管理员配置汇率服务商。"

#: frontend/src/components/ListBulkActions.vue:186
#: frontend/src/components/Modals/AssignmentModal.vue:4
msgid "Assign To"
msgstr "分配给"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:435
msgid "Assign condition is required"
msgstr "必须设置分配条件"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:438
msgid "Assign conditions are invalid"
msgstr "分配条件无效。"

#: frontend/src/components/AssignTo.vue:11
#: frontend/src/components/AssignToBody.vue:5
msgid "Assign to"
msgstr "分配至"

#: frontend/src/components/AssignToBody.vue:63
msgid "Assign to me"
msgstr "自己认领"

#. Label of the assigned_to (Link) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Assigned To"
msgstr "负责人"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:5
msgid "Assignee Rules"
msgstr "负责人分配规则"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:81
msgid "Assignees"
msgstr "负责人列表"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Assignment"
msgstr "分配"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Assignment Rule"
msgstr "分配规则"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:302
msgid "Assignment Schedule"
msgstr "分配计划"

#: frontend/src/components/ListBulkActions.vue:154
msgid "Assignment cleared successfully"
msgstr "分配已成功清除"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:145
msgid "Assignment condition"
msgstr "分配条件"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:471
msgid "Assignment days are required"
msgstr "必须设置分配天数。"

#: frontend/src/components/Layouts/AppSidebar.vue:582
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:19
msgid "Assignment rule"
msgstr "分配规则"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:667
msgid "Assignment rule created"
msgstr "分配规则创建成功。"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:111
msgid "Assignment rule deleted"
msgstr "分配规则删除成功。"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:156
msgid "Assignment rule duplicated"
msgstr "分配规则复制成功。"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:749
msgid "Assignment rule updated"
msgstr "分配规则更新成功。"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:188
msgid "Assignment rule {0} updated"
msgstr "分配规则{0}更新成功。"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:7
#: frontend/src/components/Settings/Settings.vue:164
msgid "Assignment rules"
msgstr "分配规则"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:11
msgid "Assignment rules automatically assign lead/deal to the right sales user based on predefined conditions"
msgstr "分配规则根据预设条件自动将线索/商机分配给对应销售人员。"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:173
msgid "At least one field is required"
msgstr "至少需填写一个字段"

#: frontend/src/components/FilesUploader/FilesUploader.vue:5
#: frontend/src/components/FilesUploader/FilesUploader.vue:73
msgid "Attach"
msgstr "附加"

#: frontend/src/components/CommentBox.vue:65
#: frontend/src/components/EmailEditor.vue:146 frontend/src/pages/Deal.vue:105
#: frontend/src/pages/Lead.vue:151
msgid "Attach a file"
msgstr "附加文件"

#: frontend/src/pages/Deal.vue:577 frontend/src/pages/Lead.vue:424
#: frontend/src/pages/MobileDeal.vue:466 frontend/src/pages/MobileLead.vue:372
msgid "Attachments"
msgstr "附件"

#: frontend/src/components/Modals/EventModal.vue:120
msgid "Attendees"
msgstr "参会人员"

#. Label of the auth_token (Password) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Auth Token"
msgstr "认证令牌"

#. Label of the auto_update_expected_deal_value (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Auto Update Expected Deal Value"
msgstr "自动更新预期商机价值"

#: frontend/src/components/Settings/ForecastingSettings.vue:42
msgid "Auto update expected deal value"
msgstr "自动更新预期商机价值"

#: frontend/src/components/Settings/ForecastingSettings.vue:88
msgid "Auto update of expected deal value disabled"
msgstr "预期商机价值自动更新功能已禁用。"

#: frontend/src/components/Settings/ForecastingSettings.vue:87
msgid "Auto update of expected deal value enabled"
msgstr "预期商机价值自动更新功能已启用。"

#. Description of the 'Auto Update Expected Deal Value' (Check) field in
#. DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/ForecastingSettings.vue:46
msgid "Automatically update \"Expected Deal Value\" based on the total value of associated products in a deal"
msgstr "根据商机中关联产品的总价值自动更新“预期商机价值”"

#: frontend/src/components/Settings/Settings.vue:161
msgid "Automation & Rules"
msgstr "自动化与规则"

#: crm/api/dashboard.py:250
msgid "Average deal value of non won/lost deals"
msgstr "非赢单/输单商机的平均价值"

#: crm/api/dashboard.py:429
msgid "Average deal value of ongoing & won deals"
msgstr "进行中及赢单商机的平均价值"

#: crm/api/dashboard.py:370
msgid "Average deal value of won deals"
msgstr "赢单商机的平均价值"

#: crm/api/dashboard.py:534
msgid "Average time taken from deal creation to deal closure"
msgstr "商机创建至关闭的平均耗时"

#: crm/api/dashboard.py:481
msgid "Average time taken from lead creation to deal closure"
msgstr "线索创建至商机关闭的平均耗时"

#: frontend/src/components/Dashboard/AddChartModal.vue:81
msgid "Avg deal value"
msgstr "平均商机价值"

#: frontend/src/components/Dashboard/AddChartModal.vue:78
msgid "Avg ongoing deal value"
msgstr "平均进行中商机价值"

#: frontend/src/components/Dashboard/AddChartModal.vue:87
msgid "Avg time to close a deal"
msgstr "平均成交时间"

#: frontend/src/components/Dashboard/AddChartModal.vue:83
msgid "Avg time to close a lead"
msgstr "平均成交时间"

#: frontend/src/components/Dashboard/AddChartModal.vue:80
msgid "Avg won deal value"
msgstr "平均赢单商机价值"

#: crm/api/dashboard.py:428
msgid "Avg. deal value"
msgstr "平均商机价值"

#: crm/api/dashboard.py:249
msgid "Avg. ongoing deal value"
msgstr "平均进行中商机价值"

#: crm/api/dashboard.py:533
msgid "Avg. time to close a deal"
msgstr "平均商机关闭耗时"

#: crm/api/dashboard.py:480
msgid "Avg. time to close a lead"
msgstr "平均线索关闭耗时"

#: crm/api/dashboard.py:369
msgid "Avg. won deal value"
msgstr "平均赢单商机价值"

#: frontend/src/components/Dashboard/AddChartModal.vue:26
#: frontend/src/components/Dashboard/AddChartModal.vue:70
msgid "Axis chart"
msgstr "轴线图"

#: frontend/src/components/Activities/EmailArea.vue:62
#: frontend/src/components/EmailEditor.vue:45
#: frontend/src/components/EmailEditor.vue:71
msgid "BCC"
msgstr "密送"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
#: frontend/src/components/Settings/EmailAdd.vue:79
#: frontend/src/components/Settings/EmailEdit.vue:67
msgid "Back"
msgstr "返回"

#: frontend/src/components/FilesUploader/FilesUploader.vue:31
msgid "Back to file upload"
msgstr "返回文件上传"

#. Label of the background_sync_frequency (Select) field in DocType 'Lead Sync
#. Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:80
msgid "Background Sync Frequency"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Backlog"
msgstr "待办事项"

#: frontend/src/components/Filter.vue:350
msgid "Between"
msgstr "介于"

#: frontend/src/components/Settings/Settings.vue:119
msgid "Brand Settings"
msgstr "品牌设置"

#: frontend/src/components/Settings/BrandSettings.vue:52
msgid "Brand logo"
msgstr "品牌标识"

#: frontend/src/components/Settings/BrandSettings.vue:32
msgid "Brand name"
msgstr "品牌名称"

#: frontend/src/components/Settings/BrandSettings.vue:7
msgid "Brand settings"
msgstr "品牌设置"

#. Label of the branding_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Branding"
msgstr "品牌标识"

#: frontend/src/components/Modals/EditValueModal.vue:2
msgid "Bulk Edit"
msgstr "批量编辑"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Busy"
msgstr "忙碌"

#: frontend/src/components/Activities/EmailArea.vue:57
#: frontend/src/components/EmailEditor.vue:35
#: frontend/src/components/EmailEditor.vue:57
msgid "CC"
msgstr "抄送"

#. Name of a DocType
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "CRM Call Log"
msgstr "CRM通话记录"

#. Name of a DocType
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
msgid "CRM Communication Status"
msgstr "CRM沟通状态"

#. Name of a DocType
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
msgid "CRM Contacts"
msgstr "CRM联系人"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/pages/Dashboard.vue:304
msgid "CRM Dashboard"
msgstr "客户关系管理仪表板"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "CRM Deal"
msgstr "CRM商机"

#. Name of a DocType
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "CRM Deal Status"
msgstr "CRM商机状态"

#. Name of a DocType
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "CRM Dropdown Item"
msgstr "CRM下拉项"

#. Name of a DocType
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "CRM Exotel Settings"
msgstr "CRM Exotel设置"

#. Name of a DocType
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "CRM Fields Layout"
msgstr "CRM字段布局"

#. Name of a DocType
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "CRM Form Script"
msgstr "CRM表单脚本"

#. Name of a DocType
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "CRM Global Settings"
msgstr "CRM全局设置"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
msgid "CRM Holiday"
msgstr "CRM假期"

#. Name of a DocType
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "CRM Holiday List"
msgstr "CRM假期列表"

#. Name of a DocType
#: crm/fcrm/doctype/crm_industry/crm_industry.json
msgid "CRM Industry"
msgstr "CRM行业"

#. Name of a DocType
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "CRM Invitation"
msgstr "CRM邀请"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "CRM Lead"
msgstr "CRM线索"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
msgid "CRM Lead Source"
msgstr "CRM线索来源"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "CRM Lead Status"
msgstr "CRM线索状态"

#. Name of a DocType
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "CRM Lost Reason"
msgstr "CRM 丢失原因"

#. Name of a DocType
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "CRM Notification"
msgstr "CRM通知"

#. Name of a DocType
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "CRM Organization"
msgstr "CRM组织"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "CRM Portal Page"
msgstr "CRM门户页面"

#. Name of a DocType
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "CRM Product"
msgstr "CRM 产品"

#. Name of a DocType
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "CRM Products"
msgstr "CRM 产品"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "CRM Service Day"
msgstr "CRM服务日"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "CRM Service Level Agreement"
msgstr "CRM服务级别协议"

#. Name of a DocType
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "CRM Service Level Priority"
msgstr "CRM服务级别优先级"

#. Name of a DocType
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "CRM Status Change Log"
msgstr "CRM状态变更日志"

#. Name of a DocType
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "CRM Task"
msgstr "CRM任务"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "CRM Telephony Agent"
msgstr "CRM电话客服"

#. Name of a DocType
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "CRM Telephony Phone"
msgstr "CRM电话设备"

#. Name of a DocType
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "CRM Territory"
msgstr "CRM区域"

#. Name of a DocType
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "CRM Twilio Settings"
msgstr "CRM Twilio设置"

#. Name of a DocType
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "CRM View Settings"
msgstr "CRM视图设置"

#: frontend/src/components/Settings/CurrencySettings.vue:35
msgid "CRM currency for all monetary values. Once set, cannot be edited."
msgstr "CRM系统统一货币单位，设置后不可修改。"

#: frontend/src/components/ViewControls.vue:272
msgid "CSV"
msgstr "CSV"

#: frontend/src/components/Modals/CallLogDetailModal.vue:8
msgid "Call Details"
msgstr "通话详情"

#. Label of the receiver (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call Received By"
msgstr "接听人"

#. Description of the 'Duration' (Duration) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Call duration in seconds"
msgstr "通话时长（秒）"

#: frontend/src/components/Layouts/AppSidebar.vue:556
msgid "Call log"
msgstr "通话记录"

#: frontend/src/components/Telephony/CallUI.vue:10
msgid "Call using {0}"
msgstr "使用{0}呼叫"

#: frontend/src/components/Modals/EventModal.vue:63
#: frontend/src/components/Modals/NoteModal.vue:28
#: frontend/src/components/Modals/TaskModal.vue:30
msgid "Call with John Doe"
msgstr "与John Doe通话"

#. Label of the caller (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Caller"
msgstr "主叫方"

#: frontend/src/components/Telephony/CallUI.vue:27
msgid "Calling Medium"
msgstr "呼叫媒介"

#: frontend/src/components/Telephony/TwilioCallUI.vue:40
#: frontend/src/components/Telephony/TwilioCallUI.vue:140
msgid "Calling..."
msgstr "呼叫中..."

#: frontend/src/pages/Deal.vue:562 frontend/src/pages/Lead.vue:409
#: frontend/src/pages/MobileDeal.vue:450 frontend/src/pages/MobileLead.vue:357
msgid "Calls"
msgstr "通话记录"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:51
msgid "Camera"
msgstr "摄像头"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:81
#: frontend/src/components/Calendar/CalendarEventPanel.vue:620
#: frontend/src/components/ColumnSettings.vue:123
#: frontend/src/components/Dashboard/AddChartModal.vue:40
#: frontend/src/components/DeleteLinkedDocModal.vue:115
#: frontend/src/components/Modals/AssignmentModal.vue:69
#: frontend/src/components/Modals/EventModal.vue:151
#: frontend/src/components/Modals/LostReasonModal.vue:43
#: frontend/src/components/Telephony/TwilioCallUI.vue:73
#: frontend/src/components/ViewControls.vue:57
#: frontend/src/components/ViewControls.vue:154
#: frontend/src/pages/Dashboard.vue:32
msgid "Cancel"
msgstr "取消"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Canceled"
msgstr "已取消"

#: frontend/src/components/Settings/Users.vue:124
msgid "Cannot change role of user with Admin access"
msgstr "不可修改具备管理员权限用户的角色。"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:34
msgid "Cannot delete standard items {0}"
msgstr "无法删除标准项{0}"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:171
msgid "Cannot enable rule without adding users in it"
msgstr "未添加用户时无法启用规则。"

#: frontend/src/components/FilesUploader/FilesUploader.vue:91
msgid "Capture"
msgstr "捕获"

#: frontend/src/components/Layouts/AppSidebar.vue:561
msgid "Capturing leads"
msgstr "线索获取"

#. Label of the category (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Category"
msgstr "类别"

#: frontend/src/components/Controls/ImageUploader.vue:19
#: frontend/src/components/Layouts/AppSidebar.vue:490
msgid "Change"
msgstr "更改"

#: frontend/src/components/Modals/ChangePasswordModal.vue:2
#: frontend/src/components/Settings/ProfileSettings.vue:65
msgid "Change Password"
msgstr "修改密码"

#: frontend/src/components/Layouts/AppSidebar.vue:481
#: frontend/src/components/Layouts/AppSidebar.vue:489
msgid "Change deal status"
msgstr "变更商机状态"

#: frontend/src/components/Settings/ProfileSettings.vue:26
#: frontend/src/pages/Contact.vue:47 frontend/src/pages/Lead.vue:88
#: frontend/src/pages/MobileContact.vue:37
#: frontend/src/pages/MobileOrganization.vue:37
#: frontend/src/pages/Organization.vue:47
msgid "Change image"
msgstr "更换图片"

#: frontend/src/components/Activities/TaskArea.vue:45
msgid "Change status"
msgstr "变更状态"

#: frontend/src/pages/Dashboard.vue:22
msgid "Chart"
msgstr "图表"

#: frontend/src/components/Dashboard/AddChartModal.vue:12
msgid "Chart Type"
msgstr "图表类型"

#: frontend/src/components/Modals/ConvertToDealModal.vue:29
#: frontend/src/components/Modals/ConvertToDealModal.vue:55
#: frontend/src/pages/MobileLead.vue:122 frontend/src/pages/MobileLead.vue:149
msgid "Choose Existing"
msgstr "选择现有项"

#: frontend/src/components/Modals/DealModal.vue:44
msgid "Choose Existing Contact"
msgstr "选择现有联系人"

#: frontend/src/components/Modals/DealModal.vue:37
msgid "Choose Existing Organization"
msgstr "选择现有组织"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:9
msgid "Choose how {0} are assigned among salespeople."
msgstr "请选择{0}在销售人员间的分配方式。"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:306
msgid "Choose the days of the week when this rule should be active."
msgstr "请选择本周内规则生效的具体日期。"

#: frontend/src/components/Settings/EmailAdd.vue:9
msgid "Choose the email service provider you want to configure."
msgstr "请选择需要配置的邮件服务提供商。"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:229
msgid "Choose which {0} are affected by this un-assignment rule."
msgstr "请选择受本取消分配规则影响的{0}范围。"

#: frontend/src/components/Controls/Link.vue:59
msgid "Clear"
msgstr "清除"

#: frontend/src/components/ListBulkActions.vue:136
#: frontend/src/components/ListBulkActions.vue:144
#: frontend/src/components/ListBulkActions.vue:190
msgid "Clear Assignment"
msgstr "清除分配"

#: frontend/src/components/SortBy.vue:153
msgid "Clear Sort"
msgstr "清除排序"

#. Label of the clear_table (Button) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Clear Table"
msgstr "清空表格"

#: frontend/src/components/Filter.vue:21 frontend/src/components/Filter.vue:146
msgid "Clear all Filter"
msgstr "清除所有筛选"

#: crm/templates/emails/helpdesk_invitation.html:7
msgid "Click on the link below to complete your registration and set a new password"
msgstr "点击下面的链接完成注册，并设置新密码"

#: frontend/src/components/Notifications.vue:26
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:54
msgid "Close"
msgstr "关闭"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:38
msgid "Close panel"
msgstr "关闭面板"

#. Label of the closed_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Closed Date"
msgstr "截止日期"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Collapse"
msgstr "折叠"

#: frontend/src/components/FieldLayoutEditor.vue:344
msgid "Collapsible"
msgstr "可折叠"

#. Label of the color (Select) field in DocType 'CRM Deal Status'
#. Label of the color (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Color"
msgstr "颜色"

#: frontend/src/components/FieldLayoutEditor.vue:417
msgid "Column"
msgstr "列"

#. Label of the column_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:12
msgid "Column Field"
msgstr "列字段"

#. Label of the columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ColumnSettings.vue:4
msgid "Columns"
msgstr "列"

#. Label of the comment (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/CommentBox.vue:78
#: frontend/src/components/CommunicationArea.vue:16
#: frontend/src/components/Layouts/AppSidebar.vue:579
msgid "Comment"
msgstr "评论"

#: frontend/src/pages/Deal.vue:547 frontend/src/pages/Lead.vue:394
#: frontend/src/pages/MobileDeal.vue:440 frontend/src/pages/MobileLead.vue:347
msgid "Comments"
msgstr "评论"

#: crm/api/dashboard.py:920
msgid "Common reasons for losing deals"
msgstr "交易失败的常见原因"

#. Label of the communication_status (Link) field in DocType 'CRM Deal'
#. Label of the communication_status (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Communication Status"
msgstr "沟通状态"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Communication Statuses"
msgstr "沟通状态列表"

#. Label of the erpnext_company (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Company in ERPNext Site"
msgstr "ERPNext站点中的公司"

#: crm/templates/emails/helpdesk_invitation.html:10
msgid "Complete Registration"
msgstr "完成注册"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Completed"
msgstr "已完成"

#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Computer"
msgstr "计算机"

#. Label of the condition (Code) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Condition"
msgstr "条件"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:188
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:272
msgid "Conditions for this rule were created from"
msgstr "本规则的条件创建自"

#: frontend/src/components/Settings/HomeActions.vue:10
msgid "Configure actions that appear on the home dropdown"
msgstr "配置首页下拉菜单中显示的操作项"

#: frontend/src/components/Settings/ForecastingSettings.vue:9
msgid "Configure forecasting feature to help predict sales performance and growth"
msgstr "配置预测功能以辅助预估销售业绩与增长趋势"

#: frontend/src/components/Settings/TelephonySettings.vue:17
msgid "Configure telephony settings for your CRM"
msgstr "为 CRM 配置电话设置"

#: frontend/src/components/Settings/CurrencySettings.vue:11
msgid "Configure the currency and exchange rate provider for your CRM"
msgstr "为CRM系统配置货币与汇率服务商"

#: frontend/src/components/Settings/CurrencySettings.vue:63
msgid "Configure the exchange rate provider for your CRM"
msgstr "为您的 CRM 配置汇率提供商"

#: frontend/src/components/Settings/BrandSettings.vue:10
msgid "Configure your brand name, logo, and favicon"
msgstr "配置品牌名称、标识及网站图标"

#: frontend/src/composables/frappecloud.js:29
msgid "Confirm"
msgstr "确认"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:139
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:239
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:209
msgid "Confirm Delete"
msgstr "确认删除"

#: frontend/src/components/Modals/ChangePasswordModal.vue:18
msgid "Confirm Password"
msgstr "确认密码"

#: frontend/src/components/Settings/Users.vue:228
msgid "Confirm Remove"
msgstr "确认删除"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:609
msgid "Confirm overwrite"
msgstr "确认覆盖"

#: frontend/src/pages/Welcome.vue:35
msgid "Connect your email"
msgstr "连接您的电子邮件"

#. Label of the contact (Link) field in DocType 'CRM Contacts'
#. Label of the contact (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Layouts/AppSidebar.vue:552
#: frontend/src/components/Modals/ConvertToDealModal.vue:51
#: frontend/src/pages/MobileLead.vue:145
msgid "Contact"
msgstr "联系人"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:259
msgid "Contact Already Exists"
msgstr "联系人已存在"

#: frontend/src/components/Modals/AboutModal.vue:77
msgid "Contact Support"
msgstr "联系技术支持"

#: frontend/src/components/Modals/EditValueModal.vue:20
msgid "Contact Us"
msgstr "联系我们"

#: frontend/src/pages/Deal.vue:657 frontend/src/pages/MobileDeal.vue:544
msgid "Contact added"
msgstr "联系人已添加"

#: frontend/src/pages/Deal.vue:647 frontend/src/pages/MobileDeal.vue:534
msgid "Contact already added"
msgstr "联系人已存在"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:258
msgid "Contact already exists with {0}"
msgstr "联系人{0}已存在"

#: frontend/src/pages/Contact.vue:287 frontend/src/pages/MobileContact.vue:252
msgid "Contact image updated"
msgstr "联系人图片已更新"

#: frontend/src/pages/Deal.vue:668 frontend/src/pages/MobileDeal.vue:555
msgid "Contact removed"
msgstr "联系人已移除"

#: frontend/src/pages/Contact.vue:432 frontend/src/pages/Contact.vue:445
#: frontend/src/pages/Contact.vue:458 frontend/src/pages/Contact.vue:468
#: frontend/src/pages/MobileContact.vue:432
#: frontend/src/pages/MobileContact.vue:445
#: frontend/src/pages/MobileContact.vue:458
#: frontend/src/pages/MobileContact.vue:468
msgid "Contact updated"
msgstr "联系方式已更新"

#. Label of the contacts_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the contacts (Table) field in DocType 'CRM Deal'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Contact.vue:242 frontend/src/pages/MobileContact.vue:212
#: frontend/src/pages/MobileOrganization.vue:331
msgid "Contacts"
msgstr "联系人"

#. Label of the content (Text Editor) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/NoteModal.vue:33
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:89
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:102
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:89
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:102
msgid "Content"
msgstr "内容"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:78
msgid "Content Type"
msgstr "内容类型"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:159
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:163
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:162
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:166
msgid "Content is required"
msgstr "内容为必填项"

#: frontend/src/components/Layouts/AppSidebar.vue:380
#: frontend/src/components/ListBulkActions.vue:88
#: frontend/src/components/Modals/ConvertToDealModal.vue:83
#: frontend/src/pages/MobileLead.vue:54 frontend/src/pages/MobileLead.vue:108
msgid "Convert"
msgstr "转换"

#: frontend/src/components/Layouts/AppSidebar.vue:371
#: frontend/src/components/Layouts/AppSidebar.vue:379
msgid "Convert lead to deal"
msgstr "将线索转换为商机"

#: frontend/src/components/ListBulkActions.vue:80
#: frontend/src/components/ListBulkActions.vue:197
#: frontend/src/components/Modals/ConvertToDealModal.vue:7
#: frontend/src/pages/Lead.vue:38 frontend/src/pages/MobileLead.vue:104
msgid "Convert to Deal"
msgstr "转换为商机"

#. Label of the converted (Check) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Converted"
msgstr "已转换"

#: frontend/src/components/ListBulkActions.vue:96
msgid "Converted successfully"
msgstr "转换成功"

#: frontend/src/utils/index.js:338
msgid "Copied to clipboard"
msgstr "已复制到剪贴板"

#: crm/api/dashboard.py:626 crm/api/dashboard.py:763 crm/api/dashboard.py:824
#: crm/api/dashboard.py:927
msgid "Count"
msgstr "计数"

#: frontend/src/components/Modals/AddressModal.vue:98
#: frontend/src/components/Modals/CallLogModal.vue:101
#: frontend/src/components/Modals/ContactModal.vue:40
#: frontend/src/components/Modals/CreateDocumentModal.vue:92
#: frontend/src/components/Modals/DealModal.vue:66
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:20
#: frontend/src/components/Modals/EventModal.vue:159
#: frontend/src/components/Modals/LeadModal.vue:37
#: frontend/src/components/Modals/NoteModal.vue:52
#: frontend/src/components/Modals/OrganizationModal.vue:41
#: frontend/src/components/Modals/TaskModal.vue:106
#: frontend/src/components/Modals/ViewModal.vue:43
#: frontend/src/components/Settings/EmailAdd.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:23
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:30
#: frontend/src/pages/Calendar.vue:10 frontend/src/pages/CallLogs.vue:13
#: frontend/src/pages/Contacts.vue:13 frontend/src/pages/Contacts.vue:60
#: frontend/src/pages/Deals.vue:13 frontend/src/pages/Deals.vue:236
#: frontend/src/pages/Leads.vue:13 frontend/src/pages/Leads.vue:262
#: frontend/src/pages/Notes.vue:9 frontend/src/pages/Notes.vue:96
#: frontend/src/pages/Organizations.vue:13
#: frontend/src/pages/Organizations.vue:60 frontend/src/pages/Tasks.vue:13
#: frontend/src/pages/Tasks.vue:186
msgid "Create"
msgstr "创建"

#: frontend/src/components/Modals/DealModal.vue:8
msgid "Create Deal"
msgstr "创建商机"

#: frontend/src/components/Modals/LeadModal.vue:8
msgid "Create Lead"
msgstr "创建线索"

#: frontend/src/components/Controls/Link.vue:50
#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:69
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:45
#: frontend/src/components/PrimaryDropdown.vue:41
msgid "Create New"
msgstr "新建"

#: frontend/src/components/Activities/Activities.vue:388
#: frontend/src/components/Modals/NoteModal.vue:6
msgid "Create Note"
msgstr "创建备注"

#: frontend/src/components/Activities/Activities.vue:403
#: frontend/src/components/Modals/TaskModal.vue:6
msgid "Create Task"
msgstr "创建任务"

#: frontend/src/components/Modals/ViewModal.vue:9
#: frontend/src/components/ViewControls.vue:687
msgid "Create View"
msgstr "创建视图"

#: frontend/src/components/Modals/EventModal.vue:12
msgid "Create an event"
msgstr "创建事件"

#. Label of the create_customer_on_status_change (Check) field in DocType
#. 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Create customer on status change"
msgstr "状态变更时创建客户"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:348
#: frontend/src/pages/Calendar.vue:7
msgid "Create event"
msgstr "创建事件"

#: frontend/src/components/Modals/CallLogDetailModal.vue:151
msgid "Create lead"
msgstr "创建线索"

#: frontend/src/components/Layouts/AppSidebar.vue:349
msgid "Create your first lead"
msgstr "创建首条线索"

#: frontend/src/components/Layouts/AppSidebar.vue:419
msgid "Create your first note"
msgstr "创建首条备注"

#: frontend/src/components/Layouts/AppSidebar.vue:399
msgid "Create your first task"
msgstr "创建首项任务"

#. Label of the currency (Link) field in DocType 'CRM Deal'
#. Label of the currency (Link) field in DocType 'CRM Organization'
#. Label of the currency (Link) field in DocType 'FCRM Settings'
#. Label of the currency_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/CurrencySettings.vue:31
msgid "Currency"
msgstr "货币"

#: frontend/src/components/Settings/Settings.vue:114
msgid "Currency & Exchange Rate"
msgstr "货币与汇率"

#: frontend/src/components/Settings/CurrencySettings.vue:7
msgid "Currency & Exchange rate provider"
msgstr "货币与汇率服务商"

#: frontend/src/components/Settings/CurrencySettings.vue:179
msgid "Currency set as {0} successfully"
msgstr "货币已成功设置为{0}。"

#: crm/api/dashboard.py:872
msgid "Current pipeline distribution"
msgstr "当前销售管道分布"

#: frontend/src/components/Layouts/AppSidebar.vue:591
msgid "Custom actions"
msgstr "自定义操作"

#: frontend/src/components/Layouts/AppSidebar.vue:541
msgid "Custom branding"
msgstr "自定义品牌设置"

#: frontend/src/components/Layouts/AppSidebar.vue:590
msgid "Custom fields"
msgstr "自定义字段"

#: frontend/src/components/Layouts/AppSidebar.vue:593
msgid "Custom list actions"
msgstr "列表自定义操作"

#: frontend/src/components/Layouts/AppSidebar.vue:592
msgid "Custom statuses"
msgstr "自定义状态"

#: frontend/src/pages/Deal.vue:476
msgid "Customer created successfully"
msgstr "客户创建成功"

#: frontend/src/components/Layouts/AppSidebar.vue:587
#: frontend/src/components/Settings/Settings.vue:171
msgid "Customization"
msgstr "定制"

#: frontend/src/components/ViewControls.vue:211
msgid "Customize quick filters"
msgstr "自定义快速筛选"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Daily"
msgstr "每天"

#: crm/api/dashboard.py:618
msgid "Daily performance of leads, deals, and wins"
msgstr "线索、商机及赢单的每日表现"

#: frontend/src/components/Activities/DataFields.vue:6
#: frontend/src/components/Layouts/AppSidebar.vue:580
#: frontend/src/pages/Deal.vue:552 frontend/src/pages/Lead.vue:399
#: frontend/src/pages/MobileDeal.vue:445 frontend/src/pages/MobileLead.vue:352
msgid "Data"
msgstr "数据"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Data Fields"
msgstr "数据字段"

#. Label of the date (Date) field in DocType 'CRM Holiday'
#: crm/api/dashboard.py:620 crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:202
msgid "Date"
msgstr "日期"

#: frontend/src/components/Modals/EventModal.vue:78
msgid "Date & Time"
msgstr "日期与时间"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:267
#: frontend/src/components/Calendar/CalendarEventPanel.vue:288
#: frontend/src/components/Layouts/AppSidebar.vue:551
#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:20
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:59
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:78
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:51
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:59
#: frontend/src/components/Telephony/ExotelCallUI.vue:200
#: frontend/src/pages/Tasks.vue:132
msgid "Deal"
msgstr "商机"

#. Label of the deal_owner (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Owner"
msgstr "商机负责人"

#. Label of the deal_status (Link) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Deal Status"
msgstr "商机状态"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Deal Statuses"
msgstr "商机状态列表"

#. Label of the deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Deal Value"
msgstr "商机价值"

#: crm/api/dashboard.py:1019
msgid "Deal generation channel analysis"
msgstr "商机生成渠道分析"

#: frontend/src/pages/Contact.vue:528 frontend/src/pages/MobileContact.vue:528
#: frontend/src/pages/MobileOrganization.vue:472
#: frontend/src/pages/Organization.vue:502
msgid "Deal owner"
msgstr "商机负责人"

#: crm/api/dashboard.py:1075 crm/api/dashboard.py:1135
msgid "Deal value"
msgstr "商机价值"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/Deal.vue:490 frontend/src/pages/MobileContact.vue:288
#: frontend/src/pages/MobileDeal.vue:384
#: frontend/src/pages/MobileOrganization.vue:325
msgid "Deals"
msgstr "商机"

#: crm/api/dashboard.py:818
#: frontend/src/components/Dashboard/AddChartModal.vue:97
msgid "Deals by ongoing & won stage"
msgstr "按进行中及赢单阶段统计的商机"

#: crm/api/dashboard.py:1124
#: frontend/src/components/Dashboard/AddChartModal.vue:100
msgid "Deals by salesperson"
msgstr "按销售人员统计的商机"

#: crm/api/dashboard.py:1018
#: frontend/src/components/Dashboard/AddChartModal.vue:107
msgid "Deals by source"
msgstr "按来源统计的商机"

#: crm/api/dashboard.py:871
#: frontend/src/components/Dashboard/AddChartModal.vue:105
msgid "Deals by stage"
msgstr "按阶段统计的商机"

#: crm/api/dashboard.py:1064
#: frontend/src/components/Dashboard/AddChartModal.vue:99
msgid "Deals by territory"
msgstr "按区域统计的商机"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:112
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:112
msgid "Dear {{ lead_name }}, \\n\\nThis is a reminder for the payment of {{ grand_total }}. \\n\\nThanks, \\nFrappé"
msgstr "尊敬的{{ lead_name }}：\\n\\n特此提醒您尚有{{ grand_total }}款项待支付。\\n\\n此致\\nFrappé团队"

#. Label of the default (Check) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Default"
msgstr "默认"

#: frontend/src/components/Settings/EmailAccountCard.vue:41
msgid "Default Inbox"
msgstr "默认收件箱"

#: frontend/src/components/Settings/emailConfig.js:44
msgid "Default Incoming"
msgstr "默认收件箱"

#. Label of the default_medium (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Default Medium"
msgstr "默认媒介"

#: frontend/src/components/Settings/emailConfig.js:52
msgid "Default Outgoing"
msgstr "默认外发"

#. Label of the default_priority (Check) field in DocType 'CRM Service Level
#. Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "Default Priority"
msgstr "默认优先级"

#: frontend/src/components/Settings/EmailAccountCard.vue:43
msgid "Default Sending"
msgstr "默认发送账号"

#: frontend/src/components/Settings/EmailAccountCard.vue:39
msgid "Default Sending and Inbox"
msgstr "默认发送和收件账号"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:33
msgid "Default Service Level Agreement already exists for {0}"
msgstr "{0}的默认服务级别协议已存在"

#: frontend/src/components/Settings/TelephonySettings.vue:44
msgid "Default calling medium for logged in user"
msgstr "当前用户的默认呼叫媒介"

#: frontend/src/components/Telephony/CallUI.vue:112
msgid "Default calling medium set successfully to {0}"
msgstr "默认呼叫媒介已成功设置为{0}。"

#: frontend/src/components/Settings/TelephonySettings.vue:280
msgid "Default calling medium updated successfully"
msgstr "默认呼叫媒介更新成功"

#: frontend/src/components/Settings/TelephonySettings.vue:37
msgid "Default medium"
msgstr "默认媒介"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:18
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:30
msgid "Default statuses, custom fields and layouts restored successfully."
msgstr "默认状态、自定义字段及布局已成功恢复。"

#: frontend/src/components/Activities/AttachmentArea.vue:131
#: frontend/src/components/Activities/NoteArea.vue:12
#: frontend/src/components/Activities/TaskArea.vue:55
#: frontend/src/components/Activities/TaskArea.vue:63
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:8
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:48
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
#: frontend/src/components/BulkDeleteLinkedDocModal.vue:121
#: frontend/src/components/Controls/Grid.vue:329
#: frontend/src/components/DeleteLinkedDocModal.vue:10
#: frontend/src/components/DeleteLinkedDocModal.vue:90
#: frontend/src/components/Kanban/KanbanView.vue:222
#: frontend/src/components/ListBulkActions.vue:179
#: frontend/src/components/Modals/EventModal.vue:407
#: frontend/src/components/Modals/EventModal.vue:411
#: frontend/src/components/PrimaryDropdownItem.vue:42
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:129
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:229
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:199
#: frontend/src/components/ViewControls.vue:1116
#: frontend/src/components/ViewControls.vue:1127
#: frontend/src/pages/Calendar.vue:299 frontend/src/pages/Calendar.vue:303
#: frontend/src/pages/Contact.vue:106 frontend/src/pages/Deal.vue:111
#: frontend/src/pages/Lead.vue:157 frontend/src/pages/MobileContact.vue:79
#: frontend/src/pages/MobileContact.vue:263
#: frontend/src/pages/MobileDeal.vue:515
#: frontend/src/pages/MobileOrganization.vue:72
#: frontend/src/pages/MobileOrganization.vue:264
#: frontend/src/pages/Notes.vue:43 frontend/src/pages/Organization.vue:89
#: frontend/src/pages/Tasks.vue:371
msgid "Delete"
msgstr "删除"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:26
msgid "Delete & Restore"
msgstr "删除并恢复"

#: frontend/src/components/Activities/TaskArea.vue:59
msgid "Delete Task"
msgstr "删除任务"

#: frontend/src/components/ViewControls.vue:1112
#: frontend/src/components/ViewControls.vue:1120
msgid "Delete View"
msgstr "删除视图"

#: frontend/src/components/DeleteLinkedDocModal.vue:66
msgid "Delete all"
msgstr "全部删除"

#: frontend/src/components/Activities/AttachmentArea.vue:58
#: frontend/src/components/Activities/AttachmentArea.vue:127
msgid "Delete attachment"
msgstr "删除附件"

#: frontend/src/pages/MobileContact.vue:259
msgid "Delete contact"
msgstr "删除联系人"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:25
msgid "Delete event"
msgstr "删除事件"

#: frontend/src/components/Settings/InviteUserPage.vue:79
msgid "Delete invitation"
msgstr "删除邀请"

#: frontend/src/components/DeleteLinkedDocModal.vue:230
msgid "Delete linked item"
msgstr "删除关联项"

#: frontend/src/components/DeleteLinkedDocModal.vue:11
msgid "Delete or unlink linked documents"
msgstr "删除或取消关联文档"

#: frontend/src/components/DeleteLinkedDocModal.vue:23
msgid "Delete or unlink these linked documents before deleting this document"
msgstr "删除本文档前，请先删除或取消关联以下文档。"

#: frontend/src/pages/MobileOrganization.vue:260
msgid "Delete organization"
msgstr "删除组织"

#: frontend/src/components/DeleteLinkedDocModal.vue:67
msgid "Delete {0} item(s)"
msgstr "删除{0}个项目"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:28
msgid "Delete {0} items"
msgstr "删除{0}个项目"

#. Label of the description (Text Editor) field in DocType 'CRM Holiday'
#. Label of the description (Text Editor) field in DocType 'CRM Lost Reason'
#. Label of the description (Text Editor) field in DocType 'CRM Product'
#. Label of the description (Text Editor) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:152
#: frontend/src/components/Modals/EventModal.vue:134
#: frontend/src/components/Modals/TaskModal.vue:36
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:112
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:113
msgid "Description"
msgstr "描述"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:426
msgid "Description is required"
msgstr "必须填写描述信息。"

#: frontend/src/components/Apps.vue:63
msgid "Desk"
msgstr "工作台"

#. Label of the details (Tab Break) field in DocType 'CRM Lead'
#. Label of the details (Text Editor) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/pages/MobileContact.vue:283
#: frontend/src/pages/MobileDeal.vue:424 frontend/src/pages/MobileLead.vue:331
#: frontend/src/pages/MobileOrganization.vue:320
msgid "Details"
msgstr "详情"

#. Label of the call_receiving_device (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:39
msgid "Device"
msgstr "设备"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Disable"
msgstr "禁用"

#. Label of the disabled (Check) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Disabled"
msgstr "禁用"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:626
#: frontend/src/components/CommentBox.vue:74
#: frontend/src/components/EmailEditor.vue:161
msgid "Discard"
msgstr "放弃"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:614
msgid "Discard unsaved changes?"
msgstr "是否放弃未保存的修改？"

#. Label of the discount_percentage (Percent) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount %"
msgstr "折扣 %"

#. Label of the discount_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Discount Amount"
msgstr "折扣金额"

#. Label of the dt (Link) field in DocType 'CRM Form Script'
#. Label of the dt (Link) field in DocType 'CRM Global Settings'
#. Label of the dt (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "DocType"
msgstr "文档类型"

#. Label of the dt (Link) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Document Type"
msgstr "文档类型"

#: frontend/src/data/document.js:30
msgid "Document does not exist"
msgstr "文档不存在。"

#: crm/api/activities.py:19
msgid "Document not found"
msgstr "未找到文档"

#: frontend/src/data/document.js:45
msgid "Document updated successfully"
msgstr "文档更新成功。"

#: frontend/src/components/Modals/AboutModal.vue:62
msgid "Documentation"
msgstr "用户操作手册"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Done"
msgstr "完成"

#: frontend/src/components/Dashboard/AddChartModal.vue:33
#: frontend/src/components/Dashboard/AddChartModal.vue:71
msgid "Donut chart"
msgstr "环形图"

#: frontend/src/components/Activities/AudioPlayer.vue:166
#: frontend/src/components/ViewControls.vue:254
msgid "Download"
msgstr "下载"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:24
msgid "Drag and drop files here or upload from"
msgstr "拖放文件至此或从本地"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:56
msgid "Drop files here"
msgstr "拖放文件至此"

#. Label of the dropdown_items_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Dropdown Items"
msgstr "下拉选项"

#. Label of the due_date (Datetime) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Due Date"
msgstr "截止日期"

#. Option for the 'Type' (Select) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: frontend/src/components/Modals/EventModal.vue:158
#: frontend/src/components/Modals/ViewModal.vue:42
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:57
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:119
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:224
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:23
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:194
#: frontend/src/components/ViewControls.vue:1068
msgid "Duplicate"
msgstr "复制"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:38
msgid "Duplicate Assignment Rule"
msgstr "复制分配规则"

#: frontend/src/components/Modals/ViewModal.vue:8
msgid "Duplicate View"
msgstr "复制视图"

#: frontend/src/components/Modals/EventModal.vue:11
msgid "Duplicate an event"
msgstr "复制事件"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:33
#: frontend/src/components/Calendar/CalendarEventPanel.vue:347
#: frontend/src/components/Calendar/CalendarEventPanel.vue:432
msgid "Duplicate event"
msgstr "复制事件"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "Duplicate template"
msgstr "复制模板"

#. Label of the duration (Duration) field in DocType 'CRM Call Log'
#. Label of the duration (Duration) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Duration"
msgstr "持续时间"

#: frontend/src/components/Layouts/AppSidebar.vue:604
#: frontend/src/components/Settings/Settings.vue:197
msgid "ERPNext"
msgstr "ERPNext"

#. Name of a DocType
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext CRM Settings"
msgstr "ERPNext CRM设置"

#. Label of the section_break_oubd (Section Break) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site API's"
msgstr "ERPNext站点API"

#. Label of the erpnext_site_url (Data) field in DocType 'ERPNext CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "ERPNext Site URL"
msgstr "ERPNext站点URL"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:25
msgid "ERPNext is not installed in the current site"
msgstr "当前站点未安装ERPNext"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:98
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:124
msgid "ERPNext is not integrated with the CRM"
msgstr "ERPNext未与CRM集成"

#: frontend/src/components/Settings/ERPNextSettings.vue:4
msgid "ERPNext settings"
msgstr "ERPNext系统设置"

#: frontend/src/components/Settings/ERPNextSettings.vue:5
msgid "ERPNext settings updated"
msgstr "ERPNext系统设置更新成功。"

#: frontend/src/components/FieldLayout/Field.vue:91
#: frontend/src/components/FieldLayoutEditor.vue:313
#: frontend/src/components/FieldLayoutEditor.vue:339
#: frontend/src/components/ListBulkActions.vue:172
#: frontend/src/components/PrimaryDropdownItem.vue:35
#: frontend/src/components/ViewControls.vue:1086
#: frontend/src/pages/Dashboard.vue:16
msgid "Edit"
msgstr "编辑"

#: frontend/src/components/Modals/CallLogModal.vue:97
msgid "Edit Call Log"
msgstr "编辑通话记录"

#: frontend/src/components/Modals/DataFieldsModal.vue:7
msgid "Edit Data Fields Layout"
msgstr "编辑数据字段布局"

#: frontend/src/components/Settings/EmailEdit.vue:6
msgid "Edit Email"
msgstr "编辑邮件"

#: frontend/src/components/Modals/SidePanelModal.vue:7
msgid "Edit Field Layout"
msgstr "编辑字段布局"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:7
msgid "Edit Grid Fields Layout"
msgstr "编辑网格字段布局"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:7
msgid "Edit Grid Row Fields Layout"
msgstr "编辑网格行字段布局"

#: frontend/src/components/Modals/NoteModal.vue:6
msgid "Edit Note"
msgstr "编辑备注"

#: frontend/src/components/Modals/QuickEntryModal.vue:7
msgid "Edit Quick Entry Layout"
msgstr "编辑快速录入布局"

#: frontend/src/components/Modals/TaskModal.vue:6
msgid "Edit Task"
msgstr "编辑任务"

#: frontend/src/components/Modals/ViewModal.vue:6
msgid "Edit View"
msgstr "编辑视图"

#: frontend/src/components/Modals/EventModal.vue:9
msgid "Edit an event"
msgstr "编辑事件"

#: frontend/src/components/Modals/CallLogDetailModal.vue:39
msgid "Edit call log"
msgstr "编辑通话记录"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:18
msgid "Edit event"
msgstr "编辑事件"

#: frontend/src/components/Activities/DataFields.vue:17
#: frontend/src/components/Controls/GridRowModal.vue:14
#: frontend/src/components/Modals/AddressModal.vue:14
#: frontend/src/components/Modals/CallLogModal.vue:16
#: frontend/src/components/Modals/ContactModal.vue:16
#: frontend/src/components/Modals/CreateDocumentModal.vue:16
#: frontend/src/components/Modals/DealModal.vue:16
#: frontend/src/components/Modals/LeadModal.vue:16
#: frontend/src/components/Modals/OrganizationModal.vue:16
msgid "Edit fields layout"
msgstr "编辑字段布局"

#: frontend/src/components/Controls/Grid.vue:57
msgid "Edit grid fields"
msgstr "编辑网格字段"

#: frontend/src/components/Modals/CallLogDetailModal.vue:19
msgid "Edit note"
msgstr "编辑备注"

#: frontend/src/components/Controls/Grid.vue:297
msgid "Edit row"
msgstr "编辑行"

#: frontend/src/components/Modals/CallLogDetailModal.vue:24
msgid "Edit task"
msgstr "编辑任务"

#: frontend/src/components/Controls/GridRowModal.vue:8
msgid "Editing Row {0}"
msgstr "正在编辑第{0}行"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:430
msgid "Editing event"
msgstr "正在编辑事件"

#. Label of the email (Data) field in DocType 'CRM Contacts'
#. Label of the email (Data) field in DocType 'CRM Invitation'
#. Label of the email (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json frontend/src/pages/Contact.vue:518
#: frontend/src/pages/MobileContact.vue:518
#: frontend/src/pages/MobileOrganization.vue:462
#: frontend/src/pages/MobileOrganization.vue:490
#: frontend/src/pages/Organization.vue:492
#: frontend/src/pages/Organization.vue:520
msgid "Email"
msgstr "电子邮件"

#: frontend/src/components/Settings/Settings.vue:148
msgid "Email Accounts"
msgstr "邮件账户"

#: frontend/src/components/Settings/emailConfig.js:168
msgid "Email ID is required"
msgstr "必须填写邮件地址。"

#. Label of the email_sent_at (Datetime) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Email Sent At"
msgstr "发送时间"

#: frontend/src/components/Settings/Settings.vue:145
msgid "Email Settings"
msgstr "邮件设置"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:4
#: frontend/src/components/Settings/Settings.vue:154
msgid "Email Templates"
msgstr "邮件模板"

#: frontend/src/components/Settings/EmailAdd.vue:141
msgid "Email account created successfully"
msgstr "邮件账户创建成功。"

#: frontend/src/components/Settings/EmailEdit.vue:208
msgid "Email account updated successfully"
msgstr "邮件账户更新成功。"

#: frontend/src/components/Settings/EmailAccountList.vue:7
msgid "Email accounts"
msgstr "邮件账户"

#: frontend/src/components/Layouts/AppSidebar.vue:578
msgid "Email communication"
msgstr "邮件沟通"

#: frontend/src/components/EmailEditor.vue:209
msgid "Email from Lead"
msgstr "来自线索的邮件"

#: frontend/src/components/Layouts/AppSidebar.vue:557
msgid "Email template"
msgstr "邮件模板"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:7
msgid "Email templates"
msgstr "邮件模板"

#: frontend/src/pages/Deal.vue:542 frontend/src/pages/Lead.vue:389
#: frontend/src/pages/MobileDeal.vue:435 frontend/src/pages/MobileLead.vue:342
msgid "Emails"
msgstr "邮件"

#: frontend/src/components/ListViews/ListRows.vue:12
msgid "Empty"
msgstr "空"

#: frontend/src/components/Filter.vue:123
msgid "Empty - Choose a field to filter by"
msgstr "空 - 选择筛选字段"

#: frontend/src/components/SortBy.vue:130
msgid "Empty - Choose a field to sort by"
msgstr "空 - 选择排序字段"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:30
msgid "Enable"
msgstr "启用"

#. Label of the enable_forecasting (Check) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Enable Forecasting"
msgstr "启用预测功能"

#: frontend/src/components/Settings/emailConfig.js:28
msgid "Enable Incoming"
msgstr "收邮件"

#: frontend/src/components/Settings/emailConfig.js:36
msgid "Enable Outgoing"
msgstr "启用该邮箱帐号发送邮件"

#: frontend/src/components/Settings/ForecastingSettings.vue:20
msgid "Enable forecasting"
msgstr "启用预测功能"

#. Label of the enabled (Check) field in DocType 'CRM Exotel Settings'
#. Label of the enabled (Check) field in DocType 'CRM Form Script'
#. Label of the enabled (Check) field in DocType 'CRM Service Level Agreement'
#. Label of the enabled (Check) field in DocType 'CRM Twilio Settings'
#. Label of the enabled (Check) field in DocType 'ERPNext CRM Settings'
#. Label of the enabled (Check) field in DocType 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:32
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:21
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:18
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:85
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:20
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:18
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:61
msgid "Enabled"
msgstr "已启用"

#. Label of the enabled (Check) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Enabled?"
msgstr "已启用?"

#. Label of the end_date (Date) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "End Date"
msgstr "结束日期"

#. Label of the end_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the end_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:242
#: frontend/src/components/Modals/EventModal.vue:112
msgid "End Time"
msgstr "结束时间"

#: frontend/src/composables/event.js:206
msgid "End time should be after start time"
msgstr "结束时间必须晚于开始时间。"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:88
msgid "Enter Access Token"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:70
msgid "Enter Source Name"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:115
msgid "Enter access key"
msgstr "请输入访问密钥"

#: frontend/src/components/Settings/BrandSettings.vue:33
msgid "Enter brand name"
msgstr "请输入品牌名称"

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:30
msgid "Enter your Facebook Access Token"
msgstr ""

#: frontend/src/components/FieldLayout/Field.vue:347
msgid "Enter {0}"
msgstr "输入{0}"

#: frontend/src/components/Filter.vue:66 frontend/src/components/Filter.vue:99
#: frontend/src/components/Filter.vue:267
#: frontend/src/components/Filter.vue:288
#: frontend/src/components/Filter.vue:305
#: frontend/src/components/Filter.vue:316
#: frontend/src/components/Filter.vue:327
#: frontend/src/components/Filter.vue:343
msgid "Equals"
msgstr "等于"

#: frontend/src/components/Modals/ConvertToDealModal.vue:176
msgid "Error converting to deal: {0}"
msgstr "转换为商机时出错：{0}"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:245
msgid "Error creating Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:362
msgid "Error syncing leads"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:226
msgid "Error updating Lead Sync Source"
msgstr ""

#: frontend/src/pages/Deal.vue:741 frontend/src/pages/Lead.vue:469
#: frontend/src/pages/MobileDeal.vue:612 frontend/src/pages/MobileLead.vue:412
msgid "Error updating field"
msgstr "字段更新失败"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:279
msgid "Error while creating customer in ERPNext, check error log for more details"
msgstr "ERPNext创建客户失败，请查看错误日志"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:191
msgid "Error while creating prospect in ERPNext, check error log for more details"
msgstr "ERPNext创建潜在客户失败，请查看错误日志"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.py:117
msgid "Error while fetching customer in ERPNext, check error log for more details"
msgstr "ERPNext获取客户失败，请查看错误日志"

#: frontend/src/components/Modals/EventModal.vue:368
msgid "Event ID is required"
msgstr "必须填写事件ID。"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:429
msgid "Event details"
msgstr "事件详情"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:182
msgid "Event title"
msgstr "事件标题"

#: frontend/src/pages/Deal.vue:557 frontend/src/pages/Lead.vue:404
msgid "Events"
msgstr "事件"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 10 Minutes"
msgstr ""

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 15 Minutes"
msgstr ""

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Every 5 Minutes"
msgstr ""

#: frontend/src/components/ViewControls.vue:268
#: frontend/src/components/ViewControls.vue:277
msgid "Excel"
msgstr "Excel"

#. Label of the exchange_rate (Float) field in DocType 'CRM Deal'
#. Label of the exchange_rate (Float) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Exchange Rate"
msgstr "汇率"

#. Label of the exchange_rate_provider_section (Section Break) field in DocType
#. 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Exchange Rate Provider"
msgstr "汇率服务商"

#: frontend/src/components/Settings/CurrencySettings.vue:60
msgid "Exchange rate provider"
msgstr "汇率服务商"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the exotel (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:602
#: frontend/src/components/Settings/TelephonySettings.vue:41
#: frontend/src/components/Settings/TelephonySettings.vue:63
msgid "Exotel"
msgstr "Exotel"

#: crm/integrations/exotel/handler.py:114
msgid "Exotel Exception"
msgstr "Exotel异常"

#. Label of the exotel_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Exotel Number"
msgstr "Exotel号码"

#: crm/integrations/exotel/handler.py:85
msgid "Exotel Number Missing"
msgstr "Exotel号码缺失"

#: crm/integrations/exotel/handler.py:89
msgid "Exotel Number {0} is not valid"
msgstr "Exotel号码{0}无效"

#: frontend/src/components/Settings/TelephonySettings.vue:293
msgid "Exotel is not enabled"
msgstr "Exotel未启用"

#: frontend/src/components/Settings/TelephonySettings.vue:140
msgid "Exotel settings updated successfully"
msgstr "Exotel设置更新成功"

#: frontend/src/components/Layouts/AppSidebar.vue:107
msgid "Expand"
msgstr "展开"

#. Label of the expected_closure_date (Date) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Closure Date"
msgstr "预期关闭日期"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:173
msgid "Expected Closure Date is required."
msgstr "必须填写预期关闭日期。"

#. Label of the expected_deal_value (Currency) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Expected Deal Value"
msgstr "预期商机价值"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:171
msgid "Expected Deal Value is required."
msgstr "必须填写预期商机价值。"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Expired"
msgstr "已过期"

#: frontend/src/components/ViewControls.vue:203
#: frontend/src/components/ViewControls.vue:251
msgid "Export"
msgstr "导出"

#: frontend/src/components/ViewControls.vue:282
msgid "Export All {0} Record(s)"
msgstr "导出全部{0}条记录"

#: frontend/src/components/ViewControls.vue:264
msgid "Export Type"
msgstr "导出类型"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "FCRM Note"
msgstr "FCRM备注"

#. Name of a DocType
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "FCRM Settings"
msgstr "FCRM设置"

#. Option for the 'Type' (Select) field in DocType 'Lead Sync Source'
#. Label of the facebook_section (Section Break) field in DocType 'Lead Sync
#. Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook"
msgstr "Facebook"

#. Label of the facebook_form_id (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Facebook Form ID"
msgstr ""

#. Name of a DocType
#. Label of the facebook_lead_form (Link) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook Lead Form"
msgstr ""

#. Name of a DocType
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Facebook Lead Form Question"
msgstr ""

#. Label of the facebook_lead_id (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Facebook Lead ID"
msgstr ""

#. Name of a DocType
#. Label of the facebook_page (Link) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Facebook Page"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Failed"
msgstr "失败"

#. Name of a DocType
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Failed Lead Sync Log"
msgstr ""

#: frontend/src/components/Modals/AddExistingUserModal.vue:111
msgid "Failed to add users"
msgstr "用户添加失败"

#: crm/integrations/twilio/api.py:130
msgid "Failed to capture Twilio recording"
msgstr "获取Twilio录音失败"

#: frontend/src/components/Settings/EmailAdd.vue:145
msgid "Failed to create email account, Invalid credentials"
msgstr "邮件账户创建失败，凭证无效"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:179
msgid "Failed to create template"
msgstr "模板创建失败"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:186
msgid "Failed to delete Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:216
msgid "Failed to delete template"
msgstr "模板删除失败"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:205
msgid "Failed to fetch exchange rate from {0} to {1} on {2}. Please check your internet connection or try again later."
msgstr "获取{2}当日{0}至{1}的汇率失败。请检查网络连接或稍后重试。"

#: frontend/src/data/script.js:106
msgid "Failed to load form controller: {0}"
msgstr "表单控制器加载失败：{0}"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:242
msgid "Failed to rename template"
msgstr "模板重命名失败"

#: crm/integrations/twilio/api.py:152
msgid "Failed to update Twilio call status"
msgstr "更新Twilio通话状态失败"

#: frontend/src/components/Settings/EmailEdit.vue:213
msgid "Failed to update email account, Invalid credentials"
msgstr "邮件账户更新失败，凭证无效"

#: frontend/src/components/Modals/ChangePasswordModal.vue:95
msgid "Failed to update password"
msgstr "密码更新失败"

#: frontend/src/components/Settings/ProfileSettings.vue:151
msgid "Failed to update profile"
msgstr "个人资料更新失败"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:171
msgid "Failed to update source"
msgstr ""

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:208
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:201
msgid "Failed to update template"
msgstr "模板更新失败"

#. Option for the 'Type' (Select) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Failure"
msgstr "故障"

#. Label of the favicon (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/BrandSettings.vue:87
msgid "Favicon"
msgstr "网站图标"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:38
#: frontend/src/components/Modals/EditValueModal.vue:5
msgid "Field"
msgstr "字段"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:19
#: frontend/src/components/Kanban/KanbanSettings.vue:48
msgid "Fields Order"
msgstr "字段顺序"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:333
msgid "File \"{0}\" was skipped because of invalid file type"
msgstr "由于文件类型无效，文件“{0}”被跳过"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:354
msgid "File \"{0}\" was skipped because only {1} uploads are allowed"
msgstr "文件\"{0}\"已跳过，因为最多只允许上传{1}个文件"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:359
msgid "File \"{0}\" was skipped because only {1} uploads are allowed for DocType \"{2}\""
msgstr "文件\"{0}\"已跳过，因为文档类型\"{2}\"最多只允许上传{1}个文件"

#: frontend/src/components/Filter.vue:6
msgid "Filter"
msgstr "筛选器"

#. Label of the filters (Code) field in DocType 'CRM View Settings'
#. Label of the filters_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Filters"
msgstr "筛选条件"

#. Label of the first_name (Data) field in DocType 'CRM Deal'
#. Label of the first_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/ColumnSettings.vue:103
#: frontend/src/components/Filter.vue:57 frontend/src/components/Filter.vue:88
#: frontend/src/components/SortBy.vue:6 frontend/src/components/SortBy.vue:103
#: frontend/src/components/SortBy.vue:136
msgid "First Name"
msgstr "名字"

#: frontend/src/components/Modals/LeadModal.vue:134
msgid "First Name is mandatory"
msgstr "名字为必填项"

#. Label of the first_responded_on (Datetime) field in DocType 'CRM Deal'
#. Label of the first_responded_on (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Responded On"
msgstr "首次响应时间"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Due"
msgstr "首次响应截止"

#. Label of the first_response_time (Duration) field in DocType 'CRM Service
#. Level Priority'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
msgid "First Response TIme"
msgstr "首次响应时间"

#. Label of the first_response_time (Duration) field in DocType 'CRM Deal'
#. Label of the first_response_time (Duration) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "First Response Time"
msgstr "首次响应时间"

#: frontend/src/components/Filter.vue:130
#: frontend/src/components/Settings/ProfileSettings.vue:78
msgid "First name"
msgstr "名字"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:48
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:84
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:48
msgid "For"
msgstr "目标"

#: crm/api/dashboard.py:688
#: frontend/src/components/Dashboard/AddChartModal.vue:95
msgid "Forecasted revenue"
msgstr "预测收入"

#: frontend/src/components/Settings/ForecastingSettings.vue:5
#: frontend/src/components/Settings/Settings.vue:109
msgid "Forecasting"
msgstr "预测"

#: frontend/src/components/Settings/ForecastingSettings.vue:76
msgid "Forecasting disabled successfully"
msgstr "预测功能已成功禁用"

#: frontend/src/components/Settings/ForecastingSettings.vue:75
msgid "Forecasting enabled successfully"
msgstr "预测功能已成功启用"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Form"
msgstr "表单"

#. Label of the form_name (Data) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Form Name"
msgstr ""

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:19
msgid "Form Script updated successfully"
msgstr "表单脚本更新成功"

#. Name of a Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Frappe CRM"
msgstr "Frappe CRM"

#: frontend/src/components/Layouts/AppSidebar.vue:608
msgid "Frappe CRM mobile"
msgstr "Frappe CRM移动端"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Friday"
msgstr "周五"

#. Label of the from (Data) field in DocType 'CRM Call Log'
#. Label of the from (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From"
msgstr "发件人"

#. Label of the from_date (Date) field in DocType 'CRM Holiday List'
#. Label of the from_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Date"
msgstr "起始日期"

#. Label of the from_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "From Type"
msgstr "来源类型"

#. Label of the from_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "From User"
msgstr "来自用户"

#. Option for the 'SLA Status' (Select) field in DocType 'CRM Deal'
#. Option for the 'SLA Status' (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Fulfilled"
msgstr "已完成"

#. Label of the full_name (Data) field in DocType 'CRM Contacts'
#. Label of the lead_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Full Name"
msgstr "全名"

#: crm/api/dashboard.py:755
#: frontend/src/components/Dashboard/AddChartModal.vue:96
msgid "Funnel conversion"
msgstr "销售漏斗转化率"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:196
msgid "GMT+5:30"
msgstr "GMT+5:30"

#. Label of the gender (Link) field in DocType 'CRM Contacts'
#. Label of the gender (Link) field in DocType 'CRM Deal'
#. Label of the gender (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Gender"
msgstr "性别"

#: crm/api/dashboard.py:1065
msgid "Geographic distribution of deals and revenue"
msgstr "商机与收入的地理分布"

#: frontend/src/components/Modals/AboutModal.vue:57
msgid "GitHub Repository"
msgstr "GitHub代码仓库"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:582
msgid "Go back"
msgstr "返回"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:156
msgid "Go to invite page"
msgstr "前往邀请页面"

#: frontend/src/pages/Deal.vue:95 frontend/src/pages/Lead.vue:141
msgid "Go to website"
msgstr "访问网站"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Grid Row"
msgstr "网格行"

#. Label of the group_by_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:379
#: frontend/src/components/ViewControls.vue:611 frontend/src/utils/view.js:16
msgid "Group By"
msgstr "分组依据"

#. Label of the group_by_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Group By Field"
msgstr "分组字段"

#: frontend/src/components/GroupBy.vue:8
msgid "Group By: "
msgstr "分组依据："

#: frontend/src/components/Telephony/TwilioCallUI.vue:63
msgid "Hang up"
msgstr "挂断通话"

#: crm/templates/emails/helpdesk_invitation.html:2
msgid "Hello"
msgstr "你好"

#: frontend/src/components/Layouts/AppSidebar.vue:93
msgid "Help"
msgstr "帮助"

#: frontend/src/components/Settings/Settings.vue:203
msgid "Helpdesk"
msgstr "服务台"

#. Name of a DocType
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk CRM Settings"
msgstr "服务台CRM设置"

#. Label of the helpdesk_site_apis_section (Section Break) field in DocType
#. 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk Site API's"
msgstr "服务台站点API"

#. Label of the helpdesk_site_url (Data) field in DocType 'Helpdesk CRM
#. Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Helpdesk Site URL"
msgstr "服务台站点URL"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:18
msgid "Helpdesk is not installed in the current site"
msgstr "当前站点未安装服务台"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:40
msgid "Helpdesk is not integrated with the CRM"
msgstr "服务台未与CRM系统集成"

#: frontend/src/components/Settings/HelpdeskSettings.vue:4
msgid "Helpdesk settings"
msgstr "服务台设置"

#: frontend/src/components/Settings/HelpdeskSettings.vue:5
msgid "Helpdesk settings updated"
msgstr "服务台设置更新成功"

#: frontend/src/components/CommunicationArea.vue:56
msgid "Hi John, \\n\\nCan you please provide more details on this..."
msgstr "John 您好：\\n\\n可否请您就此事项提供更多详细信息..."

#. Label of the hidden (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Hidden"
msgstr "隐藏"

#: frontend/src/components/Activities/Activities.vue:237
msgid "Hide"
msgstr "隐藏"

#: frontend/src/components/Controls/Password.vue:19
msgid "Hide Password"
msgstr "隐藏密码"

#: frontend/src/components/Activities/CallArea.vue:75
msgid "Hide Recording"
msgstr "隐藏录音"

#: frontend/src/components/FieldLayoutEditor.vue:354
msgid "Hide border"
msgstr "隐藏边框"

#: frontend/src/components/FieldLayoutEditor.vue:349
msgid "Hide label"
msgstr "隐藏标签"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Hide preview"
msgstr "隐藏预览"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "High"
msgstr "高"

#. Label of the holiday_list (Link) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Holiday List"
msgstr "假期列表"

#. Label of the holiday_list_name (Data) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holiday List Name"
msgstr "假期列表名称"

#. Label of the holidays_section (Section Break) field in DocType 'CRM Holiday
#. List'
#. Label of the holidays (Table) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Holidays"
msgstr "假期"

#: frontend/src/components/Settings/Settings.vue:174
msgid "Home Actions"
msgstr "首页操作"

#: frontend/src/components/Layouts/AppSidebar.vue:542
#: frontend/src/components/Settings/HomeActions.vue:7
msgid "Home actions"
msgstr "主页操作"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Hourly"
msgstr "每小时"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:199
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:283
msgid "I understand, add conditions"
msgstr "我已知悉，继续添加条件"

#. Label of the id (Data) field in DocType 'CRM Call Log'
#. Label of the id (Data) field in DocType 'Facebook Lead Form'
#. Label of the id (Data) field in DocType 'Facebook Lead Form Question'
#. Label of the id (Data) field in DocType 'Facebook Page'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "ID"
msgstr "ID"

#. Label of the icon (Code) field in DocType 'CRM Dropdown Item'
#. Label of the icon (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Icon"
msgstr "图标"

#: frontend/src/components/Settings/emailConfig.js:55
msgid "If enabled, all outgoing emails will be sent from this account. Note: Only one account can be default outgoing."
msgstr "若启用，所有外发邮件将通过本账户发送。注：仅可设置一个默认发件账户。"

#: frontend/src/components/Settings/emailConfig.js:47
msgid "If enabled, all replies to your company (eg: <EMAIL>) will come to this account. Note: Only one account can be default incoming."
msgstr "若启用，发送至贵司的邮件回复（如：<EMAIL>）将统一归集至本账户。注：仅可设置一个默认收件账户。"

#: frontend/src/components/Settings/emailConfig.js:39
msgid "If enabled, outgoing emails can be sent from this account."
msgstr "若启用，可通过本账户发送外发邮件。"

#: frontend/src/components/Settings/emailConfig.js:31
msgid "If enabled, records can be created from the incoming emails on this account."
msgstr "若启用，可根据本账户的收件自动创建记录。"

#. Label of the image (Attach Image) field in DocType 'CRM Lead'
#. Label of the image (Attach Image) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Image"
msgstr "图片"

#: frontend/src/components/Filter.vue:271
#: frontend/src/components/Filter.vue:292
#: frontend/src/components/Filter.vue:307
#: frontend/src/components/Filter.vue:320
#: frontend/src/components/Filter.vue:334
msgid "In"
msgstr "在"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "In Progress"
msgstr "进行中"

#: frontend/src/components/SLASection.vue:68
msgid "In less than a minute"
msgstr "不到一分钟前"

#: frontend/src/components/Activities/CallArea.vue:36
msgid "Inbound Call"
msgstr "呼入通话"

#: frontend/src/components/Settings/EmailAccountCard.vue:45
msgid "Inbox"
msgstr "收件箱"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Incoming"
msgstr "呼入"

#: frontend/src/components/Telephony/TwilioCallUI.vue:41
msgid "Incoming call..."
msgstr "来电中..."

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Industries"
msgstr "行业"

#. Label of the industry (Link) field in DocType 'CRM Deal'
#. Label of the industry (Data) field in DocType 'CRM Industry'
#. Label of the industry (Link) field in DocType 'CRM Lead'
#. Label of the industry (Link) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Industry"
msgstr "行业"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Initiated"
msgstr "已启动"

#: frontend/src/components/Telephony/TwilioCallUI.vue:36
msgid "Initiating call..."
msgstr "正在发起呼叫..."

#: frontend/src/components/EmailEditor.vue:154
msgid "Insert Email Template"
msgstr "插入邮件模板"

#: frontend/src/components/CommentBox.vue:49
#: frontend/src/components/EmailEditor.vue:130
msgid "Insert Emoji"
msgstr "插入表情符号"

#: frontend/src/components/Layouts/AppSidebar.vue:598
msgid "Integration"
msgstr "系统集成"

#: crm/integrations/exotel/handler.py:73
msgid "Integration Not Enabled"
msgstr "未启用集成"

#: frontend/src/components/Settings/Settings.vue:182
msgctxt "FCRM"
msgid "Integrations"
msgstr "集成"

#: frontend/src/components/Layouts/AppSidebar.vue:529
#: frontend/src/components/Layouts/AppSidebar.vue:532
msgid "Introduction"
msgstr "简介"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:33
msgid "Invalid Account SID or Auth Token."
msgstr "无效的账户SID或认证令牌"

#: frontend/src/components/Modals/DealModal.vue:212
#: frontend/src/components/Modals/LeadModal.vue:153
msgid "Invalid Email"
msgstr "无效的电子邮件"

#: crm/integrations/exotel/handler.py:89
msgid "Invalid Exotel Number"
msgstr "无效的Exotel号码"

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:137
msgid "Invalid access token provided for Facebook."
msgstr ""

#: crm/api/dashboard.py:77
msgid "Invalid chart name"
msgstr "图表名称无效"

#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.py:25
msgid "Invalid credentials"
msgstr "无效的凭据"

#: frontend/src/components/Settings/emailConfig.js:172
msgid "Invalid email ID"
msgstr "邮件地址格式无效"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:601
msgid "Invalid fields, check if all are filled in and values are correct."
msgstr "字段数据无效，请检查是否已填写所有必填项且数值正确。"

#: frontend/src/pages/InvalidPage.vue:6
msgid "Invalid page or not permitted to access"
msgstr "页面无效或无权访问"

#: frontend/src/composables/event.js:203
msgid "Invalid start or end time"
msgstr "开始时间或结束时间无效"

#: frontend/src/components/Settings/Users.vue:25
msgid "Invite New User"
msgstr "邀请新用户"

#: frontend/src/components/Settings/Settings.vue:136
msgid "Invite User"
msgstr "邀请用户"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:78
#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:149
msgid "Invite agent"
msgstr "邀请客服人员"

#: frontend/src/components/Settings/InviteUserPage.vue:51
msgid "Invite as"
msgstr "邀请身份"

#: frontend/src/components/Layouts/AppSidebar.vue:543
msgid "Invite users"
msgstr "邀请用户"

#: frontend/src/components/Settings/InviteUserPage.vue:10
msgid "Invite users to access CRM. Specify their roles to control access and permissions"
msgstr "邀请用户访问CRM系统。通过指定角色控制其访问权限。"

#: frontend/src/components/Layouts/AppSidebar.vue:359
msgid "Invite your team"
msgstr "邀请团队成员"

#. Label of the invited_by (Link) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Invited By"
msgstr "邀请人"

#: frontend/src/components/Filter.vue:273
#: frontend/src/components/Filter.vue:282
#: frontend/src/components/Filter.vue:294
#: frontend/src/components/Filter.vue:309
#: frontend/src/components/Filter.vue:322
#: frontend/src/components/Filter.vue:336
#: frontend/src/components/Filter.vue:345
msgid "Is"
msgstr "是"

#. Label of the is_default (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Default"
msgstr "设为默认"

#. Label of the is_erpnext_in_different_site (Check) field in DocType 'ERPNext
#. CRM Settings'
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
msgid "Is ERPNext installed on a different site?"
msgstr "ERPNext是否安装在其他站点？"

#. Label of the is_group (Check) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Is Group"
msgstr "是否分组"

#. Label of the is_helpdesk_in_different_site (Check) field in DocType
#. 'Helpdesk CRM Settings'
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Is Helpdesk installed on a different site?"
msgstr "服务台是否安装在其他站点？"

#. Label of the is_primary (Check) field in DocType 'CRM Contacts'
#. Label of the is_primary (Check) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Is Primary"
msgstr "是否主要"

#. Label of the is_standard (Check) field in DocType 'CRM Dropdown Item'
#. Label of the is_standard (Check) field in DocType 'CRM Form Script'
#. Label of the is_standard (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Is Standard"
msgstr "是否标准"

#. Description of the 'Enable Forecasting' (Check) field in DocType 'FCRM
#. Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "It will make deal's \"Expected Closure Date\" & \"Expected Deal Value\" mandatory to get accurate forecasting insights"
msgstr "系统将强制要求填写商机的“预期关闭日期”和“预期商机价值”，以获取准确的预测分析"

#. Label of the json (JSON) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "JSON"
msgstr "JSON"

#. Label of the job_title (Data) field in DocType 'CRM Deal'
#. Label of the job_title (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Job Title"
msgstr "职位"

#: frontend/src/components/AssignToBody.vue:11
#: frontend/src/components/Filter.vue:74 frontend/src/components/Filter.vue:107
#: frontend/src/components/Modals/AssignmentModal.vue:13
#: frontend/src/components/Modals/TaskModal.vue:63
#: frontend/src/components/Telephony/TaskPanel.vue:47
msgid "John Doe"
msgstr "张三"

#. Label of the kanban_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:384
#: frontend/src/components/ViewControls.vue:600 frontend/src/utils/view.js:20
msgid "Kanban"
msgstr "看板"

#. Label of the kanban_columns (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Columns"
msgstr "看板列"

#. Label of the kanban_fields (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Kanban Fields"
msgstr "看板字段"

#: frontend/src/components/Kanban/KanbanSettings.vue:3
#: frontend/src/components/Kanban/KanbanSettings.vue:8
msgid "Kanban Settings"
msgstr "看板设置"

#. Label of the key (Data) field in DocType 'CRM Invitation'
#. Label of the key (Data) field in DocType 'Facebook Lead Form Question'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Key"
msgstr "键"

#. Label of the label (Data) field in DocType 'CRM Dropdown Item'
#. Label of the label (Data) field in DocType 'CRM View Settings'
#. Label of the label (Data) field in DocType 'Facebook Lead Form Question'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: frontend/src/components/ColumnSettings.vue:100
msgid "Label"
msgstr "标签"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:108
msgid "Last"
msgstr "最近"

#: frontend/src/components/Filter.vue:615
msgid "Last 6 Months"
msgstr "过去6个月"

#: frontend/src/components/Filter.vue:607
msgid "Last Month"
msgstr "上月"

#. Label of the last_name (Data) field in DocType 'CRM Deal'
#. Label of the last_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Last Name"
msgstr "姓氏"

#: frontend/src/components/Filter.vue:611
msgid "Last Quarter"
msgstr "上季度"

#. Label of the last_status_change_log (Link) field in DocType 'CRM Status
#. Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Last Status Change Log"
msgstr "最后状态变更日志"

#. Label of the last_synced_at (Datetime) field in DocType 'Lead Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:102
msgid "Last Synced At"
msgstr "最后同步于"

#: frontend/src/components/Filter.vue:603
msgid "Last Week"
msgstr "上周"

#: frontend/src/components/Filter.vue:619
msgid "Last Year"
msgstr "去年"

#: frontend/src/pages/Contact.vue:533 frontend/src/pages/MobileContact.vue:533
#: frontend/src/pages/MobileOrganization.vue:477
#: frontend/src/pages/MobileOrganization.vue:505
#: frontend/src/pages/Organization.vue:507
#: frontend/src/pages/Organization.vue:535
msgid "Last modified"
msgstr "最后修改"

#: frontend/src/components/Settings/ProfileSettings.vue:83
msgid "Last name"
msgstr "姓氏"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:101
msgid "Last user assigned by this rule"
msgstr "本规则最后分配的用户"

#. Label of the layout (Code) field in DocType 'CRM Dashboard'
#. Label of the layout (Code) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Layout"
msgstr "布局"

#. Label of the lead (Link) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:263
#: frontend/src/components/Calendar/CalendarEventPanel.vue:288
#: frontend/src/components/Layouts/AppSidebar.vue:550
#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:19
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:55
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:77
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:55
#: frontend/src/components/Telephony/ExotelCallUI.vue:200
#: frontend/src/pages/Tasks.vue:133
msgid "Lead"
msgstr "线索"

#. Label of the lead_data (Code) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Lead Data"
msgstr ""

#. Label of the lead_details_tab (Tab Break) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Details"
msgstr "线索详情"

#. Label of the lead_name (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lead Name"
msgstr "线索名称"

#. Label of the lead_owner (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Lead Owner"
msgstr "线索负责人"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:120
msgid "Lead Owner cannot be same as the Lead Email Address"
msgstr "线索负责人不能与线索邮箱地址相同"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Sources"
msgstr "线索来源"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Lead Statuses"
msgstr "线索状态"

#. Name of a DocType
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Lead Sync Source"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:240
msgid "Lead Sync Source created successfully"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:183
msgid "Lead Sync Source deleted successfully"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:209
msgid "Lead Syncing"
msgstr ""

#: crm/api/dashboard.py:974
msgid "Lead generation channel analysis"
msgstr "线索生成渠道分析"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:7
msgid "Lead sources"
msgstr ""

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.js:8
msgid "Lead sync initiated."
msgstr ""

#: crm/api/dashboard.py:756
msgid "Lead to deal conversion pipeline"
msgstr "线索至商机转化管道"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/InvalidPage.vue:9 frontend/src/pages/Lead.vue:340
#: frontend/src/pages/MobileLead.vue:291
msgid "Leads"
msgstr "线索"

#: crm/api/dashboard.py:973
#: frontend/src/components/Dashboard/AddChartModal.vue:106
msgid "Leads by source"
msgstr "按来源统计的线索"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:158
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:238
msgid "Learn about conditions"
msgstr "了解条件设置"

#. Label of the lft (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Left"
msgstr "左侧"

#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:43
msgid "Library"
msgstr "媒体库"

#: frontend/src/components/Filter.vue:269
#: frontend/src/components/Filter.vue:280
#: frontend/src/components/Filter.vue:290
#: frontend/src/components/Filter.vue:318
#: frontend/src/components/Filter.vue:332
msgid "Like"
msgstr "相似"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:252
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:47
msgid "Link"
msgstr "链接"

#. Label of the links (Table) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Links"
msgstr "链接"

#. Option for the 'Apply To' (Select) field in DocType 'CRM Form Script'
#. Label of the list_tab (Tab Break) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/ViewControls.vue:374
#: frontend/src/components/ViewControls.vue:589 frontend/src/utils/view.js:12
msgid "List"
msgstr "列表"

#: frontend/src/components/Activities/CallArea.vue:75
msgid "Listen"
msgstr "收听"

#. Label of the load_default_columns (Check) field in DocType 'CRM View
#. Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Load Default Columns"
msgstr "加载默认列"

#: frontend/src/components/Kanban/KanbanView.vue:140
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:142
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:120
#: frontend/src/components/Settings/Users.vue:155
msgid "Load More"
msgstr "加载更多"

#: frontend/src/components/Activities/Activities.vue:22
#: frontend/src/components/Activities/DataFields.vue:35
#: frontend/src/pages/Deal.vue:176 frontend/src/pages/MobileDeal.vue:117
msgid "Loading..."
msgstr "加载中..."

#. Label of the log_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the log_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Log"
msgstr "日志"

#: frontend/src/components/Activities/Activities.vue:814
#: frontend/src/components/Activities/ActivityHeader.vue:133
#: frontend/src/components/Activities/ActivityHeader.vue:176
msgid "Log a Call"
msgstr "记录通话"

#: frontend/src/composables/frappecloud.js:23
msgid "Login to Frappe Cloud?"
msgstr "登录Frappe云平台？"

#. Label of the brand_logo (Attach) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Logo"
msgstr "徽标"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Lost"
msgstr "未成交"

#. Label of the lost_notes (Text) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Lost Notes"
msgstr "输单备注"

#. Label of the lost_reason (Link) field in DocType 'CRM Deal'
#. Label of the lost_reason (Data) field in DocType 'CRM Lost Reason'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
msgid "Lost Reason"
msgstr "未成交原因"

#: crm/api/dashboard.py:919
#: frontend/src/components/Dashboard/AddChartModal.vue:98
msgid "Lost deal reasons"
msgstr "输单原因分析"

#: frontend/src/components/Modals/LostReasonModal.vue:27
msgid "Lost notes"
msgstr "输单备注"

#: frontend/src/components/Modals/LostReasonModal.vue:83
msgid "Lost notes are required when lost reason is \"Other\""
msgstr "当输单原因为“其他”时，必须填写输单备注"

#: frontend/src/components/Modals/LostReasonModal.vue:4
#: frontend/src/components/Modals/LostReasonModal.vue:14
msgid "Lost reason"
msgstr "输单原因"

#: frontend/src/components/Modals/LostReasonModal.vue:79
msgid "Lost reason is required"
msgstr "必须填写输单原因"

#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Low"
msgstr "低"

#: frontend/src/pages/Contact.vue:100 frontend/src/pages/MobileContact.vue:73
msgid "Make Call"
msgstr "发起呼叫"

#: frontend/src/components/ViewControls.vue:1101
msgid "Make Private"
msgstr "设为私有"

#: frontend/src/components/ViewControls.vue:1101
msgid "Make Public"
msgstr "设为公开"

#: frontend/src/components/Activities/Activities.vue:818
#: frontend/src/components/Activities/ActivityHeader.vue:138
#: frontend/src/components/Activities/ActivityHeader.vue:181
#: frontend/src/pages/Deals.vue:505 frontend/src/pages/Leads.vue:532
msgid "Make a Call"
msgstr "拨打电话"

#: frontend/src/pages/Deal.vue:81 frontend/src/pages/Lead.vue:123
msgid "Make a call"
msgstr "发起呼叫"

#: frontend/src/components/Activities/AttachmentArea.vue:98
msgid "Make attachment {0}"
msgstr "将附件{0}"

#: frontend/src/components/Telephony/CallUI.vue:7
msgid "Make call"
msgstr "拨打电话"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make private"
msgstr "设为私有"

#: frontend/src/components/Activities/AttachmentArea.vue:43
msgid "Make public"
msgstr "设为公开"

#: frontend/src/components/Activities/AttachmentArea.vue:107
msgid "Make {0}"
msgstr "创建{0}"

#: frontend/src/components/Telephony/CallUI.vue:34
msgid "Make {0} as default calling medium"
msgstr "将{0}设为默认呼叫媒介"

#: frontend/src/components/Settings/ForecastingSettings.vue:24
msgid "Makes \"Expected Closure Date\" and \"Expected Deal Value\" mandatory for deal value forecasting"
msgstr "为进行商机价值预测，强制要求填写“预期关闭日期”和“预期商机价值”"

#: frontend/src/components/Settings/Users.vue:11
msgid "Manage CRM users by adding or inviting them, and assign roles to control their access and permissions"
msgstr "通过添加或邀请用户管理CRM系统用户，并通过分配角色控制其访问权限。"

#: frontend/src/components/Settings/EmailAccountList.vue:11
msgid "Manage your email accounts to send and receive emails directly from CRM. You can add multiple accounts and set one as default for incoming and outgoing emails."
msgstr "管理邮件账户以直接从CRM系统收发邮件。您可添加多个账户，并分别设置默认收件和发件账户。"

#: frontend/src/components/Modals/AddExistingUserModal.vue:93
#: frontend/src/components/Settings/InviteUserPage.vue:157
#: frontend/src/components/Settings/InviteUserPage.vue:164
#: frontend/src/components/Settings/Users.vue:87
#: frontend/src/components/Settings/Users.vue:193
#: frontend/src/components/Settings/Users.vue:253
#: frontend/src/components/Settings/Users.vue:256
msgid "Manager"
msgstr "经理"

#: frontend/src/data/document.js:57
msgid "Mandatory field error: {0}"
msgstr "必填字段错误：{0}"

#: frontend/src/data/document.js:187 frontend/src/data/document.js:190
msgid "Mandatory fields required: {0}"
msgstr "必须填写以下字段：{0}"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Manual"
msgstr "手动"

#. Label of the mapped_to_crm_field (Autocomplete) field in DocType 'Facebook
#. Lead Form Question'
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
msgid "Mapped to CRM Field"
msgstr ""

#: frontend/src/components/Notifications.vue:20
#: frontend/src/pages/MobileNotification.vue:12
#: frontend/src/pages/MobileNotification.vue:13
msgid "Mark all as read"
msgstr "全部标记为已读"

#: frontend/src/components/Layouts/AppSidebar.vue:547
msgid "Masters"
msgstr "主数据"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:209
#: frontend/src/components/Modals/EventModal.vue:86
msgid "May 1, 2025"
msgstr "2025年5月1日"

#. Label of the medium (Data) field in DocType 'CRM Call Log'
#. Option for the 'Priority' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Medium"
msgstr "媒介"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Mention"
msgstr "@提及"

#. Label of the message (HTML Editor) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Message"
msgstr "消息"

#. Label of the middle_name (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Middle Name"
msgstr "中间名"

#: frontend/src/components/Telephony/ExotelCallUI.vue:127
msgid "Minimize"
msgstr "最小化"

#. Label of the mobile_no (Data) field in DocType 'CRM Contacts'
#. Label of the mobile_no (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Mobile No"
msgstr "手机号码"

#: frontend/src/components/Modals/DealModal.vue:208
#: frontend/src/components/Modals/LeadModal.vue:149
msgid "Mobile No should be a number"
msgstr "手机号码应为数字"

#. Label of the mobile_no (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Mobile No."
msgstr "手机号"

#: frontend/src/components/Telephony/CallUI.vue:22
msgid "Mobile Number"
msgstr "手机号码"

#: crm/integrations/exotel/handler.py:93
msgid "Mobile Number Missing"
msgstr "手机号码缺失"

#: frontend/src/components/Layouts/AppSidebar.vue:611
msgid "Mobile app installation"
msgstr "移动端应用安装"

#: frontend/src/pages/Contact.vue:523 frontend/src/pages/MobileContact.vue:523
#: frontend/src/pages/MobileOrganization.vue:467
#: frontend/src/pages/Organization.vue:497
msgid "Mobile no"
msgstr "手机号"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Monday"
msgstr "周一"

#: crm/api/dashboard.py:691
msgid "Month"
msgstr "月"

#. Option for the 'Background Sync Frequency' (Select) field in DocType 'Lead
#. Sync Source'
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Monthly"
msgstr "每月"

#: frontend/src/components/ViewControls.vue:221
msgid "More Options"
msgstr "更多选项"

#: frontend/src/components/FieldLayoutEditor.vue:448
msgid "Move to next section"
msgstr "跳转至下一区块"

#: frontend/src/components/FieldLayoutEditor.vue:401
msgid "Move to next tab"
msgstr "切换至下一标签页"

#: frontend/src/components/FieldLayoutEditor.vue:458
msgid "Move to previous section"
msgstr "返回上一区块"

#: frontend/src/components/FieldLayoutEditor.vue:387
msgid "Move to previous tab"
msgstr "返回上一标签页"

#: frontend/src/components/Modals/ViewModal.vue:29
msgid "My Open Deals"
msgstr "我负责的开放商机"

#. Label of the title (Data) field in DocType 'CRM Dashboard'
#. Label of the name1 (Data) field in DocType 'CRM Dropdown Item'
#. Label of the brand_name (Data) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:51
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:52
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:39
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:39
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:59
#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:22
#: frontend/src/components/ViewControls.vue:779
#: frontend/src/pages/MobileOrganization.vue:485
#: frontend/src/pages/Organization.vue:515
msgid "Name"
msgstr "名称"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:417
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:151
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:154
msgid "Name is required"
msgstr "必须填写名称"

#. Label of the naming_series (Select) field in DocType 'CRM Deal'
#. Label of the naming_series (Select) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Naming Series"
msgstr "命名规则"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:99
msgid "Nested conditions"
msgstr "嵌套条件"

#. Label of the net_amount (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Net Amount"
msgstr "净额"

#. Label of the net_total (Currency) field in DocType 'CRM Deal'
#. Label of the net_total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Net Total"
msgstr "净总计"

#: frontend/src/components/Activities/ActivityHeader.vue:76
#: frontend/src/components/Settings/AssignmentRules/AssignmentRules.vue:19
#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:19
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:20
#: frontend/src/components/Settings/Users.vue:30
msgid "New"
msgstr "新建"

#: frontend/src/components/Modals/AddressModal.vue:93
msgid "New Address"
msgstr "新建地址"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:12
msgid "New Assignment Rule"
msgstr "新建分配规则"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleListItem.vue:44
msgid "New Assignment Rule Name"
msgstr "新分配规则名称"

#: frontend/src/components/Modals/CallLogModal.vue:97
msgid "New Call Log"
msgstr "新建通话记录"

#: frontend/src/components/Activities/Activities.vue:398
#: frontend/src/components/Activities/ActivityHeader.vue:19
#: frontend/src/components/Activities/ActivityHeader.vue:123
msgid "New Comment"
msgstr "新建评论"

#: frontend/src/components/Modals/ContactModal.vue:8
msgid "New Contact"
msgstr "新建联系人"

#: frontend/src/components/Activities/Activities.vue:393
#: frontend/src/components/Activities/ActivityHeader.vue:12
#: frontend/src/components/Activities/ActivityHeader.vue:118
msgid "New Email"
msgstr "新建邮件"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:9
msgid "New Lead Sync Source"
msgstr ""

#: frontend/src/components/Activities/ActivityHeader.vue:66
msgid "New Message"
msgstr "新建消息"

#: frontend/src/components/Activities/ActivityHeader.vue:41
#: frontend/src/components/Activities/ActivityHeader.vue:144
#: frontend/src/pages/Deals.vue:511 frontend/src/pages/Leads.vue:538
msgid "New Note"
msgstr "新建备注"

#: frontend/src/components/Modals/OrganizationModal.vue:8
msgid "New Organization"
msgstr "新建组织"

#: frontend/src/components/Modals/ChangePasswordModal.vue:6
msgid "New Password"
msgstr "新密码"

#: frontend/src/components/FieldLayoutEditor.vue:201
#: frontend/src/components/SidePanelLayoutEditor.vue:131
msgid "New Section"
msgstr "新建区块"

#: frontend/src/components/FieldLayoutEditor.vue:293
#: frontend/src/components/FieldLayoutEditor.vue:298
msgid "New Tab"
msgstr "新建标签页"

#: frontend/src/components/Activities/ActivityHeader.vue:48
#: frontend/src/components/Activities/ActivityHeader.vue:149
#: frontend/src/pages/Deals.vue:516 frontend/src/pages/Leads.vue:543
msgid "New Task"
msgstr "新建任务"

#: frontend/src/components/Activities/ActivityHeader.vue:159
msgid "New WhatsApp Message"
msgstr "新建WhatsApp消息"

#: frontend/src/components/Modals/ConvertToDealModal.vue:67
#: frontend/src/pages/MobileLead.vue:162
msgid "New contact will be created based on the person's details"
msgstr "将根据个人信息新建联系人"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:431
msgid "New event"
msgstr "新建事件"

#: frontend/src/components/Modals/ConvertToDealModal.vue:42
#: frontend/src/pages/MobileLead.vue:136
msgid "New organization will be created based on the data in details section"
msgstr "将根据详情数据新建组织"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:10
msgid "New template"
msgstr "新建模板"

#: frontend/src/components/Modals/CreateDocumentModal.vue:88
msgid "New {0}"
msgstr "新建 {0}"

#: frontend/src/components/Filter.vue:663
msgid "Next 6 Months"
msgstr "未来6个月"

#: frontend/src/components/Filter.vue:655
msgid "Next Month"
msgstr "下月"

#: frontend/src/components/Filter.vue:659
msgid "Next Quarter"
msgstr "下季度"

#. Label of the next_step (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Next Step"
msgstr "下一步"

#: frontend/src/components/Filter.vue:651
msgid "Next Week"
msgstr "下周"

#: frontend/src/components/Filter.vue:667
msgid "Next Year"
msgstr "明年"

#: frontend/src/components/Controls/Grid.vue:27
msgid "No"
msgstr "否"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "No Answer"
msgstr "无应答"

#: frontend/src/components/Controls/Grid.vue:322
msgid "No Data"
msgstr "无数据"

#: frontend/src/components/Activities/EventArea.vue:78
msgid "No Events Scheduled"
msgstr "未安排任何事件"

#: frontend/src/components/Kanban/KanbanView.vue:103
#: frontend/src/pages/Deals.vue:105 frontend/src/pages/Leads.vue:121
#: frontend/src/pages/Tasks.vue:71
msgid "No Title"
msgstr "无标题"

#: frontend/src/components/Settings/EmailEdit.vue:150
msgid "No changes made"
msgstr "未作更改"

#: frontend/src/components/Modals/SidePanelModal.vue:51
#: frontend/src/pages/Deal.vue:271 frontend/src/pages/MobileDeal.vue:205
msgid "No contacts added"
msgstr "未添加联系人"

#: frontend/src/pages/Deal.vue:257
msgid "No details added"
msgstr ""

#: frontend/src/pages/Deal.vue:90 frontend/src/pages/Lead.vue:137
msgid "No email set"
msgstr "未设置邮箱"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:48
msgid "No email templates found"
msgstr "未找到邮件模板"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:14
msgid "No items in the list"
msgstr "列表中无项目"

#: frontend/src/components/FieldLayoutEditor.vue:92
msgid "No label"
msgstr "无标签"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:49
msgid "No lead sources found"
msgstr ""

#: frontend/src/pages/Deal.vue:707
msgid "No mobile number set"
msgstr "未设置手机号"

#: frontend/src/components/Notifications.vue:77
#: frontend/src/pages/MobileNotification.vue:61
msgid "No new notifications"
msgstr "无新通知"

#: frontend/src/pages/Lead.vue:129
msgid "No phone number set"
msgstr "未设置电话号码"

#: frontend/src/pages/Deal.vue:702
msgid "No primary contact set"
msgstr "未设置主要联系人"

#: frontend/src/components/Calendar/Attendee.vue:232
#: frontend/src/components/Controls/EmailMultiSelect.vue:133
#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:70
msgid "No results found"
msgstr "未找到匹配结果"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:66
#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:42
msgid "No templates found"
msgstr "未找到模板"

#: frontend/src/components/Modals/AddExistingUserModal.vue:39
#: frontend/src/components/Settings/Users.vue:57
msgid "No users found"
msgstr "未找到用户"

#: frontend/src/pages/MobileOrganization.vue:281
#: frontend/src/pages/Organization.vue:318
msgid "No website found"
msgstr "未找到网站"

#: frontend/src/pages/Deal.vue:100 frontend/src/pages/Lead.vue:146
msgid "No website set"
msgstr "未设置网站"

#: frontend/src/components/PrimaryDropdown.vue:33
msgid "No {0} Available"
msgstr "无可用{0}"

#: frontend/src/pages/CallLogs.vue:59 frontend/src/pages/Contact.vue:162
#: frontend/src/pages/Contacts.vue:58 frontend/src/pages/Deals.vue:234
#: frontend/src/pages/Leads.vue:260 frontend/src/pages/MobileContact.vue:147
#: frontend/src/pages/MobileOrganization.vue:139
#: frontend/src/pages/Notes.vue:95 frontend/src/pages/Organization.vue:158
#: frontend/src/pages/Organizations.vue:58 frontend/src/pages/Tasks.vue:184
msgid "No {0} Found"
msgstr "未找到{0}"

#. Label of the no_of_employees (Select) field in DocType 'CRM Deal'
#. Label of the no_of_employees (Select) field in DocType 'CRM Lead'
#. Label of the no_of_employees (Select) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "No. of Employees"
msgstr "员工数量"

#: frontend/src/components/Activities/AudioPlayer.vue:148
msgid "Normal"
msgstr "普通"

#: crm/utils/__init__.py:263
msgid "Not Allowed"
msgstr "不允许"

#: frontend/src/components/Filter.vue:268
#: frontend/src/components/Filter.vue:289
#: frontend/src/components/Filter.vue:306
#: frontend/src/components/Filter.vue:317
#: frontend/src/components/Filter.vue:344
msgid "Not Equals"
msgstr "不等于"

#: frontend/src/components/Filter.vue:272
#: frontend/src/components/Filter.vue:293
#: frontend/src/components/Filter.vue:308
#: frontend/src/components/Filter.vue:321
#: frontend/src/components/Filter.vue:335
msgid "Not In"
msgstr "不在"

#: frontend/src/components/Filter.vue:270
#: frontend/src/components/Filter.vue:281
#: frontend/src/components/Filter.vue:291
#: frontend/src/components/Filter.vue:319
#: frontend/src/components/Filter.vue:333
msgid "Not Like"
msgstr "不包含"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:10
#: frontend/src/components/Controls/GridRowFieldsModal.vue:10
#: frontend/src/components/Modals/DataFieldsModal.vue:10
#: frontend/src/components/Modals/QuickEntryModal.vue:10
#: frontend/src/components/Modals/SidePanelModal.vue:10
#: frontend/src/components/Settings/SettingsPage.vue:11
#: frontend/src/components/Settings/TelephonySettings.vue:11
msgid "Not Saved"
msgstr "未保存"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:133
msgid "Not Synced"
msgstr ""

#: crm/fcrm/doctype/crm_deal/crm_deal.py:272
msgid "Not allowed to add contact to Deal"
msgstr "无权向商机添加联系人"

#: crm/fcrm/doctype/crm_lead/crm_lead.py:455
msgid "Not allowed to convert Lead to Deal"
msgstr "无权将线索转为商机"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:283
msgid "Not allowed to remove contact from Deal"
msgstr "无权从商机移除联系人"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:294
msgid "Not allowed to set primary contact for Deal"
msgstr "无权设置商机主要联系人"

#. Label of the note (Link) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: frontend/src/components/Layouts/AppSidebar.vue:554
msgid "Note"
msgstr "备注"

#: frontend/src/pages/Deal.vue:572 frontend/src/pages/Lead.vue:419
#: frontend/src/pages/MobileDeal.vue:461 frontend/src/pages/MobileLead.vue:367
msgid "Notes"
msgstr "备注"

#: frontend/src/pages/Notes.vue:23
msgid "Notes View"
msgstr "备注视图"

#: frontend/src/components/Activities/EmailArea.vue:15
#: frontend/src/components/Layouts/AppSidebar.vue:583
msgid "Notification"
msgstr "通知"

#. Label of the notification_text (Text) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Text"
msgstr "通知内容"

#. Label of the notification_type_doc (Dynamic Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doc"
msgstr "通知类型文档"

#. Label of the notification_type_doctype (Link) field in DocType 'CRM
#. Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Notification Type Doctype"
msgstr "通知类型文档类型"

#: frontend/src/components/Layouts/AppSidebar.vue:13
#: frontend/src/components/Mobile/MobileSidebar.vue:23
#: frontend/src/components/Notifications.vue:17
#: frontend/src/pages/MobileNotification.vue:6
msgid "Notifications"
msgstr "通知"

#. Label of the number (Data) field in DocType 'CRM Telephony Phone'
#: crm/fcrm/doctype/crm_telephony_phone/crm_telephony_phone.json
msgid "Number"
msgstr "编号"

#: frontend/src/components/Dashboard/AddChartModal.vue:19
#: frontend/src/components/Dashboard/AddChartModal.vue:69
msgid "Number chart"
msgstr "数字图表"

#: crm/api/dashboard.py:1072 crm/api/dashboard.py:1132
msgid "Number of deals"
msgstr "商机数量"

#: crm/api/dashboard.py:1125
msgid "Number of deals and total value per salesperson"
msgstr "各销售人员的商机数量与总价值"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:167
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:251
msgid "Old Condition"
msgstr "原有条件"

#. Label of the old_parent (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Old Parent"
msgstr "原上级"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "On Hold"
msgstr "临时冻结"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Ongoing"
msgstr "进行中"

#: crm/api/dashboard.py:190
#: frontend/src/components/Dashboard/AddChartModal.vue:77
msgid "Ongoing deals"
msgstr "进行中商机"

#: frontend/src/utils/index.js:474
msgid "Only image files are allowed"
msgstr "仅允许上传图像文件"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:60
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.py:23
msgid "Only one {0} can be set as primary."
msgstr "仅可设置一个主要{0}"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Open"
msgstr "待处理"

#: frontend/src/components/Modals/NoteModal.vue:13
#: frontend/src/components/Modals/TaskModal.vue:13
msgid "Open Deal"
msgstr "开放商机"

#: frontend/src/components/Modals/NoteModal.vue:14
#: frontend/src/components/Modals/TaskModal.vue:14
msgid "Open Lead"
msgstr "开放线索"

#: crm/fcrm/doctype/crm_deal/crm_deal.js:6
#: crm/fcrm/doctype/crm_lead/crm_lead.js:6
msgid "Open in Portal"
msgstr "在门户中打开"

#. Label of the open_in_new_window (Check) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Open in new window"
msgstr "新窗口打开"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:88
msgid "Open nested conditions"
msgstr "展开嵌套条件"

#: frontend/src/pages/Organization.vue:96
msgid "Open website"
msgstr "打开网站"

#: frontend/src/components/Kanban/KanbanView.vue:218
#: frontend/src/components/Modals/CallLogDetailModal.vue:15
#: frontend/src/components/ViewControls.vue:199
msgid "Options"
msgstr "选项"

#: frontend/src/pages/Welcome.vue:40
msgid "Or create leads manually"
msgstr "或手动创建线索"

#. Label of the order_by_tab (Tab Break) field in DocType 'CRM View Settings'
#. Label of the order_by (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Order By"
msgstr "排序依据"

#. Label of the organization (Link) field in DocType 'CRM Deal'
#. Label of the organization_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the organization (Data) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: frontend/src/components/Layouts/AppSidebar.vue:553
#: frontend/src/components/Modals/ConvertToDealModal.vue:25
#: frontend/src/pages/Contact.vue:502 frontend/src/pages/MobileContact.vue:502
#: frontend/src/pages/MobileLead.vue:118
#: frontend/src/pages/MobileOrganization.vue:446
#: frontend/src/pages/MobileOrganization.vue:500
#: frontend/src/pages/Organization.vue:476
#: frontend/src/pages/Organization.vue:530
msgid "Organization"
msgstr "组织"

#. Label of the organization_details_section (Section Break) field in DocType
#. 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Organization Details"
msgstr "组织详情"

#. Label of the organization_logo (Attach Image) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Logo"
msgstr "组织徽标"

#. Label of the organization_name (Data) field in DocType 'CRM Deal'
#. Label of the organization_name (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Organization Name"
msgstr "组织名称"

#: frontend/src/pages/Deal.vue:62
msgid "Organization logo"
msgstr "组织徽标"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
#: frontend/src/pages/MobileOrganization.vue:205
#: frontend/src/pages/Organization.vue:243
msgid "Organizations"
msgstr "组织"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:107
msgid "Organizer"
msgstr "组织者"

#: frontend/src/components/Layouts/AppSidebar.vue:575
msgid "Other features"
msgstr "其他功能"

#. Label of the organization_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Others"
msgstr "其他"

#: frontend/src/components/Activities/CallArea.vue:37
msgid "Outbound Call"
msgstr "呼出通话"

#. Option for the 'Type' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Outgoing"
msgstr "呼出"

#. Label of the log_owner (Link) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "Owner"
msgstr "负责人"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:102
msgid "Owner: {0}"
msgstr "负责人：{0}"

#. Label of the page (Link) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Page"
msgstr "页面"

#. Label of the page_name (Data) field in DocType 'Facebook Page'
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
msgid "Page Name"
msgstr "页面名称"

#. Label of the parent_crm_territory (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Parent CRM Territory"
msgstr "上级CRM区域"

#: frontend/src/components/Settings/emailConfig.js:64
msgid "Password"
msgstr "密码"

#: crm/api/demo.py:21 crm/api/demo.py:29
msgid "Password cannot be reset by Demo User {}"
msgstr "演示用户{}无法重置密码"

#: frontend/src/components/Settings/emailConfig.js:175
msgid "Password is required"
msgstr "必须填写密码"

#: frontend/src/components/Modals/ChangePasswordModal.vue:88
msgid "Password updated successfully"
msgstr "密码更新成功"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:13
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:38
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:38
msgid "Payment Reminder"
msgstr "付款提醒"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:69
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:69
msgid "Payment Reminder from Frappé - (#{{ name }})"
msgstr "Frappé付款提醒 - (#{{ name }})"

#. Option for the 'Status' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
msgid "Pending"
msgstr "待处理"

#: frontend/src/components/Settings/InviteUserPage.vue:61
msgid "Pending Invites"
msgstr "待处理邀请"

#: frontend/src/pages/Dashboard.vue:66
msgid "Period"
msgstr "期间"

#. Label of the person_section (Section Break) field in DocType 'CRM Deal'
#. Label of the person_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Person"
msgstr "个人"

#: frontend/src/components/Settings/Settings.vue:90
msgid "Personal Settings"
msgstr "个人设置"

#. Label of the phone (Data) field in DocType 'CRM Contacts'
#. Label of the phone (Data) field in DocType 'CRM Lead'
#. Option for the 'Device' (Select) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_contacts/crm_contacts.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/pages/MobileOrganization.vue:495
#: frontend/src/pages/Organization.vue:525
msgid "Phone"
msgstr "电话"

#. Label of the phone_nos (Table) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Phone Numbers"
msgstr "电话号码"

#: frontend/src/components/ViewControls.vue:1093
msgid "Pin View"
msgstr "固定视图"

#. Label of the pinned (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Pinned"
msgstr "已固定"

#: frontend/src/components/ViewControls.vue:677
msgid "Pinned Views"
msgstr "固定视图"

#: frontend/src/components/Layouts/AppSidebar.vue:571
msgid "Pinned view"
msgstr "置顶视图"

#: frontend/src/components/Activities/AudioPlayer.vue:176
msgid "Playback speed"
msgstr "播放速度"

#: frontend/src/components/Settings/EmailAccountList.vue:49
msgid "Please add an email account to continue."
msgstr "请添加邮件账户以继续操作"

#: crm/lead_syncing/doctype/lead_sync_source/facebook.py:157
msgid "Please check your access token"
msgstr ""

#: crm/integrations/twilio/twilio_handler.py:119
msgid "Please enable twilio settings before making a call."
msgstr "请先启用Twilio设置"

#: frontend/src/components/FilesUploader/FilesUploader.vue:165
msgid "Please enter a valid URL"
msgstr "请输入有效URL"

#: frontend/src/components/Settings/CurrencySettings.vue:150
msgid "Please enter the Exchangerate Host access key."
msgstr "请输入汇率服务商访问密钥"

#: frontend/src/components/Modals/LostReasonModal.vue:9
msgid "Please provide a reason for marking this deal as lost"
msgstr "请提供标记本商机为输单的原因"

#: frontend/src/components/Settings/CurrencySettings.vue:143
msgid "Please select a currency before saving."
msgstr "保存前请选择货币"

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.py:65
msgid "Please select a lead gen form before syncing!"
msgstr ""

#: frontend/src/components/Modals/ConvertToDealModal.vue:136
#: frontend/src/pages/MobileLead.vue:431
msgid "Please select an existing contact"
msgstr "请选择现有联系人"

#: frontend/src/components/Modals/ConvertToDealModal.vue:141
#: frontend/src/pages/MobileLead.vue:436
msgid "Please select an existing organization"
msgstr "请选择现有组织"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:126
msgid "Please set Email Address"
msgstr "请设置电子邮件地址"

#: crm/integrations/exotel/handler.py:73
msgid "Please setup Exotel intergration"
msgstr "请配置Exotel集成"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:181
msgid "Please specify a reason for losing the deal."
msgstr "请具体说明输单原因"

#: crm/fcrm/doctype/crm_deal/crm_deal.py:183
msgid "Please specify the reason for losing the deal."
msgstr "请具体说明输单原因"

#. Label of the position (Int) field in DocType 'CRM Deal Status'
#. Label of the position (Int) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "Position"
msgstr "位置"

#: frontend/src/pages/Deal.vue:205 frontend/src/pages/MobileDeal.vue:149
msgid "Primary"
msgstr "主要"

#. Label of the email (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Email"
msgstr "主要邮箱"

#. Label of the mobile_no (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Mobile No"
msgstr "主要手机号码"

#. Label of the phone (Data) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "Primary Phone"
msgstr "主要电话"

#: frontend/src/pages/Deal.vue:679 frontend/src/pages/MobileDeal.vue:566
msgid "Primary contact set"
msgstr "已设置主要联系人"

#. Label of the priorities (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Priorities"
msgstr "优先级"

#. Label of the priority (Link) field in DocType 'CRM Service Level Priority'
#. Label of the priority (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_priority/crm_service_level_priority.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:64
#: frontend/src/components/Settings/AssignmentRules/AssignmentRulesList.vue:20
msgid "Priority"
msgstr "优先级"

#. Label of the private (Check) field in DocType 'CRM Dashboard'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: frontend/src/components/FilesUploader/FilesUploaderArea.vue:89
msgid "Private"
msgstr "私有"

#. Label of the probability (Percent) field in DocType 'CRM Deal'
#. Label of the probability (Percent) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Probability"
msgstr "成交概率"

#. Label of the product_code (Link) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product"
msgstr "产品"

#. Label of the product_code (Data) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Product Code"
msgstr "产品编码"

#. Label of the product_name (Data) field in DocType 'CRM Product'
#. Label of the product_name (Data) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Product Name"
msgstr "产品名称"

#. Label of the products_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the products (Table) field in DocType 'CRM Deal'
#. Label of the products_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the products (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Products"
msgstr "产品"

#: frontend/src/components/Layouts/AppSidebar.vue:540
#: frontend/src/components/Settings/Settings.vue:94
msgid "Profile"
msgstr "个人资料"

#: frontend/src/components/Settings/ProfileSettings.vue:147
msgid "Profile updated successfully"
msgstr "个人资料更新成功"

#: crm/api/dashboard.py:689
msgid "Projected vs actual revenue based on deal probability"
msgstr "基于商机概率的预测收入与实际收入对比"

#. Label of the public (Check) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Public"
msgstr "公开"

#: frontend/src/components/ViewControls.vue:672
msgid "Public Views"
msgstr "公开视图"

#: frontend/src/components/Layouts/AppSidebar.vue:570
msgid "Public view"
msgstr "公开视图"

#. Label of the qty (Float) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Quantity"
msgstr "数量"

#. Label of the questions (Table) field in DocType 'Facebook Lead Form'
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
msgid "Questions"
msgstr "问题"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Queued"
msgstr "已排队"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Quick Entry"
msgstr "快速录入"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Quick Filters"
msgstr "快速筛选"

#: frontend/src/components/ViewControls.vue:731
msgid "Quick Filters updated successfully"
msgstr "快速筛选更新成功"

#: frontend/src/components/Layouts/AppSidebar.vue:594
msgid "Quick entry layout"
msgstr "快速录入布局"

#. Label of the rate (Currency) field in DocType 'CRM Products'
#: crm/fcrm/doctype/crm_products/crm_products.json
msgid "Rate"
msgstr "单价"

#. Label of the read (Check) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Read"
msgstr "已读"

#: crm/api/dashboard.py:922
msgid "Reason"
msgstr "原因"

#. Label of the record_calls (Check) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "Record Calls"
msgstr "录制通话"

#. Label of the record_call (Check) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Record Outgoing Calls"
msgstr "录制呼出通话"

#. Label of the recording_url (Data) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Recording URL"
msgstr "录音URL"

#. Label of the reference_name (Dynamic Link) field in DocType 'CRM
#. Notification'
#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Task'
#. Label of the reference_docname (Dynamic Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Doc"
msgstr "关联文档"

#. Label of the reference_doctype (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "Reference Doctype"
msgstr "关联文档类型"

#. Label of the reference_doctype (Link) field in DocType 'CRM Call Log'
#. Label of the reference_doctype (Link) field in DocType 'CRM Task'
#. Label of the reference_doctype (Link) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
msgid "Reference Document Type"
msgstr "关联文档类型"

#. Label of the reference_docname (Dynamic Link) field in DocType 'CRM Call
#. Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Reference Name"
msgstr "关联名称"

#: frontend/src/components/ViewControls.vue:26
#: frontend/src/components/ViewControls.vue:159
#: frontend/src/pages/Dashboard.vue:10
msgid "Refresh"
msgstr "刷新"

#: frontend/src/components/Telephony/TwilioCallUI.vue:97
msgid "Reject"
msgstr "拒绝"

#: frontend/src/components/Telephony/TwilioCallUI.vue:163
msgid "Reject call"
msgstr "拒接来电"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:192
#: frontend/src/components/Controls/ImageUploader.vue:25
#: frontend/src/components/Settings/Users.vue:218
#: frontend/src/pages/Deal.vue:628
msgid "Remove"
msgstr "移除"

#: frontend/src/components/FilesUploader/FilesUploader.vue:23
msgid "Remove all"
msgstr "全部移除"

#: frontend/src/components/FieldLayoutEditor.vue:438
msgid "Remove and move fields to previous column"
msgstr "移除并将字段移至前一列"

#: frontend/src/components/FieldLayoutEditor.vue:432
msgid "Remove column"
msgstr "移除列"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:200
msgid "Remove group"
msgstr "移除分组"

#: frontend/src/components/Settings/ProfileSettings.vue:32
#: frontend/src/pages/Contact.vue:53 frontend/src/pages/Lead.vue:94
#: frontend/src/pages/MobileContact.vue:43
#: frontend/src/pages/MobileOrganization.vue:43
#: frontend/src/pages/Organization.vue:53
msgid "Remove image"
msgstr "移除图片"

#: frontend/src/components/FieldLayoutEditor.vue:359
msgid "Remove section"
msgstr "移除区块"

#: frontend/src/components/FieldLayoutEditor.vue:318
msgid "Remove tab"
msgstr "移除标签页"

#: frontend/src/components/Activities/EmailArea.vue:34
#: frontend/src/components/CommunicationArea.vue:10
msgid "Reply"
msgstr "回复"

#: frontend/src/components/Activities/EmailArea.vue:41
msgid "Reply All"
msgstr "回复全部"

#: frontend/src/components/Modals/AboutModal.vue:72
msgid "Report an Issue"
msgstr "提交一个问题"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Required Fields"
msgstr "必填字段"

#: frontend/src/components/Controls/GridFieldsEditorModal.vue:79
#: frontend/src/components/Controls/GridRowFieldsModal.vue:30
#: frontend/src/components/Modals/DataFieldsModal.vue:30
#: frontend/src/components/Modals/QuickEntryModal.vue:30
#: frontend/src/components/Modals/SidePanelModal.vue:30
msgid "Reset"
msgstr "重置"

#: frontend/src/components/ColumnSettings.vue:78
msgid "Reset Changes"
msgstr "撤销修改"

#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.js:7
msgid "Reset ERPNext Form Script"
msgstr "重置ERPNext表单脚本"

#: frontend/src/components/ColumnSettings.vue:86
msgid "Reset to Default"
msgstr "恢复默认"

#: frontend/src/pages/Dashboard.vue:28
msgid "Reset to default"
msgstr "恢复默认设置"

#. Label of the response_by (Datetime) field in DocType 'CRM Deal'
#. Label of the response_by (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response By"
msgstr "响应人"

#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Deal'
#. Label of the response_details_section (Section Break) field in DocType 'CRM
#. Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Response Details"
msgstr "响应详情"

#. Label of the section_break_ufaf (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Response and Follow Up"
msgstr "响应与跟进"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:14
msgid "Restore"
msgstr "恢复"

#. Label of the restore_defaults (Button) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:13
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Restore Defaults"
msgstr "恢复默认值"

#: frontend/src/components/FilesUploader/FilesUploader.vue:51
msgid "Retake"
msgstr "重新拍摄"

#: crm/api/dashboard.py:697
msgid "Revenue"
msgstr "收入"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:81
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:81
msgid "Rich Text"
msgstr "富文本"

#. Label of the rgt (Int) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Right"
msgstr "右侧"

#. Option for the 'Status' (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Ringing"
msgstr "响铃中"

#: frontend/src/components/Telephony/TwilioCallUI.vue:38
#: frontend/src/components/Telephony/TwilioCallUI.vue:140
msgid "Ringing..."
msgstr "正在响铃..."

#. Label of the role (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:46
msgid "Role"
msgstr "角色"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#. Label of the route (Data) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Route"
msgstr "路由"

#. Label of the route_name (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Route Name"
msgstr "路由名称"

#. Label of the rows (Code) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "Rows"
msgstr "行"

#. Label of the sla_tab (Tab Break) field in DocType 'CRM Deal'
#. Label of the sla (Link) field in DocType 'CRM Deal'
#. Label of the sla_tab (Tab Break) field in DocType 'CRM Lead'
#. Label of the sla (Link) field in DocType 'CRM Lead'
#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "SLA"
msgstr "服务级别协议"

#. Label of the sla_creation (Datetime) field in DocType 'CRM Deal'
#. Label of the sla_creation (Datetime) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Creation"
msgstr "SLA创建"

#. Label of the sla_name (Data) field in DocType 'CRM Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "SLA Name"
msgstr "SLA名称"

#. Label of the sla_status (Select) field in DocType 'CRM Deal'
#. Label of the sla_status (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "SLA Status"
msgstr "SLA状态"

#: frontend/src/components/EmailEditor.vue:85
msgid "SUBJECT"
msgstr "主题"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
msgid "Sales Manager"
msgstr "销售经理"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_industry/crm_industry.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/crm_territory/crm_territory.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/AddExistingUserModal.vue:92
#: frontend/src/components/Settings/InviteUserPage.vue:156
#: frontend/src/components/Settings/InviteUserPage.vue:163
#: frontend/src/components/Settings/Users.vue:88
#: frontend/src/components/Settings/Users.vue:194
#: frontend/src/components/Settings/Users.vue:264
#: frontend/src/components/Settings/Users.vue:267
msgid "Sales User"
msgstr "销售人员"

#: crm/api/dashboard.py:617
#: frontend/src/components/Dashboard/AddChartModal.vue:94
msgid "Sales trend"
msgstr "销售趋势"

#: frontend/src/pages/Dashboard.vue:93
msgid "Sales user"
msgstr "销售人员"

#: crm/api/dashboard.py:1127
msgid "Salesperson"
msgstr "销售人员"

#. Label of the salutation (Link) field in DocType 'CRM Deal'
#. Label of the salutation (Link) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Salutation"
msgstr "称呼"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Saturday"
msgstr "周六"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:345
#: frontend/src/components/Controls/GridFieldsEditorModal.vue:84
#: frontend/src/components/Controls/GridRowFieldsModal.vue:26
#: frontend/src/components/Modals/AddressModal.vue:98
#: frontend/src/components/Modals/CallLogModal.vue:101
#: frontend/src/components/Modals/DataFieldsModal.vue:26
#: frontend/src/components/Modals/LostReasonModal.vue:44
#: frontend/src/components/Modals/QuickEntryModal.vue:26
#: frontend/src/components/Modals/SidePanelModal.vue:26
#: frontend/src/components/PrimaryDropdownItem.vue:21
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:36
#: frontend/src/components/Settings/CurrencySettings.vue:173
#: frontend/src/components/Telephony/ExotelCallUI.vue:222
#: frontend/src/components/ViewControls.vue:125
#: frontend/src/pages/Dashboard.vue:36
msgid "Save"
msgstr "保存"

#: frontend/src/components/Modals/ViewModal.vue:40
#: frontend/src/components/ViewControls.vue:58
#: frontend/src/components/ViewControls.vue:155
msgid "Save Changes"
msgstr "保存更改"

#: frontend/src/components/ViewControls.vue:667
msgid "Saved Views"
msgstr "已保存视图"

#: frontend/src/components/Layouts/AppSidebar.vue:569
msgid "Saved view"
msgstr "已保存视图"

#: frontend/src/components/Telephony/TaskPanel.vue:8
msgid "Schedule a task..."
msgstr "安排任务..."

#: frontend/src/components/Activities/EventArea.vue:79
msgid "Schedule an Event"
msgstr "安排事件"

#: frontend/src/components/Activities/ActivityHeader.vue:36
#: frontend/src/components/Activities/ActivityHeader.vue:128
msgid "Schedule an event"
msgstr "安排事件"

#. Label of the script (Code) field in DocType 'CRM Form Script'
#: crm/fcrm/doctype/crm_form_script/crm_form_script.json
msgid "Script"
msgstr "脚本"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:22
msgid "Search"
msgstr "搜索"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:64
msgid "Search template"
msgstr "搜索模板"

#: frontend/src/components/Settings/Users.vue:73
msgid "Search user"
msgstr "搜索用户"

#: frontend/src/components/FieldLayoutEditor.vue:336
msgid "Section"
msgstr "区块"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:130
msgid "See all participants"
msgstr "查看所有参与者"

#: frontend/src/pages/Dashboard.vue:50
msgid "Select Range"
msgstr "选择范围"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:47
msgid "Select Source Type"
msgstr ""

#: frontend/src/components/Settings/CurrencySettings.vue:51
msgid "Select currency"
msgstr "选择货币"

#: frontend/src/components/Settings/CurrencySettings.vue:75
msgid "Select provider"
msgstr "选择服务商"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:84
msgid "Select the assignees for {0}."
msgstr "请选择{0}的负责人。"

#: frontend/src/components/FieldLayout/Field.vue:345
msgid "Select {0}"
msgstr "选择{0}"

#: frontend/src/components/EmailEditor.vue:165
msgid "Send"
msgstr "发送"

#: frontend/src/components/Settings/InviteUserPage.vue:18
msgid "Send Invites"
msgstr "发送邀请"

#: frontend/src/components/Activities/ActivityHeader.vue:61
msgid "Send Template"
msgstr "发送模板"

#: frontend/src/pages/Deal.vue:87 frontend/src/pages/Lead.vue:134
msgid "Send an email"
msgstr "发送邮件"

#: frontend/src/components/Layouts/AppSidebar.vue:460
msgid "Send email"
msgstr "发送邮件"

#: frontend/src/components/Settings/InviteUserPage.vue:6
msgid "Send invites to"
msgstr "发送邀请至"

#. Option for the 'Type' (Select) field in DocType 'CRM Dropdown Item'
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
msgid "Separator"
msgstr "分隔符"

#. Label of the naming_series (Select) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Series"
msgstr "系列"

#. Label of the service_provider (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "Service Provider"
msgstr "服务商"

#: frontend/src/components/Layouts/AppSidebar.vue:581
msgid "Service level agreement"
msgstr "服务等级协议"

#: frontend/src/components/PrimaryDropdownItem.vue:27
msgid "Set As Primary"
msgstr "设为主要"

#: frontend/src/components/FilesUploader/FilesUploader.vue:66
msgid "Set all as private"
msgstr "全部设为私有"

#: frontend/src/components/FilesUploader/FilesUploader.vue:59
msgid "Set all as public"
msgstr "全部设为公开"

#: frontend/src/pages/Deal.vue:73
msgid "Set an organization"
msgstr "设置组织"

#: frontend/src/pages/Deal.vue:636 frontend/src/pages/MobileDeal.vue:523
msgid "Set as Primary Contact"
msgstr "设为主要联系人"

#: frontend/src/components/ViewControls.vue:1078
msgid "Set as default"
msgstr "设为默认"

#: frontend/src/components/Settings/CurrencySettings.vue:164
msgid "Set currency"
msgstr "设置货币"

#: frontend/src/pages/Lead.vue:115
msgid "Set first name"
msgstr "设置名字"

#: frontend/src/components/Layouts/AppSidebar.vue:533
msgid "Setting up"
msgstr "系统配置中"

#: frontend/src/components/Settings/emailConfig.js:145
msgid "Setting up Frappe Mail requires you to have an API key and API Secret of your email account. Read more "
msgstr "配置Frappe Mail需提供邮件账户的API密钥与API密钥。了解更多"

#: frontend/src/components/Settings/emailConfig.js:97
msgid "Setting up GMail requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "配置GMail需启用双重认证及应用专用密码。了解更多"

#: frontend/src/components/Settings/emailConfig.js:105
msgid "Setting up Outlook requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more"
msgstr "配置Outlook需启用双重认证及应用专用密码。了解更多"

#: frontend/src/components/Settings/emailConfig.js:113
msgid "Setting up Sendgrid requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "配置Sendgrid需启用双重认证及应用专用密码。了解更多 "

#: frontend/src/components/Settings/emailConfig.js:121
msgid "Setting up SparkPost requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "配置SparkPost需启用双重认证及应用专用密码。了解更多 "

#: frontend/src/components/Settings/emailConfig.js:129
msgid "Setting up Yahoo requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "配置Yahoo需启用双重认证及应用专用密码。了解更多 "

#: frontend/src/components/Settings/emailConfig.js:137
msgid "Setting up Yandex requires you to enable two factor authentication\n"
"\t\t  and app specific passwords. Read more "
msgstr "配置Yandex需启用双重认证及应用专用密码。了解更多 "

#. Label of the defaults_tab (Tab Break) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Layouts/AppSidebar.vue:537
#: frontend/src/components/Settings/Settings.vue:12
msgid "Settings"
msgstr "设置"

#: frontend/src/components/Settings/EmailAdd.vue:6
msgid "Setup Email"
msgstr "设置邮件"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.py:199
msgid "Setup the Exchange Rate Provider as 'Exchangerate Host' in settings, as default provider does not support currency conversion for {0} to {1}."
msgstr "请在设置中将汇率服务商配置为'Exchangerate Host'，因默认服务商不支持{0}至{1}的货币转换。"

#: frontend/src/components/Layouts/AppSidebar.vue:339
msgid "Setup your password"
msgstr "设置密码"

#: frontend/src/components/Activities/Activities.vue:237
msgid "Show"
msgstr "显示"

#: frontend/src/components/Controls/Password.vue:19
msgid "Show Password"
msgstr "显示密码"

#: frontend/src/components/FieldLayoutEditor.vue:354
msgid "Show border"
msgstr "显示边框"

#: frontend/src/components/FieldLayoutEditor.vue:349
msgid "Show label"
msgstr "显示标签"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:138
msgid "Show less"
msgstr "显示更少"

#: frontend/src/components/Controls/GridRowFieldsModal.vue:20
#: frontend/src/components/Modals/DataFieldsModal.vue:20
#: frontend/src/components/Modals/QuickEntryModal.vue:20
#: frontend/src/components/Modals/SidePanelModal.vue:20
msgid "Show preview"
msgstr "显示预览"

#. Option for the 'Type' (Select) field in DocType 'CRM Fields Layout'
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
msgid "Side Panel"
msgstr "侧边栏"

#. Option for the 'Type' (Select) field in DocType 'CRM Global Settings'
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
msgid "Sidebar Items"
msgstr "侧边栏项"

#. Description of the 'Condition' (Code) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Simple Python Expression, Example: doc.status == 'Open' and doc.lead_source == 'Ads'"
msgstr "简单Python表达式，示例：doc.status == '开放' and doc.lead_source == '广告'"

#: frontend/src/components/AssignTo.vue:83
msgid "Since you removed {0} from the assignee, the {0} has also been removed."
msgstr "由于您已将{0}从负责人中移除，对应的{0}也已同步移除。"

#: frontend/src/components/AssignTo.vue:76
msgid "Since you removed {0} from the assignee, the {0} has been changed to the next available assignee {1}."
msgstr "由于您已将{0}从负责人中移除，系统已将{0}自动分配给下一可用负责人{1}。"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:735
msgid "Some error occurred while renaming assignment rule"
msgstr "重命名分配规则时发生错误"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:720
msgid "Some error occurred while updating assignment rule"
msgstr "更新分配规则时发生错误"

#: frontend/src/components/SortBy.vue:10 frontend/src/components/SortBy.vue:24
#: frontend/src/components/SortBy.vue:232
msgid "Sort"
msgstr "排序"

#. Label of the source (Link) field in DocType 'CRM Deal'
#. Label of the source (Link) field in DocType 'CRM Lead'
#. Label of the source (Link) field in DocType 'Failed Lead Sync Log'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: frontend/src/components/Modals/EditValueModal.vue:10
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:60
msgid "Source"
msgstr "来源"

#. Label of the source_name (Data) field in DocType 'CRM Lead Source'
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:69
msgid "Source Name"
msgstr "来源名称"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:46
msgid "Source Type"
msgstr "来源类型"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:167
msgid "Source disabled successfully"
msgstr ""

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSources.vue:166
msgid "Source enabled successfully"
msgstr ""

#: frontend/src/components/Dashboard/AddChartModal.vue:68
#: frontend/src/components/Dashboard/DashboardItem.vue:21
msgid "Spacer"
msgstr "空白分隔线"

#: crm/api/dashboard.py:758 crm/api/dashboard.py:820
msgid "Stage"
msgstr "阶段"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.js:15
msgid "Standard Form Scripts can not be modified, duplicate the Form Script instead."
msgstr "标准表单脚本不可修改，请复制后进行编辑"

#. Label of the standard_rate (Currency) field in DocType 'CRM Product'
#: crm/fcrm/doctype/crm_product/crm_product.json
msgid "Standard Selling Rate"
msgstr "标准售价"

#: frontend/src/components/ViewControls.vue:634
msgid "Standard Views"
msgstr "标准视图"

#. Label of the start_date (Date) field in DocType 'CRM Service Level
#. Agreement'
#. Label of the start_date (Date) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Start Date"
msgstr "开始日期"

#. Label of the start_time (Datetime) field in DocType 'CRM Call Log'
#. Label of the start_time (Time) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
#: frontend/src/components/Calendar/CalendarEventPanel.vue:234
#: frontend/src/components/Modals/EventModal.vue:103
msgid "Start Time"
msgstr "开始时间"

#: frontend/src/composables/event.js:198
msgid "Start and end time are required"
msgstr "必须填写开始时间和结束时间"

#: frontend/src/pages/Welcome.vue:21
msgid "Start with sample 10 leads"
msgstr "从10条示例线索开始"

#. Label of the status (Select) field in DocType 'CRM Call Log'
#. Label of the status (Data) field in DocType 'CRM Communication Status'
#. Label of the status (Link) field in DocType 'CRM Deal'
#. Label of the deal_status (Data) field in DocType 'CRM Deal Status'
#. Label of the status (Select) field in DocType 'CRM Invitation'
#. Label of the status (Link) field in DocType 'CRM Lead'
#. Label of the lead_status (Data) field in DocType 'CRM Lead Status'
#. Label of the status (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_communication_status/crm_communication_status.json
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
#: crm/fcrm/doctype/crm_task/crm_task.json frontend/src/pages/Contact.vue:513
#: frontend/src/pages/MobileContact.vue:513
#: frontend/src/pages/MobileOrganization.vue:457
#: frontend/src/pages/Organization.vue:487
msgid "Status"
msgstr "状态"

#. Label of the status_change_log (Table) field in DocType 'CRM Deal'
#. Label of the status_change_log (Table) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Status Change Log"
msgstr "状态变更日志"

#: frontend/src/components/Modals/DealModal.vue:216
#: frontend/src/components/Modals/LeadModal.vue:157
msgid "Status is required"
msgstr "状态为必填项"

#. Label of the subdomain (Data) field in DocType 'CRM Exotel Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Subdomain"
msgstr "子域名"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:68
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:68
msgid "Subject"
msgstr "主题"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:155
#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:158
msgid "Subject is required"
msgstr "必须填写主题"

#: frontend/src/components/Modals/EmailTemplateSelectorModal.vue:45
msgid "Subject: {0}"
msgstr "主题：{0}"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Sunday"
msgstr "周日"

#: frontend/src/components/Settings/emailConfig.js:16
msgid "Support / Sales"
msgstr "支持/销售"

#: frontend/src/components/FilesUploader/FilesUploader.vue:46
msgid "Switch camera"
msgstr "切换摄像头"

#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.js:6
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:23
msgid "Sync Now"
msgstr "立即同步"

#: frontend/src/pages/Welcome.vue:32
msgid "Sync your contacts,email and calenders"
msgstr "同步联系人、邮件和日历"

#. Label of the syncing_tab (Tab Break) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Syncing"
msgstr "同步"

#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:359
msgid "Syncing started in background"
msgstr ""

#: frontend/src/components/Settings/Settings.vue:106
msgid "System Configuration"
msgstr "系统配置"

#. Name of a role
#. Option for the 'Role' (Select) field in DocType 'CRM Invitation'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_invitation/crm_invitation.json
#: crm/fcrm/doctype/crm_lead_source/crm_lead_source.json
#: crm/fcrm/doctype/crm_lost_reason/crm_lost_reason.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_product/crm_product.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
#: crm/fcrm/doctype/erpnext_crm_settings/erpnext_crm_settings.json
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form/facebook_lead_form.json
#: crm/lead_syncing/doctype/facebook_page/facebook_page.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "System Manager"
msgstr "系统管理员"

#: frontend/src/components/EmailEditor.vue:22
msgid "TO"
msgstr "收件人"

#: frontend/src/components/Telephony/ExotelCallUI.vue:149
msgid "Take a note..."
msgstr "记录备注..."

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:555
msgid "Task"
msgstr "任务"

#: frontend/src/pages/Deal.vue:567 frontend/src/pages/Lead.vue:414
#: frontend/src/pages/MobileDeal.vue:456 frontend/src/pages/MobileLead.vue:362
msgid "Tasks"
msgstr "任务"

#: frontend/src/components/Modals/AboutModal.vue:67
msgid "Telegram Channel"
msgstr "Telegram频道"

#: frontend/src/components/Settings/Settings.vue:185
msgid "Telephony"
msgstr "电话系统"

#. Label of the telephony_medium (Select) field in DocType 'CRM Call Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
msgid "Telephony Medium"
msgstr "电话媒介"

#: frontend/src/components/Settings/TelephonySettings.vue:8
msgid "Telephony settings"
msgstr "电话设置"

#: frontend/src/components/Settings/EmailTemplate/NewEmailTemplate.vue:175
msgid "Template created successfully"
msgstr "模板创建成功"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:213
msgid "Template deleted successfully"
msgstr "模板删除成功"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:197
msgid "Template disabled successfully"
msgstr "模板已成功禁用"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:196
msgid "Template enabled successfully"
msgstr "模板已成功启用"

#: frontend/src/components/Settings/EmailTemplate/EmailTemplates.vue:83
msgid "Template name"
msgstr "模板名称"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:239
msgid "Template renamed successfully"
msgstr "模板重命名成功"

#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:204
msgid "Template updated successfully"
msgstr "模板更新成功"

#. Label of a shortcut in the Frappe CRM Workspace
#: crm/fcrm/workspace/frappe_crm/frappe_crm.json
msgid "Territories"
msgstr "区域"

#. Label of the territory (Link) field in DocType 'CRM Deal'
#. Label of the territory (Link) field in DocType 'CRM Lead'
#. Label of the territory (Link) field in DocType 'CRM Organization'
#: crm/api/dashboard.py:1067 crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "Territory"
msgstr "区域"

#. Label of the territory_manager (Link) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Manager"
msgstr "区域经理"

#. Label of the territory_name (Data) field in DocType 'CRM Territory'
#: crm/fcrm/doctype/crm_territory/crm_territory.json
msgid "Territory Name"
msgstr "区域名称"

#: crm/templates/emails/helpdesk_invitation.html:16
msgid "Thanks"
msgstr "谢谢"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.py:46
msgid "The Condition '{0}' is invalid: {1}"
msgstr "条件'{0}'无效：{1}"

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM Deal'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
msgid "The rate used to convert the deal’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "本汇率用于将商机货币转换为CRM基准货币（在CRM设置中配置）。该汇率在首次添加货币时设定，不会自动更新。"

#. Description of the 'Exchange Rate' (Float) field in DocType 'CRM
#. Organization'
#: crm/fcrm/doctype/crm_organization/crm_organization.json
msgid "The rate used to convert the organization’s currency to your crm's base currency (set in CRM Settings). It is set once when the currency is first added and doesn't change automatically."
msgstr "本汇率用于将组织货币转换为CRM基准货币（在CRM设置中配置）。该汇率在首次添加货币时设定，不会自动更新。"

#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.js:14
msgid "There can only be one default priority in Priorities table"
msgstr "优先级表中只能有一个默认优先级"

#: frontend/src/components/Modals/AddressModal.vue:128
#: frontend/src/components/Modals/CallLogModal.vue:131
msgid "These fields are required: {0}"
msgstr "以下字段为必填项：{0}"

#: frontend/src/components/Filter.vue:639
msgid "This Month"
msgstr "本月"

#: frontend/src/components/Filter.vue:643
msgid "This Quarter"
msgstr "本季度"

#: frontend/src/components/Filter.vue:635
msgid "This Week"
msgstr "本周"

#: frontend/src/components/Filter.vue:647
msgid "This Year"
msgstr "本年"

#: frontend/src/components/SidePanelLayoutEditor.vue:116
msgid "This section is not editable"
msgstr "此区块不可编辑"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:59
msgid "This will delete selected items and items linked to it, are you sure?"
msgstr "此操作将删除所选项目及其关联项目，是否确认继续？"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:62
msgid "This will delete selected items and unlink linked items to it, are you sure?"
msgstr "此操作将删除所选项目并取消其关联项目的链接，是否确认继续？"

#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.js:9
msgid "This will restore (if not exist) all the default statuses, custom fields and layouts. Delete & Restore will delete default layouts and then restore them."
msgstr "将恢复（如不存在）所有默认状态、自定义字段及布局。删除并恢复将删除默认布局后重新创建"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Thursday"
msgstr "周四"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:227
msgid "Time"
msgstr "时间"

#: frontend/src/components/Filter.vue:351
msgid "Timespan"
msgstr "时间跨度"

#. Label of the title (Data) field in DocType 'CRM Task'
#. Label of the title (Data) field in DocType 'FCRM Note'
#: crm/fcrm/doctype/crm_task/crm_task.json
#: crm/fcrm/doctype/fcrm_note/fcrm_note.json
#: frontend/src/components/Modals/EventModal.vue:43
#: frontend/src/components/Modals/NoteModal.vue:26
#: frontend/src/components/Modals/TaskModal.vue:25
msgid "Title"
msgstr "标题"

#. Label of the title_field (Data) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: frontend/src/components/Kanban/KanbanSettings.vue:29
msgid "Title Field"
msgstr "标题字段"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:545
#: frontend/src/components/Modals/EventModal.vue:320
msgid "Title is required"
msgstr "标题为必填项"

#. Label of the to (Data) field in DocType 'CRM Call Log'
#. Label of the to (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
#: frontend/src/components/Activities/EmailArea.vue:53
msgid "To"
msgstr "至"

#. Label of the to_date (Date) field in DocType 'CRM Holiday List'
#. Label of the to_date (Datetime) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Date"
msgstr "截止日期"

#. Label of the to_type (Data) field in DocType 'CRM Status Change Log'
#: crm/fcrm/doctype/crm_status_change_log/crm_status_change_log.json
msgid "To Type"
msgstr "目标类型"

#. Label of the to_user (Link) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
msgid "To User"
msgstr "目标用户"

#: frontend/src/components/Settings/EmailEdit.vue:118
msgid "To know more about setting up email accounts, click"
msgstr "了解更多邮件账户设置信息，请点击"

#: frontend/src/components/Filter.vue:627 frontend/src/pages/Calendar.vue:79
msgid "Today"
msgstr "今天"

#. Option for the 'Status' (Select) field in DocType 'CRM Task'
#: crm/fcrm/doctype/crm_task/crm_task.json
msgid "Todo"
msgstr "待办"

#: frontend/src/components/Modals/SidePanelModal.vue:59
msgid "Toggle on for preview"
msgstr "切换预览模式"

#: frontend/src/components/Filter.vue:631
msgid "Tomorrow"
msgstr "明天"

#: frontend/src/components/Modals/NoteModal.vue:42
#: frontend/src/components/Modals/TaskModal.vue:46
msgid "Took a call with John Doe and discussed the new project."
msgstr "与张三通话讨论新项目"

#. Label of the total (Currency) field in DocType 'CRM Deal'
#. Label of the total (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total"
msgstr "总计"

#. Label of the total_holidays (Int) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Total Holidays"
msgstr "总假期天数"

#. Description of the 'Net Total' (Currency) field in DocType 'CRM Deal'
#. Description of the 'Net Total' (Currency) field in DocType 'CRM Lead'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
msgid "Total after discount"
msgstr "折后总计"

#: crm/api/dashboard.py:129
#: frontend/src/components/Dashboard/AddChartModal.vue:76
msgid "Total leads"
msgstr "线索总数"

#: crm/api/dashboard.py:130
msgid "Total number of leads"
msgstr "线索总数量"

#: crm/api/dashboard.py:191
msgid "Total number of non won/lost deals"
msgstr "非赢单/输单商机总数"

#: crm/api/dashboard.py:311
msgid "Total number of won deals based on its closure date"
msgstr "基于关闭日期的赢单商机总数"

#. Label of the traceback (Code) field in DocType 'Failed Lead Sync Log'
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
msgid "Traceback"
msgstr "回溯信息"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Tuesday"
msgstr "周二"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:173
msgid "Turn into a group"
msgstr "转换为分组"

#. Label of the twiml_sid (Data) field in DocType 'CRM Twilio Settings'
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.json
msgid "TwiML SID"
msgstr "TwiML SID"

#. Option for the 'Telephony Medium' (Select) field in DocType 'CRM Call Log'
#. Label of the twilio (Check) field in DocType 'CRM Telephony Agent'
#. Option for the 'Default Medium' (Select) field in DocType 'CRM Telephony
#. Agent'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: frontend/src/components/Layouts/AppSidebar.vue:601
#: frontend/src/components/Settings/TelephonySettings.vue:40
#: frontend/src/components/Settings/TelephonySettings.vue:50
msgid "Twilio"
msgstr "Twilio"

#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:59
#: crm/fcrm/doctype/crm_twilio_settings/crm_twilio_settings.py:60
msgid "Twilio API credential creation error."
msgstr "Twilio API凭证创建错误"

#. Label of the twilio_number (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "Twilio Number"
msgstr "Twilio号码"

#: frontend/src/components/Settings/TelephonySettings.vue:289
msgid "Twilio is not enabled"
msgstr "Twilio未启用"

#: frontend/src/components/Settings/TelephonySettings.vue:125
msgid "Twilio settings updated successfully"
msgstr "Twilio设置更新成功"

#. Label of the type (Select) field in DocType 'CRM Call Log'
#. Label of the type (Select) field in DocType 'CRM Deal Status'
#. Label of the type (Select) field in DocType 'CRM Dropdown Item'
#. Label of the type (Select) field in DocType 'CRM Fields Layout'
#. Label of the type (Select) field in DocType 'CRM Global Settings'
#. Label of the type (Select) field in DocType 'CRM Notification'
#. Label of the type (Select) field in DocType 'CRM View Settings'
#. Label of the type (Data) field in DocType 'Facebook Lead Form Question'
#. Label of the type (Select) field in DocType 'Failed Lead Sync Log'
#. Label of the type (Select) field in DocType 'Lead Sync Source'
#: crm/fcrm/doctype/crm_call_log/crm_call_log.json
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_dropdown_item/crm_dropdown_item.json
#: crm/fcrm/doctype/crm_fields_layout/crm_fields_layout.json
#: crm/fcrm/doctype/crm_global_settings/crm_global_settings.json
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
#: crm/lead_syncing/doctype/facebook_lead_form_question/facebook_lead_form_question.json
#: crm/lead_syncing/doctype/failed_lead_sync_log/failed_lead_sync_log.json
#: crm/lead_syncing/doctype/lead_sync_source/lead_sync_source.json
msgid "Type"
msgstr "类型"

#: frontend/src/components/Calendar/Attendee.vue:233
msgid "Type an email address to add attendee"
msgstr "输入邮件地址以添加参与者"

#: frontend/src/components/Activities/WhatsAppBox.vue:85
msgid "Type your message here..."
msgstr "在此输入消息..."

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:454
msgid "Unassign conditions are invalid"
msgstr "取消分配条件无效"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:224
msgid "Unassignment condition"
msgstr "取消分配条件"

#: crm/integrations/exotel/handler.py:170
msgid "Unauthorized request"
msgstr "未授权的请求"

#: frontend/src/components/FieldLayoutEditor.vue:344
msgid "Uncollapsible"
msgstr "不可折叠"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:183
msgid "Ungroup conditions"
msgstr "取消条件分组"

#: frontend/src/components/Telephony/TwilioCallUI.vue:24
#: frontend/src/components/Telephony/TwilioCallUI.vue:123
msgid "Unknown"
msgstr "未知"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:132
msgid "Unlink"
msgstr "取消链接"

#: frontend/src/components/DeleteLinkedDocModal.vue:78
msgid "Unlink all"
msgstr "取消所有链接"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:73
msgid "Unlink and delete"
msgstr "取消链接并删除"

#: frontend/src/components/BulkDeleteLinkedDocModal.vue:35
msgid "Unlink and delete {0} items"
msgstr "取消链接并删除{0}个项目"

#: frontend/src/components/DeleteLinkedDocModal.vue:243
msgid "Unlink linked item"
msgstr "取消关联项链接"

#: frontend/src/components/DeleteLinkedDocModal.vue:79
msgid "Unlink {0} item(s)"
msgstr "取消{0}个项目的链接"

#: frontend/src/components/ViewControls.vue:1093
msgid "Unpin View"
msgstr "取消固定视图"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:22
msgid "Unsaved"
msgstr "未保存"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:575
msgid "Unsaved changes"
msgstr "未保存的修改"

#: frontend/src/components/FieldLayoutEditor.vue:26
#: frontend/src/components/Modals/AddressModal.vue:8
#: frontend/src/components/Modals/CallLogModal.vue:8
#: frontend/src/components/Modals/CreateDocumentModal.vue:8
#: frontend/src/components/Section.vue:21
#: frontend/src/components/SidePanelLayoutEditor.vue:19
msgid "Untitled"
msgstr "未命名"

#: frontend/src/components/ColumnSettings.vue:129
#: frontend/src/components/Modals/AssignmentModal.vue:79
#: frontend/src/components/Modals/ChangePasswordModal.vue:45
#: frontend/src/components/Modals/EventModal.vue:156
#: frontend/src/components/Modals/NoteModal.vue:52
#: frontend/src/components/Modals/TaskModal.vue:106
#: frontend/src/components/Settings/BrandSettings.vue:15
#: frontend/src/components/Settings/CurrencySettings.vue:17
#: frontend/src/components/Settings/EmailTemplate/EditEmailTemplate.vue:21
#: frontend/src/components/Settings/HomeActions.vue:15
#: frontend/src/components/Settings/LeadSyncing/LeadSyncSourceForm.vue:30
#: frontend/src/components/Settings/ProfileSettings.vue:95
#: frontend/src/components/Settings/SettingsPage.vue:20
#: frontend/src/components/Settings/TelephonySettings.vue:23
#: frontend/src/components/Telephony/ExotelCallUI.vue:210
msgid "Update"
msgstr "更新"

#: frontend/src/components/Settings/EmailEdit.vue:74
msgid "Update Account"
msgstr "更新账户"

#: frontend/src/components/Modals/EditValueModal.vue:30
msgid "Update {0} Records"
msgstr "更新{0}条记录"

#: frontend/src/components/Controls/ImageUploader.vue:20
#: frontend/src/components/FilesUploader/FilesUploader.vue:83
msgid "Upload"
msgstr "上传"

#: frontend/src/components/Activities/Activities.vue:408
#: frontend/src/components/Activities/ActivityHeader.vue:55
#: frontend/src/components/Activities/ActivityHeader.vue:154
msgid "Upload Attachment"
msgstr "上传附件"

#: frontend/src/components/Activities/WhatsAppBox.vue:132
msgid "Upload Document"
msgstr "上传文档"

#: frontend/src/components/Activities/WhatsAppBox.vue:140
msgid "Upload Image"
msgstr "上传图片"

#: frontend/src/components/Activities/WhatsAppBox.vue:148
msgid "Upload Video"
msgstr "上传视频"

#: frontend/src/components/Settings/ProfileSettings.vue:27
#: frontend/src/pages/Contact.vue:48 frontend/src/pages/Lead.vue:89
#: frontend/src/pages/MobileContact.vue:38
#: frontend/src/pages/MobileOrganization.vue:38
#: frontend/src/pages/Organization.vue:48
msgid "Upload image"
msgstr "上传图片"

#: frontend/src/components/Controls/ImageUploader.vue:17
msgid "Uploading {0}%"
msgstr "上传进度{0}%"

#. Label of the user (Link) field in DocType 'CRM Dashboard'
#. Label of the user (Link) field in DocType 'CRM Telephony Agent'
#. Label of the user (Link) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_dashboard/crm_dashboard.json
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "User"
msgstr "用户"

#: frontend/src/components/Settings/Settings.vue:127
msgid "User Management"
msgstr "用户管理"

#. Label of the user_name (Data) field in DocType 'CRM Telephony Agent'
#: crm/fcrm/doctype/crm_telephony_agent/crm_telephony_agent.json
msgid "User Name"
msgstr "用户名"

#: frontend/src/components/Settings/Users.vue:296
msgid "User {0} has been removed"
msgstr "用户{0}已被移除"

#: frontend/src/components/Modals/AddExistingUserModal.vue:20
#: frontend/src/components/Settings/Settings.vue:130
#: frontend/src/components/Settings/Users.vue:7
msgid "Users"
msgstr "用户"

#: frontend/src/components/Modals/AddExistingUserModal.vue:105
msgid "Users added successfully"
msgstr "用户添加成功"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:465
msgid "Users are required"
msgstr "必须指定用户"

#. Label of the section_break_nevd (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Validity"
msgstr "有效期"

#: frontend/src/components/Modals/EditValueModal.vue:14
msgid "Value"
msgstr "值"

#: frontend/src/components/Modals/ViewModal.vue:14
msgid "View Name"
msgstr "视图名称"

#: frontend/src/pages/Deal.vue:219
msgid "View contact"
msgstr "查看联系人"

#: frontend/src/components/Layouts/AppSidebar.vue:566
msgid "Views"
msgstr "视图"

#: frontend/src/components/Layouts/AppSidebar.vue:563
msgid "Web form"
msgstr "网页表单"

#. Label of the webhook_verify_token (Data) field in DocType 'CRM Exotel
#. Settings'
#: crm/fcrm/doctype/crm_exotel_settings/crm_exotel_settings.json
msgid "Webhook Verify Token"
msgstr "Webhook验证令牌"

#. Label of the website (Data) field in DocType 'CRM Deal'
#. Label of the website (Data) field in DocType 'CRM Lead'
#. Label of the website (Data) field in DocType 'CRM Organization'
#: crm/fcrm/doctype/crm_deal/crm_deal.json
#: crm/fcrm/doctype/crm_lead/crm_lead.json
#: crm/fcrm/doctype/crm_organization/crm_organization.json
#: frontend/src/components/Modals/AboutModal.vue:52
msgid "Website"
msgstr "网站"

#. Option for the 'Weekly Off' (Select) field in DocType 'CRM Holiday List'
#. Option for the 'Workday' (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Wednesday"
msgstr "周三"

#. Label of the weekly_off (Check) field in DocType 'CRM Holiday'
#. Label of the weekly_off (Select) field in DocType 'CRM Holiday List'
#: crm/fcrm/doctype/crm_holiday/crm_holiday.json
#: crm/fcrm/doctype/crm_holiday_list/crm_holiday_list.json
msgid "Weekly Off"
msgstr "每周休息"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:11
msgid "Welcome Message"
msgstr "欢迎消息"

#: crm/fcrm/doctype/helpdesk_crm_settings/helpdesk_crm_settings.py:155
msgid "Welcome to Helpdesk"
msgstr "欢迎使用服务台"

#: frontend/src/pages/Welcome.vue:4
msgid "Welcome {0}, lets add your first lead"
msgstr "欢迎{0}，现在开始添加您的首条线索"

#. Option for the 'Type' (Select) field in DocType 'CRM Notification'
#: crm/fcrm/doctype/crm_notification/crm_notification.json
#: frontend/src/components/Layouts/AppSidebar.vue:603
#: frontend/src/components/Settings/Settings.vue:191
#: frontend/src/pages/Deal.vue:582 frontend/src/pages/Lead.vue:429
#: frontend/src/pages/MobileDeal.vue:471 frontend/src/pages/MobileLead.vue:377
msgid "WhatsApp"
msgstr "WhatsApp"

#: frontend/src/components/Modals/WhatsappTemplateSelectorModal.vue:4
msgid "WhatsApp Templates"
msgstr "WhatsApp模板"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:20
#: frontend/src/components/Filter.vue:43 frontend/src/components/Filter.vue:81
msgid "Where"
msgstr "条件"

#: frontend/src/components/ColumnSettings.vue:108
msgid "Width"
msgstr "宽度"

#: frontend/src/components/ColumnSettings.vue:113
msgid "Width can be in number, pixel or rem (eg. 3, 30px, 10rem)"
msgstr "宽度可为数字、像素或rem（如3、30px、10rem）"

#. Option for the 'Type' (Select) field in DocType 'CRM Deal Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
msgid "Won"
msgstr "赢单"

#: crm/api/dashboard.py:310
#: frontend/src/components/Dashboard/AddChartModal.vue:79
msgid "Won deals"
msgstr "赢单商机"

#. Label of the workday (Select) field in DocType 'CRM Service Day'
#: crm/fcrm/doctype/crm_service_day/crm_service_day.json
msgid "Workday"
msgstr "工作日"

#. Label of the section_break_rmgo (Section Break) field in DocType 'CRM
#. Service Level Agreement'
#. Label of the working_hours (Table) field in DocType 'CRM Service Level
#. Agreement'
#: crm/fcrm/doctype/crm_service_level_agreement/crm_service_level_agreement.json
msgid "Working Hours"
msgstr "工作时间"

#: frontend/src/components/Filter.vue:623
msgid "Yesterday"
msgstr "昨天"

#: crm/api/whatsapp.py:43 crm/api/whatsapp.py:223 crm/api/whatsapp.py:237
#: frontend/src/components/Activities/WhatsAppArea.vue:34
#: frontend/src/components/Activities/WhatsAppBox.vue:14
msgid "You"
msgstr "您"

#: crm/utils/__init__.py:262
msgid "You are not permitted to access this resource."
msgstr "您无权访问此资源"

#: crm/templates/emails/helpdesk_invitation.html:22
msgid "You can also copy-paste following link in your browser"
msgstr "也可在浏览器中粘贴以下链接"

#: frontend/src/components/Telephony/CallUI.vue:39
msgid "You can change the default calling medium from the settings"
msgstr "可在设置中修改默认呼叫媒介"

#: frontend/src/components/Settings/CurrencySettings.vue:100
msgid "You can get your access key from "
msgstr "您可从以下位置获取访问密钥"

#: frontend/src/components/Settings/InviteUserPage.vue:36
msgid "You can invite multiple users by comma separating their email addresses"
msgstr "您可通过逗号分隔邮件地址的方式邀请多个用户"

#: crm/integrations/exotel/handler.py:85
msgid "You do not have Exotel Number set in your Telephony Agent"
msgstr "您的电话客服未设置Exotel号码"

#: crm/integrations/exotel/handler.py:93
msgid "You do not have mobile number set in your Telephony Agent"
msgstr "您的电话客服未设置手机号码"

#: frontend/src/data/document.js:34
msgid "You do not have permission to access this document"
msgstr "您无权访问本文档"

#: crm/fcrm/doctype/crm_form_script/crm_form_script.py:24
msgid "You need to be in developer mode to edit a Standard Form Script"
msgstr "需进入开发者模式才能编辑标准表单脚本"

#: frontend/src/components/Settings/AssignmentRules/AssigneeSearch.vue:150
msgid "You will be redirected to invite user page, unsaved changes will be lost."
msgstr "系统将跳转至邀请用户页面，未保存的修改将会丢失"

#: frontend/src/components/Settings/LeadSyncing/leadSyncSourceConfig.js:9
msgid "You will need a Meta developer account and an access token to sync leads from Facebook. Read more "
msgstr ""

#: crm/api/todo.py:100
msgid "Your assignment on task {0} has been removed by {1}"
msgstr "{1}移除了您在任务{0}的分配"

#: crm/api/todo.py:37 crm/api/todo.py:78
msgid "Your assignment on {0} {1} has been removed by {2}"
msgstr "{2}移除了您在{0}{1}的分配"

#: crm/templates/emails/helpdesk_invitation.html:6
msgid "Your login id is"
msgstr "您的登录ID是"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:610
msgid "Your old condition will be overwritten. Are you sure you want to save?"
msgstr "原有条件将被覆盖。是否确认保存？"

#: frontend/src/components/Activities/CommentArea.vue:9
msgid "added a"
msgstr "添加了"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "amber"
msgstr "琥珀色"

#: crm/api/todo.py:109
msgid "assigned a new task {0} to you"
msgstr "向您分配了新任务{0}"

#: crm/api/todo.py:89
msgid "assigned a {0} {1} to you"
msgstr "向您分配了{0}{1}"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "black"
msgstr "黑色"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "blue"
msgstr "蓝色"

#: frontend/src/components/Activities/Activities.vue:239
msgid "changes from"
msgstr "来自的更改"

#: frontend/src/components/Activities/CommentArea.vue:11
msgid "comment"
msgstr "评论"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:65
#: frontend/src/components/ConditionsFilter/CFCondition.vue:73
msgid "condition"
msgstr "条件"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "cyan"
msgstr "青色"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:131
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:380
msgid "deals"
msgstr "商机"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:190
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:274
msgid "desk"
msgstr "工作台"

#: frontend/src/components/Calendar/Attendee.vue:295
#: frontend/src/components/Controls/EmailMultiSelect.vue:254
msgid "email already exists"
msgstr "邮件地址已存在"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
#: frontend/src/components/Settings/CurrencySettings.vue:106
msgid "exchangerate.host"
msgstr "汇率服务商"

#. Option for the 'Service Provider' (Select) field in DocType 'FCRM Settings'
#: crm/fcrm/doctype/fcrm_settings/fcrm_settings.json
msgid "frankfurter.app"
msgstr "法兰克福应用"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "gray"
msgstr "灰色"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "green"
msgstr "绿色"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "group_by"
msgstr "分组依据"

#: frontend/src/components/Activities/CallArea.vue:16
msgid "has made a call"
msgstr "进行了通话"

#: frontend/src/components/Activities/CallArea.vue:15
msgid "has reached out"
msgstr "已联系"

#: frontend/src/components/Settings/EmailAdd.vue:36
#: frontend/src/components/Settings/EmailEdit.vue:25
msgid "here"
msgstr "此处"

#: frontend/src/utils/index.js:146
msgid "in 1 hour"
msgstr "1小时后"

#: frontend/src/utils/index.js:142
msgid "in 1 minute"
msgstr "1分钟后"

#: frontend/src/utils/index.js:160
msgid "in 1 year"
msgstr "1年后"

#: frontend/src/utils/index.js:111
msgid "in {0} M"
msgstr "{0}个月后"

#: frontend/src/utils/index.js:107
msgid "in {0} d"
msgstr "{0}天后"

#: frontend/src/utils/index.js:154
msgid "in {0} days"
msgstr "{0}天后"

#: frontend/src/utils/index.js:101
msgid "in {0} h"
msgstr "{0}小时后"

#: frontend/src/utils/index.js:148
msgid "in {0} hours"
msgstr "{0}小时后"

#: frontend/src/utils/index.js:99
msgid "in {0} m"
msgstr "{0}分钟后"

#: frontend/src/utils/index.js:144
msgid "in {0} minutes"
msgstr "{0}分钟后"

#: frontend/src/utils/index.js:158
msgid "in {0} months"
msgstr "{0}个月后"

#: frontend/src/utils/index.js:109
msgid "in {0} w"
msgstr "{0}周后"

#: frontend/src/utils/index.js:156
msgid "in {0} weeks"
msgstr "{0}周后"

#: frontend/src/utils/index.js:113
msgid "in {0} y"
msgstr "{0}年后"

#: frontend/src/utils/index.js:162
msgid "in {0} years"
msgstr "{0}年后"

#: frontend/src/components/Modals/AddExistingUserModal.vue:28
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: frontend/src/utils/index.js:140 frontend/src/utils/index.js:166
msgid "just now"
msgstr "刚刚"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "kanban"
msgstr "看板"

#: crm/api/doc.py:40 crm/api/doc.py:158 crm/api/doc.py:503
msgid "label"
msgstr "标签"

#: frontend/src/components/Settings/AssignmentRules/AssigneeRules.vue:130
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:379
msgid "leads"
msgstr "线索"

#. Option for the 'Type' (Select) field in DocType 'CRM View Settings'
#: crm/fcrm/doctype/crm_view_settings/crm_view_settings.json
msgid "list"
msgstr "列表"

#: crm/api/comment.py:36 frontend/src/components/Notifications.vue:59
#: frontend/src/pages/MobileNotification.vue:46
msgid "mentioned you in {0}"
msgstr "在{0}中提到您"

#: frontend/src/components/FieldLayoutEditor.vue:368
msgid "next"
msgstr "下一步"

#: frontend/src/utils/index.js:97 frontend/src/utils/index.js:117
msgid "now"
msgstr "现在"

#: frontend/src/components/ConditionsFilter/CFCondition.vue:47
msgid "operator"
msgstr "操作员"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "orange"
msgstr "橙色"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "pink"
msgstr "粉色"

#: frontend/src/components/FieldLayoutEditor.vue:368
msgid "previous"
msgstr "上一步"

#: frontend/src/components/Activities/AttachmentArea.vue:97
msgid "private"
msgstr "私有"

#: frontend/src/components/Activities/AttachmentArea.vue:97
msgid "public"
msgstr "公开"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "purple"
msgstr "紫色"

#: crm/api/whatsapp.py:44
msgid "received a whatsapp message in {0}"
msgstr "在{0}收到WhatsApp消息"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "red"
msgstr "红色"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "teal"
msgstr "青色"

#: frontend/src/components/Activities/Activities.vue:278
#: frontend/src/components/Activities/Activities.vue:341
msgid "to"
msgstr "至"

#: frontend/src/utils/index.js:105 frontend/src/utils/index.js:152
msgid "tomorrow"
msgstr "明天"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "violet"
msgstr "紫罗兰色"

#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:193
#: frontend/src/components/Settings/AssignmentRules/AssignmentRuleView.vue:277
msgid "which are not compatible with this UI, you will need to recreate the conditions here if you want to manage and add new conditions from this UI."
msgstr "由于与当前用户界面不兼容，如需通过本界面管理和添加新条件，您需要在此重新创建条件"

#. Option for the 'Color' (Select) field in DocType 'CRM Deal Status'
#. Option for the 'Color' (Select) field in DocType 'CRM Lead Status'
#: crm/fcrm/doctype/crm_deal_status/crm_deal_status.json
#: crm/fcrm/doctype/crm_lead_status/crm_lead_status.json
msgid "yellow"
msgstr "黄色"

#: frontend/src/utils/index.js:179
msgid "yesterday"
msgstr "昨天"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:94
msgid "{0} Attendees"
msgstr "{0}位参与者"

#: frontend/src/utils/index.js:130
msgid "{0} M"
msgstr "{0} 月"

#: crm/api/todo.py:41
msgid "{0} assigned a {1} {2} to you"
msgstr "{0}向您分配了{1}{2}"

#: frontend/src/utils/index.js:126
msgid "{0} d"
msgstr "{0} 天"

#: frontend/src/utils/index.js:181
msgid "{0} days ago"
msgstr "{0}天前"

#: frontend/src/utils/index.js:121
msgid "{0} h"
msgstr "{0}小时"

#: frontend/src/components/Settings/Users.vue:286
msgid "{0} has been granted {1} access"
msgstr "已授予{0}{1}访问权限"

#: frontend/src/utils/index.js:174
msgid "{0} hours ago"
msgstr "{0}小时前"

#: frontend/src/composables/event.js:163
msgid "{0} hrs"
msgstr "{0}小时"

#: frontend/src/components/Calendar/CalendarEventPanel.vue:309
#: frontend/src/components/EmailEditor.vue:30
#: frontend/src/components/EmailEditor.vue:66
#: frontend/src/components/EmailEditor.vue:80
#: frontend/src/components/Modals/AddExistingUserModal.vue:37
#: frontend/src/components/Modals/EventModal.vue:127
msgid "{0} is an invalid email address"
msgstr "{0}是无效的电子邮件地址"

#: frontend/src/components/Modals/ConvertToDealModal.vue:172
msgid "{0} is required"
msgstr "{0}是必填项"

#: frontend/src/utils/index.js:119
msgid "{0} m"
msgstr "{0} 分"

#: frontend/src/composables/event.js:157
msgid "{0} mins"
msgstr "{0}分钟"

#: frontend/src/utils/index.js:170
msgid "{0} minutes ago"
msgstr "{0}分钟前"

#: frontend/src/utils/index.js:189
msgid "{0} months ago"
msgstr "{0}个月前"

#: frontend/src/utils/index.js:128
msgid "{0} w"
msgstr "{0} 周"

#: frontend/src/utils/index.js:185
msgid "{0} weeks ago"
msgstr "{0}周前"

#: frontend/src/utils/index.js:132
msgid "{0} y"
msgstr "{0}年前"

#: frontend/src/utils/index.js:193
msgid "{0} years ago"
msgstr "{0}年前"

#: frontend/src/data/script.js:326
msgid "⚠️ Avoid using \"trigger\" as a field name — it conflicts with the built-in trigger() method."
msgstr "⚠️ 请避免使用“trigger”作为字段名——该名称与内置trigger()方法冲突。"

#: frontend/src/data/script.js:338
msgid "⚠️ Method \"{0}\" not found in class."
msgstr "⚠️ 在类中未找到方法“{0}”。"

#: frontend/src/data/script.js:83
msgid "⚠️ No class found for doctype: {0}, it is mandatory to have a class for the parent doctype. it can be empty, but it should be present."
msgstr "⚠️ 未找到文档类型{0}对应的类，父文档类型必须包含类定义（可为空类，但必须存在）"

#: frontend/src/data/script.js:180
msgid "⚠️ No data found for parent field: {0}"
msgstr "⚠️ 父字段{0}中未找到数据"

#: frontend/src/data/script.js:188
msgid "⚠️ No row found for idx: {0} in parent field: {1}"
msgstr "⚠️ 在父字段{1}中未找到索引{0}对应的行"

