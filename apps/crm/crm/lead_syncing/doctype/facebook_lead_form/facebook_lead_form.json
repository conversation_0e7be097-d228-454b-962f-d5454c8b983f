{"actions": [], "allow_rename": 1, "autoname": "field:id", "creation": "2025-09-26 19:01:48.325681", "doctype": "DocType", "engine": "InnoDB", "field_order": ["page", "id", "column_break_ahyo", "form_name", "section_break_iqhq", "questions"], "fields": [{"fieldname": "page", "fieldtype": "Link", "in_list_view": 1, "label": "Page", "options": "Facebook Page", "reqd": 1}, {"fieldname": "id", "fieldtype": "Data", "label": "ID", "unique": 1}, {"fieldname": "column_break_ahyo", "fieldtype": "Column Break"}, {"fieldname": "form_name", "fieldtype": "Data", "label": "Form Name"}, {"fieldname": "section_break_iqhq", "fieldtype": "Section Break"}, {"fieldname": "questions", "fieldtype": "Table", "label": "Questions", "options": "Facebook Lead Form Question"}], "grid_page_length": 50, "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-29 18:50:19.215513", "modified_by": "Administrator", "module": "Lead Syncing", "name": "Facebook Lead Form", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "show_title_field_in_link": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "form_name"}