{"actions": [], "allow_rename": 1, "creation": "2024-01-01 00:00:00", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["safety_item", "description", "is_completed", "checked_by", "check_date"], "fields": [{"fieldname": "safety_item", "fieldtype": "Link", "in_list_view": 1, "label": "Safety Item", "options": "<PERSON><PERSON>", "reqd": 1}, {"fetch_from": "safety_item.description", "fieldname": "description", "fieldtype": "Small Text", "in_list_view": 1, "label": "Description"}, {"default": "0", "fieldname": "is_completed", "fieldtype": "Check", "in_list_view": 1, "label": "Completed"}, {"fieldname": "checked_by", "fieldtype": "Link", "in_list_view": 1, "label": "Checked By", "options": "Employee"}, {"fieldname": "check_date", "fieldtype": "Date", "in_list_view": 1, "label": "Check Date"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-10-21 21:46:13.349056", "modified_by": "Administrator", "module": "kamal-cisco", "name": "KC Job Safety Checklist", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}