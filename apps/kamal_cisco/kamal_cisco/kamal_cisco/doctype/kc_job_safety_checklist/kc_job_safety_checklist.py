# Copyright (c) 2024, Aakvatech-Limited and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class KCJobSafetyChecklist(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		check_date: DF.Date | None
		checked_by: DF.Link | None
		description: DF.SmallText | None
		is_completed: DF.Check
		parent: DF.Data
		parentfield: DF.Data
		parenttype: DF.Data
		safety_item: DF.Data

	# end: auto-generated types

	pass
