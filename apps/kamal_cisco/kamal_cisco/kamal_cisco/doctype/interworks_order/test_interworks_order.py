# Copyright (c) 2024, Aakvatech-Limited and Contributors
# See license.txt

import frappe
import unittest
from frappe.utils import today, add_days


class TestInterworksOrder(unittest.TestCase):
	def setUp(self):
		"""Set up test data"""
		# Create test departments if not exist
		for dept in ["Test Source Dept", "Test Target Dept"]:
			if not frappe.db.exists("Department", dept):
				department = frappe.get_doc({
					"doctype": "Department",
					"department_name": dept,
					"company": frappe.defaults.get_user_default("Company") or "Test Company"
				})
				department.insert(ignore_permissions=True)
		
		# Create test item if not exists
		if not frappe.db.exists("Item", "Test Item"):
			item = frappe.get_doc({
				"doctype": "Item",
				"item_code": "Test Item",
				"item_name": "Test Item",
				"item_group": "All Item Groups",
				"stock_uom": "Nos"
			})
			item.insert(ignore_permissions=True)
	
	def test_interworks_order_creation(self):
		"""Test basic interworks order creation"""
		order = frappe.get_doc({
			"doctype": "Interworks Order",
			"source_department": "Test Source Dept",
			"target_department": "Test Target Dept",
			"order_date": today(),
			"order_items": [
				{
					"item_code": "Test Item",
					"quantity": 5,
					"uom": "Nos",
					"remarks": "Test item"
				}
			]
		})
		order.insert()
		
		self.assertTrue(order.name)
		self.assertEqual(order.approval_status, "Draft")
	
	def test_same_department_validation(self):
		"""Test validation for same source and target departments"""
		order = frappe.get_doc({
			"doctype": "Interworks Order",
			"source_department": "Test Source Dept",
			"target_department": "Test Source Dept",  # Same as source
			"order_date": today(),
			"order_items": [
				{
					"item_code": "Test Item",
					"quantity": 5,
					"uom": "Nos"
				}
			]
		})
		
		with self.assertRaises(frappe.ValidationError):
			order.insert()
	
	def test_empty_items_validation(self):
		"""Test validation for empty order items"""
		order = frappe.get_doc({
			"doctype": "Interworks Order",
			"source_department": "Test Source Dept",
			"target_department": "Test Target Dept",
			"order_date": today(),
			"order_items": []  # Empty items
		})
		
		with self.assertRaises(frappe.ValidationError):
			order.insert()
	
	def test_date_validation(self):
		"""Test date validation"""
		order = frappe.get_doc({
			"doctype": "Interworks Order",
			"source_department": "Test Source Dept",
			"target_department": "Test Target Dept",
			"order_date": today(),
			"completion_date": add_days(today(), -1),  # Completion before order date
			"order_items": [
				{
					"item_code": "Test Item",
					"quantity": 5,
					"uom": "Nos"
				}
			]
		})
		
		with self.assertRaises(frappe.ValidationError):
			order.insert()
	
	def tearDown(self):
		"""Clean up test data"""
		# Clean up created orders
		frappe.db.delete("Interworks Order", {"source_department": "Test Source Dept"})
