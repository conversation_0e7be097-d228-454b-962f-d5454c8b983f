// Copyright (c) 2024, Aakvatech-Limited and contributors
// For license information, please see license.txt

frappe.ui.form.on('Interworks Order', {
	refresh: function(frm) {
		// Add custom buttons
		if (frm.doc.docstatus === 1 && frm.doc.approval_status === 'Approved') {
			frm.add_custom_button(__('Close Order'), function() {
				frappe.call({
					method: 'close_order',
					doc: frm.doc,
					callback: function(r) {
						frm.reload_doc();
					}
				});
			}, __('Actions'));
		}
		
		// Set order date to today if not set
		if (!frm.doc.order_date) {
			frm.set_value('order_date', frappe.datetime.get_today());
		}
		
		// Filter job cards based on departments
		set_job_card_filter(frm);
	},
	
	source_department: function(frm) {
		// Clear target department if same as source
		if (frm.doc.source_department === frm.doc.target_department) {
			frm.set_value('target_department', '');
		}
		set_job_card_filter(frm);
	},
	
	target_department: function(frm) {
		// Validate departments are different
		if (frm.doc.source_department === frm.doc.target_department) {
			frappe.msgprint(__('Source Department and Target Department cannot be the same'));
			frm.set_value('target_department', '');
		}
		set_job_card_filter(frm);
	},
	
	job_card: function(frm) {
		// Auto-populate departments from job card if available
		if (frm.doc.job_card) {
			frappe.db.get_doc('KC Job Card', frm.doc.job_card)
				.then(job_card => {
					if (job_card.department && !frm.doc.source_department) {
						frm.set_value('source_department', job_card.department);
					}
				});
		}
	},
	
	completion_date: function(frm) {
		// Validate completion date
		if (frm.doc.order_date && frm.doc.completion_date) {
			if (frm.doc.completion_date < frm.doc.order_date) {
				frappe.msgprint(__('Completion Date cannot be before Order Date'));
				frm.set_value('completion_date', '');
			}
		}
	},
	
	order_date: function(frm) {
		// Clear completion date if it becomes invalid
		if (frm.doc.order_date && frm.doc.completion_date) {
			if (frm.doc.completion_date < frm.doc.order_date) {
				frm.set_value('completion_date', '');
			}
		}
	}
});

// Child table events for Order Items
frappe.ui.form.on('Interworks Order Items', {
	item_code: function(frm, cdt, cdn) {
		let row = locals[cdt][cdn];
		if (row.item_code) {
			// Auto-populate UOM from item master
			frappe.db.get_value('Item', row.item_code, 'stock_uom')
				.then(r => {
					if (r.message && r.message.stock_uom) {
						frappe.model.set_value(cdt, cdn, 'uom', r.message.stock_uom);
					}
				});
		}
	},
	
	quantity: function(frm, cdt, cdn) {
		let row = locals[cdt][cdn];
		if (row.quantity <= 0) {
			frappe.msgprint(__('Quantity must be greater than 0'));
			frappe.model.set_value(cdt, cdn, 'quantity', 1);
		}
	},
	
	order_items_add: function(frm, cdt, cdn) {
		// Set default quantity when new row is added
		frappe.model.set_value(cdt, cdn, 'quantity', 1);
	}
});

function set_job_card_filter(frm) {
	// Filter job cards based on departments
	if (frm.doc.source_department || frm.doc.target_department) {
		let departments = [];
		if (frm.doc.source_department) departments.push(frm.doc.source_department);
		if (frm.doc.target_department) departments.push(frm.doc.target_department);
		
		frm.set_query('job_card', function() {
			return {
				filters: {
					'department': ['in', departments],
					'docstatus': 1
				}
			};
		});
	}
}
