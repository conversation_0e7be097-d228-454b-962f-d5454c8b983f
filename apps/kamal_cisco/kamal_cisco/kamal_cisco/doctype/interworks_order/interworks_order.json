{"actions": [], "allow_rename": 1, "autoname": "IWO-.#####", "creation": "2024-01-01 00:00:00.000000", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["source_department", "target_department", "job_card", "column_break_4", "order_date", "completion_date", "approval_status", "section_break_8", "order_items", "section_break_10", "authorized_by", "received_by"], "fields": [{"fieldname": "source_department", "fieldtype": "Link", "label": "Source Department", "options": "Department", "reqd": 1}, {"fieldname": "target_department", "fieldtype": "Link", "label": "Target Department", "options": "Department", "reqd": 1}, {"fieldname": "job_card", "fieldtype": "Link", "label": "Job Card", "options": "KC Job Card"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "order_date", "fieldtype": "Date", "label": "Order Date", "reqd": 1, "default": "Today"}, {"fieldname": "completion_date", "fieldtype": "Date", "label": "Completion Date"}, {"fieldname": "approval_status", "fieldtype": "Select", "label": "Approval Status", "options": "Draft\nApproved\nClosed", "reqd": 1, "default": "Draft"}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Order Items"}, {"fieldname": "order_items", "fieldtype": "Table", "label": "Order Items", "options": "Interworks Order Items", "reqd": 1}, {"fieldname": "section_break_10", "fieldtype": "Section Break", "label": "Authorization"}, {"fieldname": "authorized_by", "fieldtype": "Link", "label": "Authorized By", "options": "Employee"}, {"fieldname": "received_by", "fieldtype": "Link", "label": "Received By", "options": "Employee"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "kamal-cisco", "name": "Interworks Order", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}