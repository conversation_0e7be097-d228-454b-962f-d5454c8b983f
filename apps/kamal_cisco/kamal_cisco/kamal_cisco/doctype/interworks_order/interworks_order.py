# Copyright (c) 2024, Aakvatech-Limited and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import getdate


class InterworksOrder(Document):

	def validate(self):
		"""Validate the Interworks Order before saving"""
		self.validate_order_items()
		self.validate_dates()
		self.validate_departments()
	
	def validate_order_items(self):
		"""Validate that at least one item is present"""
		if not self.order_items:
			frappe.throw("At least one item is required in the Interworks Order.")
		
		# Validate each item
		for item in self.order_items:
			if not item.item_code:
				frappe.throw("Item Code is required for all order items.")
			if not item.quantity or item.quantity <= 0:
				frappe.throw(f"Quantity must be greater than 0 for item {item.item_code}")
			if not item.uom:
				frappe.throw(f"UOM is required for item {item.item_code}")
	
	def validate_dates(self):
		"""Validate order and completion dates"""
		if self.completion_date and self.order_date:
			if getdate(self.completion_date) < getdate(self.order_date):
				frappe.throw("Completion Date cannot be before Order Date.")
	
	def validate_departments(self):
		"""Validate that source and target departments are different"""
		if self.source_department == self.target_department:
			frappe.throw("Source Department and Target Department cannot be the same.")
	
	def on_submit(self):
		"""Actions to perform when the document is submitted"""
		if self.approval_status == "Draft":
			self.approval_status = "Approved"
			frappe.db.set_value(self.doctype, self.name, "approval_status", "Approved")
			frappe.db.commit()
	
	def before_cancel(self):
		"""Actions before cancelling the document"""
		frappe.db.set_value(self.doctype, self.name, "approval_status", "Draft")
		frappe.db.commit()
	
	def on_update_after_submit(self):
		"""Actions after updating a submitted document"""
		# Allow updating completion date and received_by after submission
		pass
	
	def close_order(self):
		"""Method to close the interworks order"""
		if self.approval_status != "Approved":
			frappe.throw("Only approved orders can be closed.")
		
		self.approval_status = "Closed"
		if not self.completion_date:
			self.completion_date = frappe.utils.today()
		
		self.save()
		frappe.msgprint("Interworks Order has been closed successfully.")


def validate(doc, method=None):
	"""Hook function for validation"""
	if not doc.order_items:
		frappe.throw("At least one item is required in the Interworks Order.")
	
	if doc.completion_date and doc.order_date:
		if getdate(doc.completion_date) < getdate(doc.order_date):
			frappe.throw("Completion Date cannot be before Order Date.")
	
	# Validate departments are different
	if doc.source_department == doc.target_department:
		frappe.throw("Source Department and Target Department cannot be the same.")


def on_submit(doc, method=None):
	"""Hook function for submission"""
	if doc.approval_status == "Draft":
		doc.approval_status = "Approved"


def before_cancel(doc, method=None):
	"""Hook function before cancellation"""
	doc.approval_status = "Draft"
