# Copyright (c) 2024, Aakvatech-Limited and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class KCJobProgress(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		date: DF.Date
		manhours: DF.Float
		materials_used: DF.Link | None
		parent: DF.Data
		parentfield: DF.Data
		parenttype: DF.Data
		technician: DF.Link
		work_done: DF.SmallText

	# end: auto-generated types

	pass
