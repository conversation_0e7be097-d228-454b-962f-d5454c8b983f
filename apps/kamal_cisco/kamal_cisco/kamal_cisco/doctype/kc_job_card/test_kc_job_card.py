# Copyright (c) 2024, Aakvatech-Limited and Contributors
# See license.txt

import frappe
import unittest
from frappe.utils import today, add_days


class TestKCJobCard(unittest.TestCase):
	def setUp(self):
		"""Set up test data"""
		# Create test asset if not exists
		if not frappe.db.exists("Asset", "Test Asset"):
			asset = frappe.get_doc({
				"doctype": "Asset",
				"asset_name": "Test Asset",
				"asset_category": "Equipment",
				"item_code": "Test Item",
				"company": frappe.defaults.get_user_default("Company") or "Test Company"
			})
			asset.insert(ignore_permissions=True)
		
		# Create test employee if not exists
		if not frappe.db.exists("Employee", "Test Employee"):
			employee = frappe.get_doc({
				"doctype": "Employee",
				"employee_name": "Test Employee",
				"company": frappe.defaults.get_user_default("Company") or "Test Company"
			})
			employee.insert(ignore_permissions=True)
	
	def test_job_card_creation(self):
		"""Test basic job card creation"""
		job_card = frappe.get_doc({
			"doctype": "KC Job Card",
			"job_description": "Test maintenance job",
			"asset": "Test Asset",
			"department": frappe.defaults.get_user_default("Company") or "Test Company",
			"shutdown_type": "Planned",
			"maintenance_type": "Preventive",
			"start_date": today(),
			"end_date": add_days(today(), 1)
		})
		job_card.insert()
		
		self.assertTrue(job_card.name)
		self.assertEqual(job_card.job_id, job_card.name)
	
	def test_date_validation(self):
		"""Test date validation"""
		job_card = frappe.get_doc({
			"doctype": "KC Job Card",
			"job_description": "Test maintenance job",
			"asset": "Test Asset",
			"department": frappe.defaults.get_user_default("Company") or "Test Company",
			"shutdown_type": "Planned",
			"maintenance_type": "Preventive",
			"start_date": today(),
			"end_date": add_days(today(), -1)  # End date before start date
		})
		
		with self.assertRaises(frappe.ValidationError):
			job_card.insert()
	
	def test_total_hours_calculation(self):
		"""Test total hours calculation from job progress"""
		job_card = frappe.get_doc({
			"doctype": "KC Job Card",
			"job_description": "Test maintenance job",
			"asset": "Test Asset",
			"department": frappe.defaults.get_user_default("Company") or "Test Company",
			"shutdown_type": "Planned",
			"maintenance_type": "Preventive",
			"start_date": today(),
			"job_progress": [
				{
					"date": today(),
					"technician": "Test Employee",
					"work_done": "Test work 1",
					"manhours": 4.0
				},
				{
					"date": today(),
					"technician": "Test Employee",
					"work_done": "Test work 2",
					"manhours": 2.5
				}
			]
		})
		job_card.insert()
		
		self.assertEqual(job_card.total_hours, 6.5)
	
	def tearDown(self):
		"""Clean up test data"""
		# Clean up created job cards
		frappe.db.delete("KC Job Card", {"job_description": "Test maintenance job"})
