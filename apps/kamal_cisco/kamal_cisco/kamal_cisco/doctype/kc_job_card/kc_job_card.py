# Copyright (c) 2024, Aakvatech-Limited and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt, getdate


class KCJobCard(Document):

	def validate(self):
		"""Validate the KC Job Card before saving"""
		self.validate_dates()
		self.calculate_total_hours()
	
	def validate_dates(self):
		"""Validate start and end dates"""
		if self.start_date and self.end_date:
			if getdate(self.end_date) < getdate(self.start_date):
				frappe.throw("End Date cannot be before Start Date.")
	
	def calculate_total_hours(self):
		"""Calculate total hours from job progress entries"""
		total_hours = 0
		for row in self.job_progress:
			total_hours += flt(row.manhours, 2)
		self.total_hours = total_hours
	
	def on_submit(self):
		"""Actions to perform when the document is submitted"""
		self.calculate_total_cost()
		self.update_status_on_submit()
	
	def calculate_total_cost(self):
		"""Calculate total cost including materials and labor"""
		material_cost = 0
		labor_cost = 0
		
		# Calculate material cost from job progress
		for row in self.job_progress:
			if row.materials_used:
				item_rate = frappe.db.get_value("Item", row.materials_used, "valuation_rate") or 0
				material_cost += flt(item_rate, 2)
		
		# Calculate labor cost
		labor_rate = self.get_labor_rate()
		labor_cost = flt(self.total_hours, 2) * flt(labor_rate, 2)
		
		# Set total cost
		self.total_cost = material_cost + labor_cost
		
		# Update the document in database
		frappe.db.set_value(self.doctype, self.name, "total_cost", self.total_cost)
		frappe.db.commit()
	
	def get_labor_rate(self):
		"""Get the default labor rate from system settings"""
		try:
			# Try to get from Maintenance Settings if it exists
			labor_rate = frappe.db.get_single_value("Maintenance Settings", "default_labor_rate")
			if labor_rate:
				return labor_rate
		except:
			pass
		
		# Fallback to a default rate or get from Employee master
		if self.assigned_to:
			try:
				employee_rate = frappe.db.get_value("Employee", self.assigned_to, "hourly_rate")
				if employee_rate:
					return employee_rate
			except:
				# hourly_rate field doesn't exist in Employee DocType
				pass
		
		# Default fallback rate
		return 50.0
	
	def update_status_on_submit(self):
		"""Update status when document is submitted"""
		if self.status == "Draft":
			frappe.db.set_value(self.doctype, self.name, "status", "Submitted")
			frappe.db.commit()
	
	def before_cancel(self):
		"""Actions before cancelling the document"""
		frappe.db.set_value(self.doctype, self.name, "status", "Draft")
		frappe.db.commit()


def validate(doc, method=None):
	"""Hook function for validation"""
	if doc.start_date and doc.end_date and getdate(doc.end_date) < getdate(doc.start_date):
		frappe.throw("End Date cannot be before Start Date.")
	
	# Calculate total hours
	total_hours = 0
	for row in doc.job_progress:
		total_hours += flt(row.manhours, 2)
	doc.total_hours = total_hours


def on_submit(doc, method=None):
	"""Hook function for submission"""
	# Calculate material cost
	material_cost = 0
	for row in doc.job_progress:
		if row.materials_used:
			item_rate = frappe.db.get_value("Item", row.materials_used, "valuation_rate") or 0
			material_cost += flt(item_rate, 2)
	
	# Get labor rate
	labor_rate = 50.0  # Default rate
	try:
		labor_rate = frappe.db.get_single_value("Maintenance Settings", "default_labor_rate") or 50.0
	except:
		pass
	
	# Calculate total cost
	labor_cost = flt(doc.total_hours, 2) * flt(labor_rate, 2)
	doc.total_cost = material_cost + labor_cost
	
	# Update status
	doc.status = "Submitted"
	
	frappe.db.commit()
