{"actions": [], "allow_rename": 1, "autoname": "format:KCJC-{#####}", "creation": "2024-01-01 00:00:00", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["job_description", "asset", "sub_asset", "work_order", "department", "column_break_7", "maintenance_type", "shutdown_type", "warranty", "assigned_to", "start_date", "end_date", "cost_center", "section_break_14", "safety_checklist", "section_break_16", "job_progress", "section_break_18", "total_hours", "total_cost", "status", "amended_from"], "fields": [{"fieldname": "job_description", "fieldtype": "Small Text", "in_list_view": 1, "label": "Job Description", "reqd": 1}, {"fieldname": "asset", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "options": "Asset Tree", "reqd": 1}, {"fieldname": "sub_asset", "fieldtype": "Link", "label": "Sub Asset", "options": "Asset Tree"}, {"fieldname": "work_order", "fieldtype": "Link", "label": "Work Order", "options": "Work Order"}, {"fieldname": "department", "fieldtype": "Link", "in_list_view": 1, "label": "Department", "options": "Department", "reqd": 1}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "shutdown_type", "fieldtype": "Select", "in_list_view": 1, "label": "Shutdown Type", "options": "Planned\nUnplanned\nNone", "reqd": 1}, {"fieldname": "maintenance_type", "fieldtype": "Select", "label": "Maintenance Type", "options": "Preventive\nCorrective\nBreakdown", "reqd": 1}, {"default": "0", "fieldname": "warranty", "fieldtype": "Check", "label": "Warranty"}, {"fieldname": "assigned_to", "fieldtype": "Link", "label": "Assigned To", "options": "Employee"}, {"fieldname": "start_date", "fieldtype": "Date", "label": "Start Date", "reqd": 1}, {"fieldname": "end_date", "fieldtype": "Date", "label": "End Date"}, {"fieldname": "section_break_14", "fieldtype": "Section Break", "label": "Safety Checklist"}, {"fieldname": "safety_checklist", "fieldtype": "Table", "label": "Safety Checklist", "options": "KC Job Safety Checklist"}, {"fieldname": "section_break_16", "fieldtype": "Section Break", "label": "Job Progress"}, {"fieldname": "job_progress", "fieldtype": "Table", "label": "Job Progress", "options": "KC Job Progress"}, {"fieldname": "section_break_18", "fieldtype": "Section Break", "label": "Summary"}, {"fieldname": "total_hours", "fieldtype": "Float", "label": "Total Hours", "read_only": 1}, {"fieldname": "total_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Cost", "read_only": 1}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Draft\nSubmitted\nIn Review\nApproved\nClosed", "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "KC Job Card", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-10-21 21:41:52.445779", "modified_by": "Administrator", "module": "kamal-cisco", "name": "KC Job Card", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance User", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}