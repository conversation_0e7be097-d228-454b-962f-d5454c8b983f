// Copyright (c) 2024, Aakvatech-Limited and contributors
// For license information, please see license.txt

frappe.ui.form.on('KC Job Card', {
	refresh: function(frm) {
		// Add custom buttons
		if (frm.doc.docstatus === 1 && frm.doc.status !== 'Closed') {
			frm.add_custom_button(__('Close Job Card'), function() {
				frm.set_value('status', 'Closed');
				frm.save();
			}, __('Actions'));
		}
				
		// Filter sub_asset based on selected asset
		if (frm.doc.asset) {
			frm.set_query('asset', function() {
				return {
					filters: {
						'is_group': 1
					}
				}
			})
			frm.set_query('sub_asset', function() {
				return {
					filters: {
						'parent_asset_tree': frm.doc.asset
					}
				};
			});
		}
	},
	
	asset: function(frm) {
		// Clear sub_asset when asset changes
		if (frm.doc.sub_asset) {
			frm.set_value('sub_asset', '');
		}
		
		// Set filter for sub_asset
		if (frm.doc.asset) {
			frm.set_query('sub_asset', function() {
				return {
					filters: {
						'parent_asset_tree': frm.doc.asset
					}
				};
			});
		}
	},
	
	start_date: function(frm) {
		// Validate start date is not in the future for breakdown maintenance
		if (frm.doc.maintenance_type === 'Breakdown' && frm.doc.start_date) {
			let today = frappe.datetime.get_today();
			if (frm.doc.start_date > today) {
				frappe.msgprint(__('Start Date for Breakdown maintenance should not be in the future'));
				frm.set_value('start_date', today);
			}
		}
	},
	
	end_date: function(frm) {
		// Validate end date
		if (frm.doc.start_date && frm.doc.end_date) {
			if (frm.doc.end_date < frm.doc.start_date) {
				frappe.msgprint(__('End Date cannot be before Start Date'));
				frm.set_value('end_date', '');
			}
		}
	},
	
	maintenance_type: function(frm) {
		// Auto-set shutdown type based on maintenance type
		if (frm.doc.maintenance_type === 'Breakdown') {
			frm.set_value('shutdown_type', 'Unplanned');
		} else if (frm.doc.maintenance_type === 'Preventive') {
			frm.set_value('shutdown_type', 'Planned');
		}
	}
});

// Child table events for Job Progress
frappe.ui.form.on('KC Job Progress', {
	manhours: function(frm, cdt, cdn) {
		// Calculate total hours when manhours change
		calculate_total_hours(frm);
	},
	
	job_progress_remove: function(frm) {
		// Recalculate total hours when row is removed
		calculate_total_hours(frm);
	},
	
	date: function(frm, cdt, cdn) {
		let row = locals[cdt][cdn];
		// Validate date is not in the future
		if (row.date > frappe.datetime.get_today()) {
			frappe.msgprint(__('Progress date cannot be in the future'));
			frappe.model.set_value(cdt, cdn, 'date', frappe.datetime.get_today());
		}
	}
});

// Child table events for Safety Checklist
frappe.ui.form.on('KC Job Safety Checklist', {
	is_completed: function(frm, cdt, cdn) {
		let row = locals[cdt][cdn];
		if (row.is_completed) {
			// Auto-fill check date and checked_by
			frappe.model.set_value(cdt, cdn, 'check_date', frappe.datetime.get_today());
			if (frappe.session.user_email) {
				frappe.db.get_value('Employee', {'user_id': frappe.session.user_email}, 'name')
					.then(r => {
						if (r.message && r.message.name) {
							frappe.model.set_value(cdt, cdn, 'checked_by', r.message.name);
						}
					});
			}
		} else {
			// Clear check date and checked_by when unchecked
			frappe.model.set_value(cdt, cdn, 'check_date', '');
			frappe.model.set_value(cdt, cdn, 'checked_by', '');
		}
	}
});

function calculate_total_hours(frm) {
	let total_hours = 0;
	frm.doc.job_progress.forEach(function(row) {
		total_hours += flt(row.manhours);
	});
	frm.set_value('total_hours', total_hours);
}
