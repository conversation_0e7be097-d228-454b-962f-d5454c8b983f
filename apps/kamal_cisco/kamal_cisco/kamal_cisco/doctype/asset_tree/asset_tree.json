{"actions": [], "allow_rename": 1, "autoname": "field:asset_name", "creation": "2025-10-21 21:58:29.030789", "default_view": "Tree", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["asset_name", "is_group", "parent_asset_tree", "asset_code", "description", "column_break_5", "asset_category", "location", "status", "section_break_10", "specifications", "maintenance_schedule", "lft", "rgt", "old_parent"], "fields": [{"fieldname": "asset_name", "fieldtype": "Data", "in_list_view": 1, "label": "Asset Name", "reqd": 1, "unique": 1}, {"fieldname": "parent_asset_tree", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "Asset Tree"}, {"fieldname": "asset_code", "fieldtype": "Data", "label": "Asset Code", "unique": 1}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "asset_category", "fieldtype": "Link", "label": "Asset Category", "options": "Asset Category"}, {"fieldname": "location", "fieldtype": "Link", "label": "Location", "options": "Location"}, {"default": "Active", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Status", "options": "Active\nInactive\nUnder Maintenance\nDecommissioned", "reqd": 1}, {"default": "0", "fieldname": "is_group", "fieldtype": "Check", "label": "Is Group"}, {"fieldname": "section_break_10", "fieldtype": "Section Break", "label": "Additional Details"}, {"fieldname": "specifications", "fieldtype": "Text", "label": "Specifications"}, {"fieldname": "maintenance_schedule", "fieldtype": "Link", "label": "Maintenance Schedule", "options": "Maintenance Schedule"}, {"fieldname": "lft", "fieldtype": "Int", "hidden": 1, "label": "Left", "no_copy": 1, "read_only": 1}, {"fieldname": "rgt", "fieldtype": "Int", "hidden": 1, "label": "Right", "no_copy": 1, "read_only": 1}, {"fieldname": "old_parent", "fieldtype": "Link", "label": "Old Parent", "options": "Asset Tree"}], "index_web_pages_for_search": 1, "is_tree": 1, "links": [], "modified": "2025-10-21 22:06:27.490287", "modified_by": "Administrator", "module": "kamal-cisco", "name": "Asset Tree", "naming_rule": "By fieldname", "nsm_parent_field": "parent_asset_tree", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Maintenance User", "share": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "asset_name", "track_changes": 1}