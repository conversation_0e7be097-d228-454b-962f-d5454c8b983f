// Copyright (c) 2024, Aakvatech-Limited and contributors
// For license information, please see license.txt

frappe.ui.form.on('Asset Tree', {
	refresh: function(frm) {
		// Set filters for parent asset to prevent circular references
		frm.set_query('parent_asset_tree', function() {
			return {
				filters: {
					'name': ['!=', frm.doc.name],
					'is_group': 1
				}
			};
		});
	},
	parent_asset_tree: function(frm) {
		// Prevent setting self as parent
		if (frm.doc.parent_asset_tree === frm.doc.name) {
			frappe.msgprint(__('Asset cannot be its own parent'));
			frm.set_value('parent_asset_tree', '');
		}
	},
	
	is_group: function(frm) {
		// If not a group, ensure it has a parent (unless it's a root asset)
		if (!frm.doc.is_group && !frm.doc.parent_asset_tree) {
			frappe.msgprint(__('Non-group assets should have a parent asset'));
		}
		
		if (frm.doc.is_group) {
			frm.set_df_property('parent_asset_tree', 'read_only', 1);
		}
	}
});
