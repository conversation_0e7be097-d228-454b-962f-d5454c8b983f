# Copyright (c) 2024, Aakvatech-Limited and Contributors
# See license.txt

import frappe
import unittest


class TestAssetTree(unittest.TestCase):
	def test_asset_tree_creation(self):
		"""Test basic asset tree creation"""
		asset = frappe.get_doc({
			"doctype": "Asset Tree",
			"asset_name": "Test Root Asset",
			"is_group": 1,
			"status": "Active"
		})
		asset.insert()
		
		self.assertTrue(asset.name)
		self.assertEqual(asset.asset_name, "Test Root Asset")
	
	def test_unique_asset_code(self):
		"""Test asset code uniqueness"""
		# Create first asset
		asset1 = frappe.get_doc({
			"doctype": "Asset Tree",
			"asset_name": "Test Asset 1",
			"asset_code": "TEST001",
			"status": "Active"
		})
		asset1.insert()
		
		# Try to create second asset with same code
		asset2 = frappe.get_doc({
			"doctype": "Asset Tree",
			"asset_name": "Test Asset 2",
			"asset_code": "TEST001",  # Same code
			"status": "Active"
		})
		
		with self.assertRaises(frappe.ValidationError):
			asset2.insert()
	
	def tearDown(self):
		"""Clean up test data"""
		# Clean up created assets
		frappe.db.delete("Asset Tree", {"asset_name": ["like", "Test%"]})
