# Copyright (c) 2024, Aakvatech-Limited and contributors
# For license information, please see license.txt

import frappe
from frappe.utils.nestedset import NestedSet


class AssetTree(NestedSet):

	def validate(self):
		# Call parent validate if it exists (NestedSet may not implement validate)
		_parent_validate = getattr(super(AssetTree, self), "validate", None)
		if callable(_parent_validate):
			_parent_validate()

		# Validate that asset code is unique if provided
		if self.asset_code:
			existing = frappe.db.get_value(
				"Asset Tree",
				{"asset_code": self.asset_code, "name": ["!=", self.name]},
			)
			if existing:
				frappe.throw(f"Asset Code {self.asset_code} already exists for {existing}")
	
	def on_update(self):
		NestedSet.on_update(self)
