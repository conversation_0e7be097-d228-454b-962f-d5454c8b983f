{"actions": [], "allow_rename": 1, "creation": "2024-01-01 00:00:00.000000", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "drawing_no", "quantity", "uom", "remarks"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "drawing_no", "fieldtype": "Data", "in_list_view": 1, "label": "Drawing No"}, {"fieldname": "quantity", "fieldtype": "Float", "in_list_view": 1, "label": "Quantity", "precision": "3", "reqd": 1}, {"fieldname": "uom", "fieldtype": "Link", "in_list_view": 1, "label": "UOM", "options": "UOM", "reqd": 1}, {"fieldname": "remarks", "fieldtype": "Small Text", "in_list_view": 1, "label": "Remarks"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-01-01 00:00:00.000000", "modified_by": "Administrator", "module": "kamal-cisco", "name": "Interworks Order Items", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}