# Copyright (c) 2024, Aakvatech-Limited and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class InterworksOrderItems(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		drawing_no: DF.Data | None
		item_code: DF.Link
		parent: DF.Data
		parentfield: DF.Data
		parenttype: DF.Data
		quantity: DF.Float
		remarks: DF.SmallText | None
		uom: DF.Link

	# end: auto-generated types

	pass
