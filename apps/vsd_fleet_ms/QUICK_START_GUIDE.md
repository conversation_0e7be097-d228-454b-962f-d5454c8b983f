# VSD Fleet MS - Quick Start Guide

## 5-Minute Setup

### Step 1: Configure Transport Settings (2 minutes)
1. Go to **Fleet MS > Settings > Transport Settings**
2. Set:
   - Vehicle Fuel Parent Warehouse
   - Fuel Item
   - Expense Account Group
   - Cash/Bank Account Group

### Step 2: Create Master Data (2 minutes)
1. **Create a Route:**
   - Fleet MS > Masters > Trip Routes
   - Add route name, start/end points
   - Add route steps with distances

2. **Register a Truck:**
   - Fleet MS > Masters > Truck
   - Enter license plate, truck number
   - Set make, model, year
   - Save

3. **Register a Driver:**
   - Fleet MS > Masters > Driver
   - Enter name, license number
   - Set status to Active
   - Save

### Step 3: Create Your First Trip (1 minute)
1. **Create Transportation Order:**
   - Fleet MS > Transactions > Transportation Order
   - Select customer
   - Enter cargo details
   - Assign truck, driver, route
   - Save

2. **Create Manifest:**
   - Fleet MS > Trips > Manifest
   - Select truck, driver, route
   - Add cargo
   - Submit

3. **Create Trip:**
   - From Manifest, click "Create Vehicle Trip"
   - Review details
   - Submit to start trip

## Common Workflows

### Workflow 1: Simple Local Transport
```
Transportation Order → Manifest → Trip → Complete → Invoice
```

### Workflow 2: Container Transport with Clearing
```
Clearing File → Transportation Order → Cargo Registration → 
Manifest → Trip → Delivery → Update Clearing File → Invoice
```

### Workflow 3: Cross-Border Transport
```
Transportation Order → Cargo Registration → Manifest → 
Trip → Border Crossings → Delivery → Invoice
```

## Quick Reference

### Key Doctypes

| DocType | Purpose | Location |
|---------|---------|----------|
| Transportation Order | Customer transport request | Transactions |
| Cargo Registration | Register cargo details | Transactions |
| Manifest | Group cargo for trip | Trips |
| Trips | Execute transport | Trips |
| Truck | Vehicle master | Masters |
| Driver | Driver master | Masters |
| Trip Routes | Route master | Masters |

### Important Fields

**Transportation Order:**
- Customer, Transport Type, Route
- Cargo Location/Destination
- Assign Transport (child table)

**Manifest:**
- Transporter Type, Truck, Driver
- Route, Trailers
- Manifest Cargo Details (child table)

**Trip:**
- Manifest, Route, Truck, Driver
- Trip Status, ETA
- Fuel Management (for in-house)

### Status Flow

**Trip Status:**
```
Pending → In Progress → Completed
```

**Truck Status:**
```
Available → On Trip → Available
(or Under Maintenance, Out of Service)
```

## Integration with Clearing App

### Quick Integration Steps

1. **From Clearing File, create Transport Order:**
   ```javascript
   // Add button in Clearing File
   frm.add_custom_button('Create Transport Order', function() {
       // Call create_transport_order method
   });
   ```

2. **Link Documents:**
   - Use reference_doctype and reference_docname
   - Maintain file_number for tracking

3. **Sync Container Data:**
   - Container numbers
   - BL numbers
   - Seal numbers

## Troubleshooting Quick Fixes

| Issue | Quick Fix |
|-------|-----------|
| Can't assign vehicle | Check vehicle status, complete existing trip |
| No fuel stock entry | Enable "Maintain Stock" on truck, set fuel warehouse |
| Invoice not creating | Save document first, select rows to invoice |
| Route steps not showing | Re-select route, refresh form |

## Keyboard Shortcuts

- **Ctrl + K**: Quick search
- **Ctrl + G**: Go to list
- **Ctrl + S**: Save
- **Ctrl + Shift + S**: Submit
- **Ctrl + B**: Toggle sidebar

## Tips & Tricks

1. **Use Manifests**: Always create manifests before trips for better organization
2. **Enable Fuel Tracking**: Set "Maintain Stock" on trucks for accurate fuel management
3. **Configure Dimensions**: Set up accounting dimensions for better financial reporting
4. **Regular Inspections**: Create vehicle inspections before and after trips
5. **Monitor Reports**: Check trip profitability regularly

## Need Help?

- **Full Documentation**: See VSD_FLEET_MS_USER_GUIDE.pdf
- **GitHub**: https://github.com/VVSD-LTD/vsd_fleet_ms
- **Support**: <EMAIL>

---

**Quick Start Version**: 1.0  
**Last Updated**: 2025-01-04

