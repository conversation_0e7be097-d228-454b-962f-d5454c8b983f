#!/usr/bin/env python3
"""
Convert Markdown documentation to PDF using markdown2 and wkhtmltopdf
"""

import subprocess
import os

# Read the markdown file
with open('VSD_FLEET_MS_USER_GUIDE.md', 'r', encoding='utf-8') as f:
    markdown_content = f.read()

# Create HTML with styling
html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>VSD Fleet Management System - User Guide</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }}
        h1 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            page-break-before: always;
        }}
        h1:first-of-type {{
            page-break-before: avoid;
        }}
        h2 {{
            color: #34495e;
            border-bottom: 2px solid #95a5a6;
            padding-bottom: 5px;
            margin-top: 30px;
        }}
        h3 {{
            color: #7f8c8d;
            margin-top: 20px;
        }}
        code {{
            background-color: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }}
        pre {{
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            overflow-x: auto;
        }}
        pre code {{
            background-color: transparent;
            padding: 0;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #3498db;
            color: white;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        ul, ol {{
            margin: 10px 0;
            padding-left: 30px;
        }}
        li {{
            margin: 5px 0;
        }}
        blockquote {{
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin: 20px 0;
            color: #555;
            font-style: italic;
        }}
        .page-break {{
            page-break-after: always;
        }}
        @media print {{
            body {{
                max-width: 100%;
            }}
            h1, h2, h3 {{
                page-break-after: avoid;
            }}
        }}
    </style>
</head>
<body>
"""

# Convert markdown to HTML using Python's markdown library
try:
    import markdown
    html_body = markdown.markdown(markdown_content, extensions=['tables', 'fenced_code', 'codehilite'])
except ImportError:
    # Fallback: simple conversion
    html_body = markdown_content.replace('\n\n', '</p><p>').replace('\n', '<br>')
    html_body = f'<p>{html_body}</p>'

html_content += html_body
html_content += """
</body>
</html>
"""

# Write HTML file
with open('VSD_FLEET_MS_USER_GUIDE.html', 'w', encoding='utf-8') as f:
    f.write(html_content)

print("HTML file created: VSD_FLEET_MS_USER_GUIDE.html")

# Convert to PDF using wkhtmltopdf
try:
    subprocess.run([
        'wkhtmltopdf',
        '--enable-local-file-access',
        '--page-size', 'A4',
        '--margin-top', '20mm',
        '--margin-bottom', '20mm',
        '--margin-left', '15mm',
        '--margin-right', '15mm',
        '--print-media-type',
        'VSD_FLEET_MS_USER_GUIDE.html',
        'VSD_FLEET_MS_USER_GUIDE.pdf'
    ], check=True)
    print("PDF file created: VSD_FLEET_MS_USER_GUIDE.pdf")
except subprocess.CalledProcessError as e:
    print(f"Error creating PDF: {e}")
except FileNotFoundError:
    print("wkhtmltopdf not found. Please install it first.")

