{"actions": [], "autoname": "format:Process-PCV-{###}", "creation": "2025-09-25 15:44:03.534699", "doctype": "DocType", "engine": "InnoDB", "field_order": ["parent_pcv", "status", "p_l_closing_balance", "normal_balances", "bs_closing_balance", "z_opening_balances", "amended_from"], "fields": [{"fieldname": "parent_pcv", "fieldtype": "Link", "in_list_view": 1, "label": "PCV", "options": "Period Closing Voucher", "reqd": 1}, {"default": "Queued", "fieldname": "status", "fieldtype": "Select", "label": "Status", "no_copy": 1, "options": "Queued\nRunning\nPaused\nCompleted\nCancelled"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Process Period Closing Voucher", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "p_l_closing_balance", "fieldtype": "JSON", "label": "P&L Closing Balance", "no_copy": 1}, {"fieldname": "normal_balances", "fieldtype": "Table", "label": "Dates to Process", "no_copy": 1, "options": "Process Period Closing Voucher Detail"}, {"fieldname": "z_opening_balances", "fieldtype": "Table", "label": "Opening Balances", "no_copy": 1, "options": "Process Period Closing Voucher Detail"}, {"fieldname": "bs_closing_balance", "fieldtype": "JSON", "label": "Balance Sheet Closing Balance"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-11-05 11:40:24.996403", "modified_by": "Administrator", "module": "Accounts", "name": "Process Period Closing Voucher", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "submit": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}