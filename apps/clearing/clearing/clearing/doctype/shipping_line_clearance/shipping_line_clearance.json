{"actions": [], "allow_rename": 1, "autoname": "format:SC-{YYYY}-{####}", "creation": "2024-07-22 16:45:32.899636", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["clearing_file", "consignee", "container_no", "port_of_loading", "isuue_date", "delivery_order_expire_date", "drop_off_date", "container_deposit_amount", "container_deposit_date", "etb", "column_break_a2owv", "posting_date", "status", "awbbl_no", "staff_id", "staff_name", "invoice_received", "shipping_charges_paid_by_consignee_section", "charge", "paid_by", "section_break_j<PERSON>rz", "shipping_charges", "make_journal", "total_charges", "total_charges_section", "paid_by_clearing_agent", "invoice_paid", "total_paid", "column_break_r0s5o", "section_break_keyml", "attach_documents", "section_break_uevyo", "document", "amended_from"], "fields": [{"fieldname": "clearing_file", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Clearing File", "options": "Clearing File", "reqd": 1}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Shipping Line Clearance", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Payment Pending\nPayment Completed", "read_only": 1}, {"fieldname": "section_break_keyml", "fieldtype": "Section Break"}, {"fieldname": "column_break_a2owv", "fieldtype": "Column Break"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date"}, {"fetch_from": "clearing_file.customer", "fieldname": "consignee", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "consignee", "options": "Customer"}, {"fieldname": "document", "fieldtype": "Table", "label": "Document", "options": "Ship clearance Document", "read_only": 1}, {"default": "0", "fieldname": "paid_by_clearing_agent", "fieldtype": "Check", "label": "Paid By Clearing Agent"}, {"fieldname": "attach_documents", "fieldtype": "<PERSON><PERSON>", "label": "Attach Documents"}, {"fieldname": "section_break_uevyo", "fieldtype": "Section Break"}, {"fieldname": "staff_id", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Staff ID", "options": "Employee"}, {"fieldname": "delivery_order_expire_date", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "Delivery Order Expire Date"}, {"fieldname": "total_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Total Paid By Clearing Agent"}, {"fieldname": "total_charges_section", "fieldtype": "Section Break", "label": "Total Payment"}, {"fieldname": "column_break_r0s5o", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "invoice_paid", "fieldtype": "Check", "label": "Shipping Charges Paid"}, {"fieldname": "drop_off_date", "fieldtype": "Date", "label": "Drop Off Date"}, {"default": "0", "fieldname": "invoice_received", "fieldtype": "Check", "label": "Invoice Received"}, {"depends_on": "container_deposit_date", "fieldname": "container_deposit_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Container <PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "isuue_date", "fieldtype": "Date", "label": "Delivery Order Issue Date"}, {"fieldname": "container_deposit_date", "fieldtype": "Date", "label": "Container Deposit Date"}, {"fieldname": "etb", "fieldtype": "Date", "label": "ETB"}, {"depends_on": "staff_id", "fetch_from": "staff_id.employee_name", "fieldname": "staff_name", "fieldtype": "Data", "ignore_user_permissions": 1, "label": "Staff Name"}, {"depends_on": "paid_by_clearing_agent", "fieldname": "section_break_j<PERSON>rz", "fieldtype": "Section Break", "label": "Paid by Clearing Agent"}, {"allow_bulk_edit": 1, "fieldname": "shipping_charges", "fieldtype": "Table", "label": "Shipping Charges ", "options": "Shipping Charges"}, {"fieldname": "shipping_charges_paid_by_consignee_section", "fieldtype": "Section Break", "label": "Paid by Consignee"}, {"fieldname": "total_paid", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Paid"}, {"fieldname": "charge", "fieldtype": "Table", "in_standard_filter": 1, "label": "Shipping Charges", "options": "Shipping Charges Paid"}, {"fieldname": "paid_by", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Paid by Consignee"}, {"fetch_from": "clearing_file.awbbl_no", "fieldname": "awbbl_no", "fieldtype": "Data", "in_standard_filter": 1, "label": "AWB/BL No"}, {"fieldname": "container_no", "fieldtype": "Data", "label": "Container No"}, {"fieldname": "port_of_loading", "fieldtype": "Data", "label": "Port of Loading"}, {"fieldname": "make_journal", "fieldtype": "<PERSON><PERSON>", "label": "Make Journal"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-11-11 12:12:27.559675", "modified_by": "Administrator", "module": "Clearing", "name": "Shipping Line Clearance", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Clearing Agent", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "TRA Clearing Agent", "share": 1, "submit": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}