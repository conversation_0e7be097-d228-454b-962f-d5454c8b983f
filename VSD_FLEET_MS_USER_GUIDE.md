# VSD Fleet Management System - Comprehensive User Guide

## Table of Contents
1. [Introduction](#introduction)
2. [What is VSD Fleet MS?](#what-is-vsd-fleet-ms)
3. [Installation](#installation)
4. [Initial Setup & Configuration](#initial-setup--configuration)
5. [Master Data Setup](#master-data-setup)
6. [Complete Workflow Guide](#complete-workflow-guide)
7. [Reports & Analytics](#reports--analytics)
8. [Integration with Clearing App](#integration-with-clearing-app)
9. [Best Practices](#best-practices)

---

## Introduction

The **VSD Fleet Management System** is a comprehensive Frappe/ERPNext application designed to streamline and optimize transport operations. It provides end-to-end management of fleet operations, from vehicle registration to trip completion and invoicing.

### Key Benefits
- **Centralized Fleet Management**: Manage all trucks, trailers, and drivers in one place
- **Real-time Trip Tracking**: Monitor trip status and locations in real-time
- **Automated Invoicing**: Generate customer invoices directly from transportation orders
- **Fuel Management**: Track fuel consumption and manage fuel requests
- **Financial Integration**: Seamless integration with ERPNext accounting
- **Compliance Ready**: Maintain vehicle inspections and regulatory compliance

---

## What is VSD Fleet MS?

VSD Fleet MS is a complete transportation management solution that handles:

### Core Modules
1. **Master Data Management**
   - Trucks, Trailers, and Drivers
   - Routes and Locations
   - Cargo Types
   - Fixed Expenses

2. **Transportation Operations**
   - Transportation Orders
   - Cargo Registration
   - Manifest Preparation
   - Trip Management
   - Vehicle Inspections

3. **Financial Management**
   - Automated Invoicing
   - Expense Tracking
   - Fuel Management
   - Payment Requests

4. **Reporting & Analytics**
   - Trip Reports
   - Fuel Expense Reports
   - Trip Report and Expenses

---

## Installation

### Prerequisites
- Frappe Framework installed
- ERPNext installed (recommended)
- Bench setup completed

### Installation Steps

1. **Navigate to your Frappe Bench directory:**
   ```bash
   cd /path/to/frappe-bench
   ```

2. **Get the app from the repository:**
   ```bash
   bench get-app https://github.com/VVSD-LTD/vsd_fleet_ms.git
   ```

3. **Install the app on your site:**
   ```bash
   bench --site [your-site-name] install-app vsd_fleet_ms
   ```

4. **Migrate your site:**
   ```bash
   bench --site [your-site-name] migrate
   ```

5. **Restart bench:**
   ```bash
   bench restart
   ```

---

## Initial Setup & Configuration

### Step 1: Access Transport Settings

Navigate to: **Fleet MS > Settings > Transport Settings**

This is a **Single DocType** (only one record exists) that controls all fleet management configurations.

### Step 2: Configure Essential Settings

#### A. Warehouse Configuration
- **Vehicle Fuel Parent Warehouse**: Select the parent warehouse for fuel management
  - This should be a group warehouse
  - All truck-specific fuel warehouses will be created under this parent

#### B. Item Groups
- **Sales Item Group**: Item group for transportation services
- **Fuel Item Group**: Item group for fuel items
- **Fuel Item**: Default fuel item for stock management

#### C. Account Groups
- **Expense Account Group**: Define expense accounts for transportation costs
  - Add multiple expense account groups as needed
  - These will be available when creating fixed expenses

- **Cash or Bank Account Group**: Define cash/bank accounts for payments
  - Add multiple account groups for different payment methods

#### D. Accounting Dimensions
Configure accounting dimensions to track financial data across different dimensions:

**Fields:**
- **Dimension Name**: Select from existing accounting dimensions (e.g., Truck, Route, Driver)
- **Source DocType**: The doctype where the dimension value comes from (e.g., Transport Assignments)
- **Source Type**: Field, Value, or Child
- **Source Field Name**: Field name in the source doctype
- **Target DocType**: Where to apply the dimension (e.g., Sales Invoice)
- **Target Type**: Main or Child
- **Target Field Name**: Field name in the target doctype

**Example Configuration:**
```
Dimension Name: Truck
Source DocType: Transport Assignments
Source Type: Field
Source Field Name: assigned_vehicle
Target DocType: Sales Invoice
Target Type: Main
Target Field Name: truck
```

---

## Master Data Setup

### 1. Countries & Locations

**Purpose**: Define countries and cities for route planning

**Navigation**: Fleet MS > Masters > Countries

**Steps:**
1. Create a new Country record
2. Enter country name
3. Add cities/locations within that country
4. Save

**Why**: Required for defining cargo origins, destinations, and route planning

---

### 2. Transport Locations

**Purpose**: Define specific locations for trip tracking

**Navigation**: Fleet MS > Masters > Transport Location

**Steps:**
1. Create new location
2. Enter location name
3. Select location type (Border, City, Port, etc.)
4. Mark if it's a local or international border
5. Save

**Why**: Used in route planning and trip tracking

---

### 3. Trip Routes

**Purpose**: Define standard routes with distances and fuel consumption

**Navigation**: Fleet MS > Masters > Trip Routes

**Steps:**
1. Create new route
2. Enter route name (e.g., "Dar es Salaam to Kampala")
3. Select starting point
4. Select ending point
5. Add route steps in the "Trip Steps" table:
   - Location
   - Distance (km)
   - Fuel consumption quantity
   - Location type
   - Mark if border crossing
6. System automatically calculates:
   - Total distance
   - Total fuel consumption
7. Save

**Why**: Pre-defined routes ensure consistency and accurate fuel/cost estimation

---

### 4. Cargo Types

**Purpose**: Categorize different types of cargo

**Navigation**: Fleet MS > Masters > Cargo Types

**Steps:**
1. Create new cargo type
2. Enter cargo type name (e.g., "Container", "Loose Cargo", "Bulk")
3. Add description
4. Save

**Why**: Helps in organizing and reporting on different cargo categories

---

### 5. Truck Registration

**Purpose**: Register and manage all trucks in the fleet

**Navigation**: Fleet MS > Masters > Truck

**Steps:**
1. Create new Truck
2. **Overview Tab:**
   - License Plate Number
   - Truck Number (unique identifier)
   - Make (e.g., Scania, Volvo)
   - Model
   - Manufacturing Year
   - Acquisition Year
   - Status (Available, On Trip, Under Maintenance, etc.)
   
3. **Details Tab:**
   - Fuel Type (Diesel, Petrol)
   - Fuel UOM (Liters, Gallons)
   - Engine Number
   - Chassis Number
   - Odometer Value
   
4. **Transport Section:**
   - Maintain Stock: Check if you want to track fuel stock for this truck
   - Fuel Warehouse: Select warehouse (if maintain stock is checked)
   - Current Driver: Assign default driver
   - Current Trip: Auto-populated when truck is on trip

5. **Image**: Upload truck photo
6. Save

**Important Notes:**
- Truck Number must be unique
- If "Maintain Stock" is checked, a fuel warehouse must be selected
- Truck status automatically updates when assigned to trips
- Disabled trucks won't appear in active selections

---

### 6. Trailer Registration

**Purpose**: Register trailers that can be attached to trucks

**Navigation**: Fleet MS > Masters > Trailer

**Steps:**
1. Create new Trailer
2. Enter trailer details:
   - Trailer Number (unique)
   - License Plate
   - Make and Model
   - Manufacturing Year
   - Trailer Type (Flatbed, Container, Tanker, etc.)
   - Status
3. Save

---

### 7. Driver Registration

**Purpose**: Register and manage all drivers

**Navigation**: Fleet MS > Masters > Driver

**Steps:**
1. Create new Truck Driver
2. Enter driver details:
   - Driver Name
   - License Number
   - License Categories (A, B, C, D, E)
   - License Expiry Date
   - Phone Number
   - Email
   - Status (Active, Inactive, On Leave)
3. Upload driver photo and license documents
4. Save

**Important**: Only active drivers appear in trip assignment selections

---

### 8. Fixed Expenses

**Purpose**: Define recurring expenses for trips

**Navigation**: Fleet MS > Masters > Fixed Expenses

**Steps:**
1. Create new Fixed Expense
2. Enter description (e.g., "Border Crossing Fee", "Toll Charges")
3. Select currency
4. Enter fixed value
5. Select expense account (from configured account groups)
6. Select cash/bank account
7. Save

**Why**: These expenses are automatically added to trips based on route configuration

---

## Complete Workflow Guide

### Workflow Overview

```
Transportation Order → Cargo Registration → Manifest → Trip → Delivery → Invoice
```

### Phase 1: Transportation Order Creation

**Purpose**: Create a transportation request from a customer

**Navigation**: Fleet MS > Transactions > Transportation Order

**Steps:**

1. **Create New Transportation Order**
   - Click "New"
   - System auto-generates order number (TORD-YYYY-####)

2. **Order Details Section:**
   - **Date**: Order date
   - **Customer**: Select customer
   - **Transport Type**: Cross Border or Internal
   - **Company**: Your company

3. **Cargo Location & Destination:**
   - **Cargo Location Country**: Origin country
   - **Cargo Location City**: Origin city
   - **Cargo Destination Country**: Destination country
   - **Cargo Destination City**: Destination city

4. **Consignee and Shipper:**
   - **Consignee**: Final receiver of goods
   - **Shipper**: Sender of goods

5. **Border & Transportation Instructions:**
   - **Border 1/2/3 Clearing Agent**: If crossing borders
   - **Special Instructions**: Any special handling requirements

6. **Cargo Information:**
   - **Cargo Type**: Container or Loose Cargo
   - **Goods Description**: What is being transported
   - **Cargo Description**: Detailed description
   - **Amount**: Quantity
   - **Unit**: Unit of measure

7. **References (if applicable):**
   - **Reference DocType**: Link to source document (e.g., Clearing File)
   - **Reference DocName**: Source document name
   - **File Number**: Related file number

8. **Save** the Transportation Order

**What Happens Next:**
- Order is created in draft status
- Ready for cargo assignment
- Can be linked to from other modules (like Clearing)

---

### Phase 2: Cargo Assignment

**Purpose**: Assign specific cargo details and allocate transport resources

**From Transportation Order:**

1. **Assign Transport Table:**
   - Click "Add Row" in the Assign Transport child table
   - **Cargo**: Link to cargo registration (if exists)
   - **Container Number**: For containerized cargo
   - **Amount**: Quantity to transport
   - **Units**: Unit of measure
   - **Expected Loading Date**: When cargo will be loaded
   - **Route**: Select pre-defined route
   - **Transporter Type**:
     - In House: Using your own trucks
     - Sub-Contractor: Using external transporter

2. **For In-House Transport:**
   - **Assigned Vehicle**: Select truck
   - **Assigned Trailer**: Select trailer (if needed)
   - **Assigned Driver**: Select driver
   - **Vehicle Plate Number**: Auto-filled
   - **Trailer Plate Number**: Auto-filled
   - **Driver Name**: Auto-filled

3. **For Sub-Contractor:**
   - **Sub Contractor**: Select supplier
   - **Vehicle Plate Number**: Enter manually
   - **Trailer Plate Number**: Enter manually
   - **Driver Name**: Enter manually
   - **Passport Number**: Driver's passport

4. **Pricing:**
   - **Currency**: Select currency
   - **Rate**: Transportation rate
   - **Amount**: Auto-calculated (quantity × rate)

5. **Save** the Transportation Order

**Important Notes:**
- You can assign multiple cargo items in one order
- Each row can have different vehicles/routes
- System validates vehicle availability
- Assigned vehicles must not be "On Trip"

---

### Phase 3: Cargo Registration (Alternative Entry Point)

**Purpose**: Register cargo independently before creating transportation orders

**Navigation**: Fleet MS > Transactions > Cargo Registration

**Steps:**

1. **Create New Cargo Registration**
   - **Customer**: Select customer
   - **Company**: Your company
   - **Posting Date**: Registration date

2. **Cargo Details Table:**
   - **Cargo Type**: Container/Loose Cargo
   - **Container Number**: For containers
   - **Seal Number**: Container seal
   - **Container Size**: 20ft, 40ft, etc.
   - **Number of Packages**: Quantity
   - **Weight**: Cargo weight
   - **Bill UOM**: Billing unit
   - **Net Weight (Tonne)**: Weight in tonnes
   - **BL Number**: Bill of Lading number
   - **Cargo Route**: Select route
   - **Expected Loading Date**: Loading date
   - **Expected Offloading Date**: Delivery date
   - **Service Item**: Billable service item
   - **Rate**: Service rate
   - **Currency**: Billing currency

3. **Cargo Location:**
   - **Cargo Location Country**: Origin
   - **Cargo Loading City**: Loading point
   - **Cargo Destination Country**: Destination
   - **Cargo Destination City**: Delivery point

4. **Transporter Assignment:**
   - **Transporter Type**: In House/Sub-Contractor
   - **Assigned Vehicle**: Select truck
   - **Assigned Trailer**: Select trailer
   - **Assigned Driver**: Select driver

5. **Save** the Cargo Registration

**What Happens:**
- Cargo is registered in the system
- Can be linked to manifests
- Can be invoiced separately
- Tracks cargo status through the journey

---

### Phase 4: Manifest Preparation

**Purpose**: Group multiple cargo items for a single trip

**Navigation**: Fleet MS > Trips > Manifest

**Steps:**

1. **Create New Manifest**
   - System auto-generates manifest number (MNFS-####)
   - **Posting Date**: Manifest date

2. **Transporter Details:**
   - **Transporter Type**: In House/Sub-Contractor
   - **Sub Contractor Name**: If using sub-contractor

3. **Vehicle Details:**
   - **Truck**: Select truck
   - **Truck License Plate No**: Auto-filled
   - **Route**: Select route
   - **Route Starting Point**: Auto-filled from route

4. **Driver Details:**
   - **Assigned Driver**: Select driver
   - **Driver Name**: Auto-filled
   - **License Number**: Auto-filled

5. **Trailer Configuration:**
   - **Has Trailers**: Check if using trailers
   - **Trailer 1**: Select first trailer
   - **Trailer 1 Type**: Auto-filled
   - **Trailer 2**: Select second trailer (if applicable)

6. **Add Cargo to Manifest:**
   - **Manifest Cargo Details Table:**
     - **Cargo ID**: Link to cargo registration
     - **Cargo Type**: Container/Loose
     - **Container Number**: Container ID
     - **Seal Number**: Container seal
     - **Number of Packages**: Quantity
     - **Weight**: Total weight
     - **BL Number**: Bill of Lading
     - **Customer Name**: Cargo owner
     - **Expected Loading Date**: Loading date
     - **Expected Offloading Date**: Delivery date

7. **Save** the Manifest

8. **Submit** the Manifest when ready

**What Happens on Submit:**
- If In-House transport:
  - Truck status changes to "On Trip"
  - Truck's current trip is updated
- Manifest is locked for editing
- Ready to create trip

---

### Phase 5: Trip Creation & Management

**Purpose**: Execute and track the actual transportation trip

**Navigation**: Fleet MS > Trips > Trips

**Two Ways to Create a Trip:**

#### Method 1: From Manifest
1. Open the Manifest
2. Click "Create Vehicle Trip" button
3. System creates trip with all manifest details pre-filled

#### Method 2: Manual Creation
1. Create new Trip
2. Fill in all details manually

**Trip Details:**

1. **Trip Information:**
   - **Naming Series**: Auto-generated (TRIP-###)
   - **Manifest**: Link to manifest
   - **Transporter Type**: In House/Sub-Contractor
   - **Date**: Trip start date
   - **Company**: Your company
   - **Route**: Select route
   - **Round Trip**: Check if it's a round trip

2. **Vehicle & Driver:**
   - **Truck Number**: Select truck
   - **Truck License Plate**: Auto-filled
   - **Assigned Driver**: Select driver
   - **Driver Name**: Auto-filled
   - **Phone Number**: Driver contact
   - **License Number**: Auto-filled

3. **Trailers:**
   - **Trailer 1**: Select trailer
   - **Trailer 2**: If applicable

4. **Trip Planning:**
   - **ETA End of Trip**: Expected arrival
   - **Route Steps**: Auto-populated from route
     - Each step shows location, distance, fuel consumption

5. **For In-House Trips:**
   - **Fuel Management:**
     - **Total Fuel**: Calculated from route
     - **Fuel Stock Out**: Fuel issued
     - **Stock Out Entry**: Link to stock entry

   - **Expenses:**
     - System auto-adds fixed expenses based on route
     - **Requested Fund Accounts Table**: Shows all expenses
     - **Requested Fund Details**: Breakdown of costs

6. **Trip Status:**
   - **Trip Status**: Pending, In Progress, Completed, Cancelled
   - **Location Update**: Track current location

7. **Save** the Trip

8. **Submit** when trip starts

**During the Trip:**
- Update trip status as it progresses
- Add location updates
- Track fuel consumption
- Record any incidents or delays

**Trip Completion:**
1. Update status to "Completed"
2. Record actual fuel consumption
3. Record any additional expenses
4. Submit final trip report

---

### Phase 6: Vehicle Inspection

**Purpose**: Ensure vehicle roadworthiness before and after trips

**Navigation**: Fleet MS > Transactions > Truck and Trailer Inspection

**When to Inspect:**
- Before trip starts
- After trip completes
- Regular scheduled inspections
- After maintenance

**Steps:**

1. **Create from Trip** (Recommended):
   - Open the Trip
   - Click "Make Vehicle Inspection"
   - System pre-fills vehicle and driver details

2. **Or Create Manually:**
   - Create new Vehicle Inspection
   - Select vehicle
   - Select driver

3. **Inspection Sections:**
   - **Truck Brakes**: Check all brake components
   - **Truck Air System**: Air pressure, leaks
   - **Truck Coupling Devices**: Fifth wheel, kingpin
   - **Truck Exhaust System**: Emissions, leaks
   - **Truck Fuel System**: Tanks, lines, leaks
   - **Truck Lighting**: All lights functional
   - **Truck Safe Loading**: Load security
   - **Truck Steering**: Steering mechanism
   - **Truck Suspension**: Springs, shocks
   - **Truck Tires**: Tread depth, pressure
   - **Truck Wheels**: Rims, lug nuts
   - **Truck Windows**: Windshield, mirrors
   - **Truck Wipers**: Functionality
   - **Emergency Equipment**: Fire extinguisher, triangle

4. **For Each Section:**
   - Mark as Satisfactory/Unsatisfactory
   - Add remarks if needed

5. **Inspector Details:**
   - Inspector name
   - Inspection date
   - Signature

6. **Save and Submit**

**What Happens:**
- If unsatisfactory items found, vehicle may be flagged for maintenance
- Inspection record is maintained for compliance
- Can be printed for regulatory requirements

---

### Phase 7: Invoicing

**Purpose**: Bill customers for transportation services

**Two Approaches:**

#### Approach 1: From Transportation Order

1. Open Transportation Order
2. Select cargo rows to invoice (checkbox selection)
3. Click "Create Invoice" button
4. System creates Sales Invoice with:
   - Customer details
   - Service items from cargo
   - Quantities and rates
   - Accounting dimensions (if configured)
   - Taxes (auto-calculated)

#### Approach 2: From Cargo Registration

1. Open Cargo Registration
2. Select cargo rows to invoice
3. Click "Create Invoice" button
4. System creates Sales Invoice

**Invoice Details:**
- **Customer**: From order/cargo
- **Posting Date**: Current date
- **Currency**: From cargo/order
- **Items**: Transportation services
  - Item Code: Service item
  - Quantity: From cargo
  - Rate: From cargo
  - Amount: Auto-calculated
- **Taxes**: Auto-applied based on customer
- **Accounting Dimensions**: Auto-filled (Truck, Route, etc.)

**After Invoice Creation:**
- Invoice reference is saved back to cargo/order
- Can track which cargo has been invoiced
- Submit invoice to post to accounts

---

### Phase 8: Fuel Management

**Purpose**: Track and manage fuel consumption and requests

#### Fuel Requests

**Navigation**: Fleet MS > Transactions > Fuel Requests

**Steps:**

1. **Create New Fuel Request**
   - **Truck**: Select truck
   - **Driver**: Select driver
   - **Route**: Select route
   - **Trip Reference**: Link to trip (if applicable)

2. **Fuel Details:**
   - **Fuel Type**: Diesel/Petrol
   - **Quantity Requested**: Liters/Gallons
   - **UOM**: Unit of measure
   - **Estimated Cost**: Expected cost
   - **Fuel Station**: Where to refuel

3. **Approval:**
   - **Request Status**: Pending/Approved/Rejected
   - **Approved By**: Approver name
   - **Approved Quantity**: May differ from requested

4. **Save and Submit**

**What Happens:**
- Request is logged
- Can be approved/rejected
- Tracks fuel consumption per truck/trip
- Integrates with fuel expense reports

#### Fuel Stock Management (If Maintain Stock is Enabled)

**For Trucks with Fuel Warehouses:**

1. **Stock Out (Fuel Issue):**
   - When trip starts, create Stock Entry
   - **Purpose**: Material Issue
   - **From Warehouse**: Truck's fuel warehouse
   - **Item**: Fuel item
   - **Quantity**: Fuel issued for trip
   - Link to Trip

2. **Stock In (Fuel Refill):**
   - When refueling, create Stock Entry
   - **Purpose**: Material Receipt
   - **To Warehouse**: Truck's fuel warehouse
   - **Item**: Fuel item
   - **Quantity**: Fuel added
   - **Rate**: Fuel cost

3. **Fuel Balance:**
   - Check truck's fuel warehouse stock balance
   - Tracks fuel consumption per truck
   - Helps in fuel theft detection

---

## Reports & Analytics

### 1. Trips Report

**Purpose**: Overview of all trips with key metrics

**Navigation**: Fleet MS > Reports > Trips Report

**Filters:**
- Date Range
- Truck
- Driver
- Route
- Trip Status
- Transporter Type

**Columns:**
- Trip ID
- Date
- Truck
- Driver
- Route
- Status
- Distance
- Fuel Consumed
- Customer
- Invoice

**Use Cases:**
- Monitor trip completion rates
- Analyze driver performance
- Track vehicle utilization
- Identify delays

---

### 2. Fuel Expense by Trip

**Purpose**: Analyze fuel consumption and costs per trip

**Navigation**: Fleet MS > Reports > Fuel Expense by Trip

**Filters:**
- Date Range
- Truck
- Route
- Trip

**Columns:**
- Trip ID
- Truck
- Route
- Total Distance
- Fuel Consumed
- Fuel Cost
- Cost per KM
- Variance from Standard

**Use Cases:**
- Identify fuel inefficiencies
- Compare actual vs. planned consumption
- Detect fuel theft
- Optimize routes for fuel efficiency

---

### 3. Trip Report and Expenses

**Purpose**: Comprehensive trip analysis with all expenses

**Navigation**: Fleet MS > Reports > Trip Report and Expenses

**Filters:**
- Date Range
- Truck
- Driver
- Customer

**Columns:**
- Trip Details
- Revenue (Invoice Amount)
- Fuel Expenses
- Fixed Expenses
- Other Expenses
- Total Expenses
- Profit/Loss
- Profit Margin %

**Use Cases:**
- Profitability analysis per trip
- Cost control
- Pricing decisions
- Performance evaluation

---

## Integration with Clearing App

### Overview

The VSD Fleet MS app integrates seamlessly with the Clearing app to provide end-to-end logistics management from customs clearance to final delivery.

### Integration Points

#### 1. Clearing File → Transportation Order

**Scenario**: Customer has imported goods that need customs clearance and transportation

**Workflow:**

1. **Create Clearing File** (in Clearing app)
   - Customer: Importer
   - Mode of Transport: Sea/Air/Road
   - Cargo Details: Containers, weight, etc.
   - Arrival Date: ETA
   - Destination: Final delivery location

2. **Automatic Transportation Order Creation:**
   - When clearing file status changes to specific status
   - Or manually create from Clearing File
   - System creates Transportation Order with:
     - Reference DocType: "Clearing File"
     - Reference DocName: Clearing file number
     - Customer: From clearing file
     - Cargo Location: Port/Airport (from clearing file)
     - Cargo Destination: From clearing file
     - File Number: Clearing file number

3. **Link Maintained:**
   - Transportation Order references Clearing File
   - Can navigate between documents
   - Status updates can be synchronized

**Code Reference:**
```python
# From clearing file, create transport order
frappe.call({
    method: "vsd_fleet_ms.vsd_fleet_ms.doctype.transportation_order.transportation_order.create_transport_order",
    args: {
        reference_doctype: "Clearing File",
        reference_docname: clearing_file.name,
        file_number: clearing_file.name,
        customer: clearing_file.customer,
        cargo_location_country: clearing_file.cargo_country_of_origin,
        cargo_destination_country: "Tanzania", // or from clearing file
        transport_type: "Cross Border" // or "Internal"
    }
})
```

#### 2. Container Tracking Integration

**Scenario**: Track containers from port clearance through delivery

**Workflow:**

1. **Clearing File has Container Details:**
   - Container numbers
   - Seal numbers
   - Container sizes
   - BL numbers

2. **Create Cargo Registration:**
   - Link to Clearing File
   - Import container details
   - Container Number: From clearing
   - Seal Number: From clearing
   - BL Number: From clearing

3. **Manifest Creation:**
   - Add containers from cargo registration
   - System tracks container movement
   - Updates clearing file when delivered

4. **Status Synchronization:**
   - When trip status = "Completed"
   - Update clearing file status
   - Mark containers as "Delivered"

#### 3. Document Flow

**Complete Integrated Workflow:**

```
Clearing File (Clearing App)
    ↓
Transportation Order (Fleet MS)
    ↓
Cargo Registration (Fleet MS)
    ↓
Manifest (Fleet MS)
    ↓
Trip (Fleet MS)
    ↓
Delivery Confirmation
    ↓
Update Clearing File Status (Clearing App)
    ↓
Invoice Customer (Both Apps)
```

#### 4. Shared Data Elements

**Common Fields Between Apps:**

| Field | Clearing File | Transportation Order | Cargo Registration |
|-------|---------------|---------------------|-------------------|
| Customer | ✓ | ✓ | ✓ |
| Container Number | ✓ | ✓ | ✓ |
| BL Number | ✓ | ✓ | ✓ |
| Seal Number | ✓ | ✓ | ✓ |
| Origin Country | ✓ | ✓ | ✓ |
| Destination | ✓ | ✓ | ✓ |
| Cargo Description | ✓ | ✓ | ✓ |
| Weight | ✓ | - | ✓ |
| Arrival Date | ✓ | - | ✓ |

#### 5. Implementation Steps for Integration

**Step 1: Configure Transport Settings**
1. Go to Transport Settings
2. Configure accounting dimensions
3. Set up expense accounts
4. Configure fuel warehouses

**Step 2: Create Custom Fields (if needed)**
```javascript
// Add clearing_file link field to Transportation Order
{
    fieldname: "clearing_file",
    fieldtype: "Link",
    options: "Clearing File",
    label: "Clearing File"
}
```

**Step 3: Create Server Script for Auto-Creation**
```python
# Server Script: Clearing File - After Save
if doc.status == "Cleared" and not doc.transport_reference:
    transport_order = frappe.get_doc({
        "doctype": "Transportation Order",
        "reference_doctype": "Clearing File",
        "reference_docname": doc.name,
        "customer": doc.customer,
        "file_number": doc.name,
        "cargo_location_country": doc.cargo_country_of_origin,
        "cargo_destination_country": "Tanzania",
        "transport_type": "Internal"
    })
    transport_order.insert()
    doc.db_set("transport_reference", transport_order.name)
```

**Step 4: Create Button in Clearing File**
```javascript
// Clearing File JS
frappe.ui.form.on('Clearing File', {
    refresh: function(frm) {
        if (frm.doc.status == "Cleared" && !frm.doc.transport_reference) {
            frm.add_custom_button(__('Create Transport Order'), function() {
                frappe.call({
                    method: "vsd_fleet_ms.vsd_fleet_ms.doctype.transportation_order.transportation_order.create_transport_order",
                    args: {
                        reference_doctype: "Clearing File",
                        reference_docname: frm.doc.name,
                        customer: frm.doc.customer,
                        file_number: frm.doc.name
                    },
                    callback: function(r) {
                        if (r.message) {
                            frappe.set_route('Form', 'Transportation Order', r.message);
                        }
                    }
                });
            });
        }
    }
});
```

#### 6. Best Practices for Integration

1. **Use Reference Fields:**
   - Always link documents using reference_doctype and reference_docname
   - Maintains data integrity
   - Enables easy navigation

2. **Status Synchronization:**
   - Update clearing file status when trip is completed
   - Use document events (on_submit, on_update)
   - Avoid circular updates

3. **Data Validation:**
   - Validate that clearing file exists before creating transport order
   - Check that containers are not already assigned
   - Verify customer matches

4. **Permissions:**
   - Ensure users have access to both apps
   - Configure role permissions appropriately
   - Use ignore_permissions=True in automated scripts

5. **Error Handling:**
   - Wrap API calls in try-except blocks
   - Log errors for debugging
   - Show user-friendly error messages

---

## Best Practices

### 1. Master Data Management

**Do's:**
- ✓ Keep truck and driver information up-to-date
- ✓ Regularly update vehicle status
- ✓ Maintain accurate odometer readings
- ✓ Set up routes before creating trips
- ✓ Define all fixed expenses upfront

**Don'ts:**
- ✗ Don't delete trucks/drivers with historical data
- ✗ Don't skip vehicle inspections
- ✗ Don't assign vehicles that are "On Trip"
- ✗ Don't modify routes that have active trips

### 2. Trip Management

**Do's:**
- ✓ Create manifests before trips
- ✓ Submit trips when they actually start
- ✓ Update trip status regularly
- ✓ Record actual fuel consumption
- ✓ Complete vehicle inspections

**Don'ts:**
- ✗ Don't submit trips without fuel stock out (for in-house)
- ✗ Don't skip manifest creation
- ✗ Don't forget to update trip status
- ✗ Don't leave trips in "Pending" status indefinitely

### 3. Financial Management

**Do's:**
- ✓ Invoice customers promptly after delivery
- ✓ Track all expenses per trip
- ✓ Reconcile fuel consumption regularly
- ✓ Use accounting dimensions for better reporting
- ✓ Review trip profitability reports

**Don'ts:**
- ✗ Don't create invoices without submitting trips
- ✗ Don't skip expense recording
- ✗ Don't ignore fuel variances
- ✗ Don't forget to configure accounting dimensions

### 4. Fuel Management

**Do's:**
- ✓ Enable "Maintain Stock" for better fuel tracking
- ✓ Create fuel warehouses per truck
- ✓ Record all fuel refills
- ✓ Compare actual vs. planned consumption
- ✓ Investigate significant variances

**Don'ts:**
- ✗ Don't skip fuel stock entries
- ✗ Don't ignore negative fuel balances
- ✗ Don't forget to update fuel prices
- ✗ Don't mix fuel types in same warehouse

### 5. Reporting & Analytics

**Do's:**
- ✓ Run reports regularly
- ✓ Analyze trip profitability
- ✓ Monitor vehicle utilization
- ✓ Track driver performance
- ✓ Use filters to focus on specific periods/vehicles

**Don'ts:**
- ✗ Don't ignore report insights
- ✗ Don't skip variance analysis
- ✗ Don't forget to export reports for management
- ✗ Don't overlook trends in data

### 6. Integration with Clearing

**Do's:**
- ✓ Link clearing files to transport orders
- ✓ Synchronize container information
- ✓ Update statuses in both systems
- ✓ Use consistent customer records
- ✓ Maintain document references

**Don'ts:**
- ✗ Don't create duplicate records
- ✗ Don't break document links
- ✗ Don't skip status updates
- ✗ Don't ignore integration errors

---

## Troubleshooting

### Common Issues and Solutions

#### Issue 1: Cannot Assign Vehicle to Trip
**Symptom**: Error "Vehicle is in trip"
**Solution**:
- Check vehicle status
- Complete or cancel existing trip
- Update vehicle status to "Available"

#### Issue 2: Fuel Stock Entry Not Created
**Symptom**: Trip submitted but no stock entry
**Solution**:
- Check if "Maintain Stock" is enabled for truck
- Verify fuel warehouse is set
- Check fuel item in Transport Settings
- Ensure sufficient fuel stock

#### Issue 3: Invoice Not Creating
**Symptom**: Create Invoice button doesn't work
**Solution**:
- Save the document first
- Select cargo rows to invoice
- Check if rows already invoiced
- Verify customer has default currency

#### Issue 4: Route Steps Not Showing
**Symptom**: Route selected but steps not populated
**Solution**:
- Check if route has trip steps defined
- Verify route is saved properly
- Refresh the form
- Re-select the route

#### Issue 5: Accounting Dimensions Not Applied
**Symptom**: Dimensions not showing in invoice
**Solution**:
- Check Transport Settings configuration
- Verify dimension mapping is correct
- Ensure source fields have values
- Check target doctype has dimension fields

---

## Conclusion

The VSD Fleet Management System provides a comprehensive solution for managing transportation operations. By following this guide:

1. **Setup**: Configure settings and master data properly
2. **Operations**: Follow the workflow from order to delivery
3. **Integration**: Leverage integration with Clearing app for complete logistics management
4. **Monitoring**: Use reports to track performance and profitability
5. **Best Practices**: Follow recommended practices for optimal results

### Getting Help

- **Documentation**: Refer to this guide
- **GitHub**: https://github.com/VVSD-LTD/vsd_fleet_ms
- **Support**: <EMAIL>
- **Community**: Frappe Forum

### Next Steps

1. Complete initial setup and configuration
2. Import master data (trucks, drivers, routes)
3. Create test transportation order
4. Run through complete workflow
5. Generate and review reports
6. Configure integration with Clearing app (if applicable)
7. Train users on the system
8. Go live!

---

**Document Version**: 1.0
**Last Updated**: 2025-01-04
**Author**: VV SYSTEMS DEVELOPER LTD
**License**: MIT

