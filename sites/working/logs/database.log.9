2025-09-21 16:49:05,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIF<PERSON> `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0
2025-09-21 16:49:05,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-09-21 16:50:15,014 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-21 16:50:15,802 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-21 16:50:16,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-21 16:50:18,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-21 16:50:18,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-09-21 16:50:19,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-21 16:50:20,128 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-21 16:50:24,030 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-09-21 17:12:58,237 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-21 17:12:59,298 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-21 17:13:00,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-21 17:13:02,532 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-21 17:13:02,908 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-09-21 17:13:04,421 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-21 17:13:04,659 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-09-21 17:13:09,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-09-21 17:21:34,313 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-21 17:21:35,676 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-21 17:21:37,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-21 17:21:40,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-21 17:21:40,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-21 17:21:42,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-21 17:21:42,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-09-21 17:21:47,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0
2025-09-21 18:02:52,222 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-21 18:02:53,978 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-21 18:02:56,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-21 18:02:58,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-21 18:02:59,458 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-21 18:03:12,834 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0
2025-09-21 18:10:41,871 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-21 18:10:43,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-21 18:10:45,309 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-21 18:10:51,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-09-21 18:15:36,362 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-09-21 18:20:18,379 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-21 18:35:52,433 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-21 18:35:53,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-21 18:35:54,379 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-21 18:35:55,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-09-21 18:35:59,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-09-22 08:44:29,565 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-22 08:44:31,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-22 08:44:32,888 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-22 08:44:38,866 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0
2025-09-28 18:57:38,020 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-28 18:57:39,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-28 18:57:40,676 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-28 18:57:44,445 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-09-28 18:57:44,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-28 18:57:51,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-09-28 19:16:45,873 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-09-28 19:16:47,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-09-28 19:16:48,251 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-09-28 19:16:51,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-28 19:16:51,866 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-09-28 19:16:54,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-09-28 19:16:55,205 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-09-28 19:17:03,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-09-28 21:14:10,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `custom_clearing_file` varchar(140)
2025-09-28 21:14:10,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0
2025-09-28 21:14:10,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0
2025-09-28 21:15:15,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice`
				CHANGE COLUMN `custom_clearing_file`
				`clearing_file`
				varchar(140)
2025-10-01 10:10:49,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabCF Delivery Note` ADD COLUMN `container_interchange` varchar(140)
2025-10-01 12:32:44,879 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-01 12:32:45,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-01 12:32:47,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-01 12:32:48,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-10-01 12:32:49,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-01 12:32:49,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-01 12:32:51,225 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-01 12:32:51,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-10-01 12:32:56,577 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0
2025-10-01 12:36:26,995 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-01 12:36:27,902 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-01 12:36:29,496 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-01 12:36:30,852 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-01 12:36:31,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-01 12:36:31,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-01 12:36:31,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-01 12:36:32,134 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-01 12:36:32,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-10-01 12:36:33,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-01 12:36:33,793 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-01 12:36:38,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-10-01 12:47:49,057 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-01 12:47:50,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-01 12:47:51,208 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-01 12:47:52,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-01 12:47:53,222 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-10-01 12:47:54,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-01 12:47:54,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-01 12:47:58,562 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0
2025-10-01 12:50:36,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0
2025-10-01 16:01:29,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` ADD COLUMN `posting_date` date
2025-10-01 16:01:29,502 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-01 16:01:39,161 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-01 16:02:22,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-01 16:03:22,234 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-01 16:04:04,379 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-01 16:37:31,414 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parent varchar(140)
2025-10-01 16:37:31,415 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parenttype varchar(140)
2025-10-01 16:37:31,416 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parentfield varchar(140)
2025-10-01 16:37:31,517 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` ADD COLUMN `container_type` varchar(140)
2025-10-01 16:37:31,538 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `volume` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-10-01 16:37:48,668 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parent varchar(140)
2025-10-01 16:37:48,668 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parenttype varchar(140)
2025-10-01 16:37:48,669 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parentfield varchar(140)
2025-10-01 16:37:48,747 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `volume` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0
2025-10-01 16:39:42,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing File` ADD COLUMN `total_container_summary` varchar(140)
2025-10-01 17:29:36,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `customs_value_fobcif` int(11) not null default 0
2025-10-01 17:29:36,943 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-01 17:52:10,359 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parent varchar(140)
2025-10-01 17:52:10,361 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parenttype varchar(140)
2025-10-01 17:52:10,361 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parentfield varchar(140)
2025-10-01 17:52:10,440 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-01 18:01:46,741 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parent varchar(140)
2025-10-01 18:01:46,742 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parenttype varchar(140)
2025-10-01 18:01:46,742 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parentfield varchar(140)
2025-10-01 18:01:46,839 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-02 08:57:10,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabParty Specific Item` ADD COLUMN `select` varchar(140)
2025-10-02 10:34:44,502 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-02 10:34:45,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-02 10:34:46,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabParty Specific Item` MODIFY `select` varchar(140) default 'Inclusive'
2025-10-02 10:34:46,667 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-02 10:34:48,044 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0
2025-10-02 10:34:48,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-02 10:34:48,448 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-02 10:34:48,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-02 10:34:48,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-02 10:34:49,387 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-02 10:34:49,719 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `volume` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-10-02 10:34:50,007 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-02 10:34:50,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-10-02 10:34:51,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-02 10:34:51,819 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-02 10:34:55,788 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-10-02 10:56:47,734 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value_fobcif` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-02 11:04:32,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `container_deposit_date` date
2025-10-02 11:04:33,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-02 11:05:48,026 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `eir_date` date
2025-10-02 11:05:48,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` MODIFY `amount` decimal(21,9) not null default 0
2025-10-02 11:20:08,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value_fobcif` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-02 11:43:15,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` MODIFY `amount` decimal(21,9) not null default 0
2025-10-02 11:44:36,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` MODIFY `amount` decimal(21,9) not null default 0
2025-10-02 11:53:44,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabCF Delivery Note` ADD COLUMN `interchange_date_and_time` datetime(6)
2025-10-02 11:56:18,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `refund_date` date, ADD COLUMN `refund_amount` decimal(21,9) not null default 0
2025-10-02 12:56:59,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `payment_status` varchar(140)
2025-10-02 12:56:59,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0
2025-10-02 13:01:47,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0
2025-10-02 13:05:12,597 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-02 13:05:13,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-02 13:05:14,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-02 13:05:16,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0
2025-10-02 13:05:17,709 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value_fobcif` decimal(21,9) not null default 0
2025-10-02 13:05:18,563 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-02 13:05:18,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` MODIFY `refund_amount` decimal(21,9) not null default 0
2025-10-02 13:05:19,414 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-02 13:05:19,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-02 13:05:21,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-02 13:05:21,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-02 13:05:27,033 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-10-02 13:13:42,096 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `total` decimal(21,9) not null default 0
2025-10-02 13:13:42,120 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-10-02 13:14:24,999 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-10-02 13:15:50,037 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-02 13:15:51,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-02 13:15:52,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-02 13:15:54,441 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0
2025-10-02 13:15:55,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-02 13:15:55,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-02 13:15:57,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-02 13:15:57,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-10-02 13:16:02,110 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-10-02 13:19:24,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-10-02 13:20:35,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
2025-10-02 13:45:40,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `customs_value` decimal(21,9) not null default 0
2025-10-02 13:45:40,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-02 15:49:33,551 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-02 15:49:34,436 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-02 15:49:35,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-02 15:49:36,861 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0
2025-10-02 15:49:37,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-02 15:49:37,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-02 15:49:37,909 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-02 15:49:39,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-02 15:49:39,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-10-02 15:49:43,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-10-02 16:06:27,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0
2025-10-02 16:07:22,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0
2025-10-02 16:09:17,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0
2025-10-02 16:18:24,222 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `invoice_status` varchar(140)
2025-10-02 16:18:24,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-10-02 16:34:13,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
2025-10-02 16:36:22,416 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `amended_from` varchar(140)
2025-10-02 16:36:22,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
2025-10-02 16:36:22,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD INDEX `amended_from_index`(`amended_from`)
2025-10-02 16:36:36,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0
2025-10-02 17:33:59,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-10-02 17:38:05,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0
2025-10-02 17:51:25,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `workflow_state` varchar(140)
2025-10-02 17:51:25,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0
2025-10-02 18:00:41,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-10-02 18:04:23,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0
2025-10-02 18:05:17,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0
2025-10-02 18:06:19,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-10-02 18:08:33,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-10-02 18:09:54,593 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `total_clearing_charges` decimal(21,9) not null default 0
2025-10-02 18:09:54,613 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0
2025-10-03 09:47:37,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-10-03 10:07:58,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0
2025-10-03 10:08:26,759 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice`
				CHANGE COLUMN `clearing_file`
				`clearing_charges`
				varchar(140)
2025-10-03 10:18:58,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0
2025-10-03 10:23:29,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
2025-10-03 10:27:59,759 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-10-03 10:32:18,536 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-10-03 10:33:53,868 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0
2025-10-03 11:46:55,457 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-03 11:46:56,489 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-03 11:46:57,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-03 11:46:59,146 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0
2025-10-03 11:46:59,673 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-03 11:46:59,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-03 11:47:01,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-03 11:47:01,450 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-03 11:47:06,196 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `include_in_payroll_summary` int(1) not null default 0
2025-10-03 11:47:06,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-10-03 11:47:06,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0
2025-10-03 12:59:41,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-03 16:14:49,107 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-03 16:14:50,170 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-03 16:14:51,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-03 16:14:53,271 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-03 16:14:53,589 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-03 16:14:54,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-03 16:14:54,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-03 16:14:55,956 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-03 16:14:56,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-03 16:15:00,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-10-03 17:25:45,108 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-03 17:25:46,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-03 17:25:47,346 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-03 17:25:49,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-03 17:25:49,330 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-03 17:25:50,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-03 17:25:50,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-10-03 17:25:58,124 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0
2025-10-07 16:38:50,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-10-07 16:39:43,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-07 16:43:56,106 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-07 16:43:56,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` MODIFY `link_filters` json
2025-10-07 16:43:57,084 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` MODIFY `rows_threshold_for_grid_search` int(11) not null default 20
2025-10-07 16:43:58,177 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-07 16:43:58,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Taxes and Charges` ADD COLUMN `project` varchar(140)
2025-10-07 16:43:58,755 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Taxes and Charges` MODIFY `base_tax_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-10-07 16:43:59,147 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule` ADD COLUMN `project` varchar(140)
2025-10-07 16:43:59,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule` MODIFY `shipping_amount` decimal(21,9) not null default 0
2025-10-07 16:43:59,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Taxes and Charges` ADD COLUMN `project` varchar(140)
2025-10-07 16:43:59,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Taxes and Charges` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `tax_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_tax_amount` decimal(21,9) not null default 0, MODIFY `tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `base_tax_amount_after_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-10-07 16:43:59,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Program` ADD COLUMN `project` varchar(140)
2025-10-07 16:43:59,746 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Program` MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-10-07 16:43:59,972 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `reference_number` text, MODIFY `withdrawal` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `deposit` decimal(21,9) not null default 0
2025-10-07 16:44:00,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Guarantee` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `charges` decimal(21,9) not null default 0, MODIFY `margin_money` decimal(21,9) not null default 0
2025-10-07 16:44:00,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` ADD COLUMN `project` varchar(140)
