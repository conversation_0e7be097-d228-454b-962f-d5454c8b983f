2025-10-27 12:56:02,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:56:02,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 13:08:16,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 13:18:15,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 13:18:27,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:18:53,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:20:22,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:23:07,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_paid_by_consignee` decimal(21,9) not null default 0
2025-10-27 13:23:07,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 13:24:35,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:24:35,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 13:27:16,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:29:53,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:33:04,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_paid` decimal(21,9) not null default 0
2025-10-27 13:33:04,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:00:20,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 14:03:31,750 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 14:03:47,861 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 14:05:23,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `total_paid` decimal(21,9) not null default 0
2025-10-27 14:05:23,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:06:07,852 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 14:08:13,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `total_paid_by_consignee` decimal(21,9) not null default 0
2025-10-27 14:08:13,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:09:18,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `total_paid` decimal(21,9) not null default 0
2025-10-27 14:09:18,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0
2025-10-27 14:10:52,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:11:35,477 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 14:18:55,888 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` ADD COLUMN `total_charges_paid_by_consignee` decimal(21,9) not null default 0, ADD COLUMN `total_paid` decimal(21,9) not null default 0
2025-10-27 14:18:55,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:56:34,449 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 14:57:05,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 14:57:39,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:58:24,641 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges_paid_by_consignee` decimal(21,9) not null default 0
2025-10-27 15:03:03,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` ADD COLUMN `paid_by` decimal(21,9) not null default 0
2025-10-27 15:03:04,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 15:03:31,261 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `paid_by` decimal(21,9) not null default 0
2025-10-27 15:03:31,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 15:04:32,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `paid_by` decimal(21,9) not null default 0
2025-10-27 15:04:32,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 15:05:06,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `paid_by` decimal(21,9) not null default 0
2025-10-27 15:05:06,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 16:20:19,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 16:20:49,536 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 16:21:19,662 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 16:21:38,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 17:23:49,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `awbbl_no` varchar(140)
2025-10-27 17:23:49,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-10-27 17:25:19,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 17:31:21,524 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-27 17:31:22,570 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-27 17:31:23,742 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-27 17:31:25,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 17:31:25,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 17:31:26,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-10-27 17:31:26,312 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 17:31:26,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 17:31:26,858 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 17:31:27,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 17:31:27,193 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 17:31:27,356 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 17:31:27,617 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 17:31:27,916 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 17:31:28,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 17:31:28,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-27 17:31:28,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-27 17:31:30,078 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-27 17:31:30,307 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-27 17:31:35,354 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-10-27 17:32:46,938 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-27 17:32:47,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-27 17:32:48,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-27 17:32:50,030 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-27 17:32:50,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-27 17:32:51,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-27 17:32:51,641 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-27 17:32:55,230 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0
2025-10-27 17:34:49,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `awbbl_no` varchar(140)
2025-10-27 17:34:49,819 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-10-27 17:35:04,316 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-10-27 17:37:17,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `awbbl_no` varchar(140)
2025-10-27 17:37:17,579 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-10-27 17:38:15,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` ADD COLUMN `awbbl_no` varchar(140)
2025-10-27 17:38:15,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-10-28 09:21:11,462 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `balance` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0
2025-10-28 12:41:14,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-28 12:44:50,101 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-28 13:01:46,324 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-28 13:04:07,952 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing File` ADD COLUMN `cleared_date` date
2025-10-28 15:21:10,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `custom_shipping_line_` varchar(140)
2025-10-28 15:21:10,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-28 15:21:10,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-04 14:00:03,599 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-04 14:00:04,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-04 14:00:06,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-04 14:00:07,448 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-04 14:00:07,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-11-04 14:00:07,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-04 14:00:08,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-04 14:00:08,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-04 14:00:08,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-04 14:00:09,094 WARNING database DDL Query made to DB:
create table `tabForeign Import Payment Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_entry` varchar(140),
`payment_date` date,
`payment_amount_foreign` decimal(21,9) not null default 0,
`payment_amount_base` decimal(21,9) not null default 0,
`payment_exchange_rate` decimal(21,9) not null default 0,
`exchange_difference` decimal(21,9) not null default 0,
`journal_entry_created` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-04 14:00:09,562 WARNING database DDL Query made to DB:
create table `tabTrip Sheet Item Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_document_id` varchar(140),
`item_name` varchar(140),
`qty` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-04 14:00:09,675 WARNING database DDL Query made to DB:
create table `tabForeign Import Exchange Difference Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`difference_type` varchar(140),
`amount` decimal(21,2) not null default 0,
`journal_entry` varchar(140),
`posting_date` date,
`remarks` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-04 14:00:09,772 WARNING database DDL Query made to DB:
create table `tabTrip Sheet References` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_document` varchar(140),
`part_name` varchar(140),
`note` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-04 14:00:09,916 WARNING database DDL Query made to DB:
create table `tabForeign Import LCV Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`landed_cost_voucher` varchar(140),
`lcv_date` date,
`lcv_amount_foreign` decimal(21,2) not null default 0,
`lcv_amount_base` decimal(21,9) not null default 0,
`exchange_rate_used` decimal(21,9) not null default 1.0,
`allocated_to_items` decimal(21,9) not null default 0,
`exchange_difference` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-04 14:00:10,061 WARNING database DDL Query made to DB:
create table `tabTrip Sheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`amended_from` varchar(140),
`vehicle` varchar(140),
`transporter_type` varchar(140),
`driver` varchar(140),
`driver_name` varchar(140),
`external_driver_name` varchar(140),
`start_km` decimal(21,9) not null default 0,
`end_km` decimal(21,9) not null default 0,
`fuel_consumed` decimal(21,9) not null default 0,
`status` varchar(140),
`start_location` varchar(140),
`end_location` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-04 14:00:10,285 WARNING database DDL Query made to DB:
create table `tabForeign Import Transaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`purchase_invoice` varchar(140),
`supplier` varchar(140),
`transaction_date` date,
`currency` varchar(140),
`original_exchange_rate` decimal(21,9) not null default 0,
`status` varchar(140) default 'Draft',
`invoice_amount_foreign` decimal(21,9) not null default 0,
`invoice_amount_base` decimal(21,9) not null default 0,
`total_gain_loss` decimal(21,9) not null default 0,
`net_difference` decimal(21,9) not null default 0,
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-04 14:00:11,387 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-04 14:00:11,591 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-04 14:00:12,084 WARNING database DDL Query made to DB:
ALTER TABLE `tabFeedback Form` ADD COLUMN `dynamic_html` varchar(140)
2025-11-04 14:00:17,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier` ADD COLUMN `track_import_exchanges` int(1) not null default 1, ADD COLUMN `preferred_exchange_account` varchar(140)
2025-11-04 14:00:17,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `foreign_import_tracker` varchar(140), ADD COLUMN `exchange_difference_amount` decimal(21,9) not null default 0
2025-11-04 14:00:17,252 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0
2025-11-04 14:00:17,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `foreign_import_tracker` varchar(140), ADD COLUMN `enable_import_tracking` int(1) not null default 1
2025-11-04 14:00:17,364 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0
2025-11-04 14:00:17,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-11-04 14:00:17,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `auto_create_import_tracker` int(1) not null default 1, ADD COLUMN `import_exchange_threshold` decimal(21,9) not null default 1.0
2025-11-04 14:00:17,564 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,2) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-11-04 14:00:17,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` ADD COLUMN `foreign_import_tracker` varchar(140)
2025-11-04 14:00:17,655 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` MODIFY `total_credit` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0
2025-11-04 14:00:17,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `transfer_goods_between_company` varchar(140)
2025-11-04 14:00:17,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-11-04 14:00:18,146 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-11-05 08:59:06,314 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-05 08:59:08,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-05 08:59:08,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `withdrawal` decimal(21,9) not null default 0, MODIFY `deposit` decimal(21,9) not null default 0
2025-11-05 08:59:08,816 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` DROP INDEX `transaction_id`
2025-11-05 08:59:09,230 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` MODIFY `credit_in_account_currency` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `debit_in_account_currency` decimal(21,9) not null default 0, MODIFY `credit` decimal(21,9) not null default 0, MODIFY `debit` decimal(21,9) not null default 0
2025-11-05 08:59:09,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `progress` decimal(21,9) not null default 0, MODIFY `total_billing_amount` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0, MODIFY `task_weight` decimal(21,9) not null default 0
2025-11-05 08:59:10,184 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-05 08:59:10,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0
2025-11-05 08:59:11,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0
2025-11-05 08:59:13,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-05 08:59:13,831 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-05 08:59:15,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-05 08:59:15,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-05 08:59:20,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0
2025-11-05 09:31:48,308 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-05 09:31:49,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-05 09:31:50,383 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-05 09:31:52,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-05 09:31:52,261 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-05 09:31:53,608 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-05 09:31:53,799 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-05 09:31:57,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-11-05 09:45:16,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `container_no` varchar(140)
2025-11-05 09:45:16,585 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-11-05 09:47:30,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-05 09:51:21,797 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parent varchar(140)
2025-11-05 09:51:21,798 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parenttype varchar(140)
2025-11-05 09:51:21,799 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parentfield varchar(140)
2025-11-05 09:51:21,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` ADD COLUMN `port_of_loading` varchar(140)
2025-11-05 09:51:21,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `volume` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0
2025-11-05 09:53:04,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `port_of_loading` varchar(140)
2025-11-05 09:53:04,836 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-11-05 11:54:12,127 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-05 11:54:13,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-05 11:54:14,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-05 11:54:15,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-05 11:54:16,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-05 11:54:17,624 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-05 11:54:17,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-05 11:54:22,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0
2025-11-05 11:55:57,655 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-05 11:55:58,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-05 11:55:59,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-05 11:56:00,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-05 11:56:01,061 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-05 11:56:02,224 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-05 11:56:02,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-05 11:56:06,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-11-05 11:57:58,842 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-05 11:57:59,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-05 11:58:00,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-05 11:58:01,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-11-05 11:58:01,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-05 11:58:01,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-11-05 11:58:02,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-05 11:58:02,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-11-05 11:58:02,493 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-05 11:58:02,602 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-11-05 11:58:02,702 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-11-05 11:58:02,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-05 11:58:02,906 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-05 11:58:03,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-05 11:58:03,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-11-05 11:58:03,577 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-05 11:58:03,774 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-05 11:58:04,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-05 11:58:05,197 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-05 11:58:05,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-05 11:58:09,008 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-11-05 12:13:25,103 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-05 12:13:51,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-11-05 12:15:19,229 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-05 12:15:20,242 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-05 12:15:21,397 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-05 12:15:22,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-11-05 12:15:23,321 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-05 12:15:23,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-05 12:15:25,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-05 12:15:25,251 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-05 12:15:30,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0
2025-11-05 12:16:14,757 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-05 12:16:16,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-05 12:16:18,084 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-05 12:16:21,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-05 12:16:21,981 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-05 12:16:24,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-05 12:16:24,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-05 12:16:31,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-11-05 12:17:19,494 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parent varchar(140)
2025-11-05 12:17:19,495 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parenttype varchar(140)
2025-11-05 12:17:19,496 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parentfield varchar(140)
2025-11-05 12:17:19,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `volume` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0
2025-11-05 12:17:49,155 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parent varchar(140)
2025-11-05 12:17:49,156 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parenttype varchar(140)
2025-11-05 12:17:49,157 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parentfield varchar(140)
2025-11-05 12:17:49,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `volume` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0
2025-11-05 13:11:22,355 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-05 13:11:23,451 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-05 13:11:24,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-05 13:11:26,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `volume` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-11-05 13:11:26,428 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-05 13:11:26,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-05 13:11:27,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-05 13:11:28,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-05 13:11:36,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-11-07 10:01:24,871 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-07 10:01:26,562 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-07 10:01:26,970 WARNING database DDL Query made to DB:
create table `tabProcess Period Closing Voucher` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parent_pcv` varchar(140),
`status` varchar(140) default 'Queued',
`p_l_closing_balance` json,
`bs_closing_balance` json,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-07 10:01:27,102 WARNING database DDL Query made to DB:
create table `tabProcess Period Closing Voucher Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`processing_date` date,
`report_type` varchar(140) default 'Profit and Loss',
`status` varchar(140) default 'Queued',
`closing_balance` json,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-07 10:01:28,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-07 10:01:30,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-07 10:01:30,745 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-07 10:01:32,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-07 10:01:32,560 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-07 10:01:37,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:06:07,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-11-07 11:07:15,145 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parent varchar(140)
2025-11-07 11:07:15,146 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:07:15,147 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:07:15,231 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` ADD COLUMN `journal_entry` varchar(140), ADD COLUMN `disbursed_date` date
2025-11-07 11:07:15,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:07:30,432 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parent varchar(140)
2025-11-07 11:07:30,433 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:07:30,434 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:07:30,519 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:07:47,677 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parent varchar(140)
2025-11-07 11:07:47,677 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:07:47,678 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:07:47,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:08:24,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-07 11:23:51,707 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parent varchar(140)
2025-11-07 11:23:51,708 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:23:51,709 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:23:51,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` ADD COLUMN `journal_entry` varchar(140), ADD COLUMN `disbursed_date` date
2025-11-07 11:23:51,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:24:31,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 11:25:35,191 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parent varchar(140)
2025-11-07 11:25:35,192 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:25:35,193 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:25:35,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges` ADD COLUMN `journal_entry` varchar(140), ADD COLUMN `disbursed_date` date
2025-11-07 11:25:35,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:26:26,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 11:27:48,151 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parent varchar(140)
2025-11-07 11:27:48,152 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:27:48,153 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:27:48,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` ADD COLUMN `journal_entry` varchar(140), ADD COLUMN `disbursed_date` date
2025-11-07 11:27:48,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:28:05,219 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parent varchar(140)
2025-11-07 11:28:05,220 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:28:05,221 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:28:05,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:28:21,460 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parent varchar(140)
2025-11-07 11:28:21,461 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:28:21,461 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:28:21,519 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:28:57,323 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parent varchar(140)
2025-11-07 11:28:57,324 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:28:57,325 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:28:57,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:29:17,051 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parent varchar(140)
2025-11-07 11:29:17,052 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:29:17,053 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:29:17,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:49:18,788 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-07 11:49:20,069 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-07 11:49:21,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-07 11:49:22,564 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-07 11:49:22,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 11:49:22,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-11-07 11:49:23,191 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:49:23,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:49:23,451 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:49:23,631 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 11:49:23,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:49:24,087 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-07 11:49:24,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-07 11:49:25,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-07 11:49:25,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-07 11:49:30,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-11-07 12:05:57,186 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-07 12:05:58,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-07 12:05:59,422 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-07 12:06:00,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 12:06:00,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-11-07 12:06:00,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-07 12:06:01,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-07 12:06:01,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-07 12:06:01,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-07 12:06:03,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-07 12:06:03,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-07 12:06:07,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-11-07 12:16:26,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-11-07 12:16:56,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 12:17:26,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 12:17:52,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-11 10:07:25,656 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-11 10:07:26,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-11 10:07:28,271 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-11 10:07:29,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-11-11 10:07:30,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-11-11 10:07:30,214 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
