2025-10-22 13:35:34,228 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0
2025-10-22 13:35:34,760 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-22 13:35:35,066 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-22 13:35:36,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-22 13:35:36,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-22 13:35:40,968 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-10-22 13:50:43,563 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-22 13:50:44,577 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-22 13:50:45,880 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-22 13:50:48,088 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` ADD COLUMN `invoice_reference_label` varchar(140)
2025-10-22 13:50:48,117 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-22 13:50:48,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Services` ADD COLUMN `reference_number_label` varchar(140)
2025-10-22 13:50:48,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Services` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-10-22 13:50:48,952 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-22 13:50:49,312 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 13:50:50,920 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-22 13:50:51,148 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 13:50:55,856 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-10-22 14:14:39,181 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-22 14:14:40,271 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-22 14:14:41,454 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-22 14:14:42,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-22 14:14:42,927 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Services` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-10-22 14:14:43,345 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-22 14:14:43,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 14:14:44,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-22 14:14:44,925 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-22 14:14:48,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-10-22 14:54:25,202 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-22 14:54:26,484 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-22 14:54:27,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-22 14:54:29,410 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-22 14:54:29,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 14:54:31,126 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-22 14:54:31,330 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-22 14:54:35,641 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0
2025-10-22 15:04:19,939 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-22 15:04:21,598 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-22 15:04:23,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-22 15:04:25,813 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-22 15:04:26,184 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-22 15:04:28,074 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-22 15:04:28,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 15:04:33,694 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-10-22 16:04:13,611 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-22 16:04:14,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-22 16:04:15,659 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-22 16:04:17,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-22 16:04:17,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 16:04:18,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-22 16:04:19,007 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 16:04:22,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0
2025-10-22 16:17:54,214 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-22 16:17:55,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-22 16:17:57,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-22 16:18:00,488 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-22 16:18:00,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 16:18:02,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-22 16:18:03,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 16:18:09,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0
2025-10-22 17:08:31,922 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-22 17:09:04,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-22 17:09:29,563 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-22 17:17:33,802 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` ADD COLUMN `custom_journal_entry` varchar(140)
2025-10-22 17:17:33,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-22 17:17:33,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` ADD COLUMN `custom_posting_date` date
2025-10-22 17:17:33,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-22 17:17:34,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-22 17:18:12,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-22 17:36:17,584 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0
2025-10-22 17:41:54,512 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-22 17:41:55,532 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-22 17:41:56,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-22 17:41:57,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0
2025-10-22 17:41:58,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `inspection_date` datetime(6), MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-22 17:41:58,541 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-22 17:41:58,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 17:42:00,124 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-22 17:42:00,315 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-22 17:42:04,642 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-10-22 17:45:01,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `inspection_date` date, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-22 17:45:35,598 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-22 17:46:00,875 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-22 17:46:12,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-22 17:47:02,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-22 17:48:07,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-22 17:48:34,972 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-22 17:49:06,165 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0
2025-10-22 17:51:39,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-10-22 17:52:24,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0
2025-10-22 17:55:30,868 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0
2025-10-22 17:55:49,719 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0
2025-10-22 17:57:11,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0
2025-10-22 17:57:38,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
2025-10-22 17:59:31,708 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0
2025-10-22 18:02:37,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0
2025-10-23 10:55:10,735 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parent varchar(140)
2025-10-23 10:55:10,737 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parenttype varchar(140)
2025-10-23 10:55:10,739 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parentfield varchar(140)
2025-10-23 10:55:10,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` ADD COLUMN `disbursement` varchar(140), ADD COLUMN `disbursed_date` date
2025-10-23 10:55:10,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-23 10:55:29,600 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parent varchar(140)
2025-10-23 10:55:29,601 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parenttype varchar(140)
2025-10-23 10:55:29,601 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parentfield varchar(140)
2025-10-23 10:55:29,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-23 10:55:41,270 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parent varchar(140)
2025-10-23 10:55:41,271 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parenttype varchar(140)
2025-10-23 10:55:41,272 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parentfield varchar(140)
2025-10-23 10:55:41,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-23 10:56:28,436 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parent varchar(140)
2025-10-23 10:56:28,437 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parenttype varchar(140)
2025-10-23 10:56:28,437 WARNING database DDL Query made to DB:
alter table `tabClearing Charge Detail` add column if not exists parentfield varchar(140)
2025-10-23 10:56:28,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-23 11:06:02,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-23 11:06:24,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-23 11:07:42,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-23 11:13:16,157 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-23 11:13:17,594 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-23 11:13:19,332 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-23 11:13:21,193 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `balance` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0
2025-10-23 11:13:21,440 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-23 11:13:21,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-23 11:13:22,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-23 11:13:22,526 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-23 11:13:22,982 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-23 11:13:23,312 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-23 11:13:25,060 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-23 11:13:25,321 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-23 11:13:31,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0
2025-10-23 11:59:33,283 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-23 11:59:34,260 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-23 11:59:35,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-23 11:59:37,052 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-23 11:59:37,309 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-23 11:59:38,542 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-23 11:59:38,725 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-23 11:59:42,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0
2025-10-23 12:57:33,086 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-23 12:57:34,110 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-23 12:57:35,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-23 12:57:36,531 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0
2025-10-23 12:57:36,752 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `inspection_date` datetime(6), MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-23 12:57:36,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-23 12:57:37,188 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-23 12:57:37,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-23 12:57:38,151 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-23 12:57:38,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-23 12:57:39,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-23 12:57:39,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-23 12:57:44,451 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0
2025-10-23 12:58:45,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `amended_from` varchar(140)
2025-10-23 12:58:45,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` MODIFY `refund_amount` decimal(21,9) not null default 0
2025-10-23 12:58:45,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD INDEX `amended_from_index`(`amended_from`)
2025-10-23 13:28:32,513 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-23 13:28:33,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-23 13:28:34,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-23 13:28:35,806 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` MODIFY `refund_amount` decimal(21,9) not null default 0
2025-10-23 13:28:36,286 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-23 13:28:36,551 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-23 13:28:37,734 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-23 13:28:37,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-23 13:28:42,356 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-10-23 13:51:34,542 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-23 13:51:35,570 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-23 13:51:36,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-23 13:51:38,321 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-23 13:51:38,585 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-23 13:51:39,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-23 13:51:40,059 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-23 13:51:44,194 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-10-23 15:20:17,473 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-23 15:20:18,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-23 15:20:19,282 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Account` MODIFY `bank_account_no` varchar(64), MODIFY `iban` varchar(64)
2025-10-23 15:20:19,563 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `deposit` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `transaction_type` varchar(64), MODIFY `withdrawal` decimal(21,9) not null default 0
2025-10-23 15:20:19,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Guarantee` MODIFY `margin_money` decimal(21,9) not null default 0, MODIFY `charges` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-10-23 15:20:20,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` MODIFY `credit_in_account_currency` decimal(21,9) not null default 0, MODIFY `credit` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `debit_in_account_currency` decimal(21,9) not null default 0, MODIFY `debit` decimal(21,9) not null default 0
2025-10-23 15:20:20,173 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` ADD INDEX `advance_voucher_type_index`(`advance_voucher_type`), ADD INDEX `advance_voucher_no_index`(`advance_voucher_no`)
2025-10-23 15:20:20,541 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-10-23 15:20:21,367 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-23 15:20:21,811 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-10-23 15:20:23,686 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `agency_fee` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `balance` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `transport_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_sales_invoice` decimal(21,9) not null default 0, MODIFY `total_paid_amount` decimal(21,9) not null default 0, MODIFY `total_outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_clearing_charges` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0
2025-10-23 15:20:23,941 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `inspection_date` date, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-23 15:20:24,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-23 15:20:24,531 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-10-23 15:20:24,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Services` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-10-23 15:20:25,076 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-23 15:20:25,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-23 15:20:25,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-23 15:20:27,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-10-23 15:20:27,837 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-23 15:20:33,639 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0
2025-10-26 09:13:59,069 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-26 09:14:03,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-26 09:14:09,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-26 09:14:13,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-26 09:14:14,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-26 09:14:15,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-26 09:14:15,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-26 09:14:23,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-10-27 10:11:31,964 WARNING database DDL Query made to DB:
create table `tabShipping Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 10:13:17,429 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parent varchar(140)
2025-10-27 10:13:17,430 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:13:17,431 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:13:17,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` decimal(21,9) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 10:13:55,374 WARNING database DDL Query made to DB:
create table `tabTRA Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 10:14:48,814 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parent varchar(140)
2025-10-27 10:14:48,816 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:14:48,816 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:14:48,886 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` decimal(21,9) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 10:15:37,960 WARNING database DDL Query made to DB:
create table `tabPort Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 10:16:30,969 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parent varchar(140)
2025-10-27 10:16:30,970 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:16:30,971 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:16:31,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` decimal(21,9) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 10:16:56,611 WARNING database DDL Query made to DB:
create table `tabPhysical Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 10:18:11,409 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parent varchar(140)
2025-10-27 10:18:11,410 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:18:11,411 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:18:11,485 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Charges` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` decimal(21,9) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 10:18:39,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 10:18:52,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 10:19:08,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 10:19:45,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 10:20:39,782 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 10:21:24,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 10:34:52,815 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parent varchar(140)
2025-10-27 10:34:52,816 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:34:52,816 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:34:52,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `currency` varchar(140)
2025-10-27 10:35:18,923 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parent varchar(140)
2025-10-27 10:35:18,924 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:35:18,924 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:35:19,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` MODIFY `currency` varchar(140), MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 10:35:40,326 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parent varchar(140)
2025-10-27 10:35:40,327 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:35:40,327 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:35:40,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Charges` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `currency` varchar(140)
2025-10-27 10:36:15,518 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parent varchar(140)
2025-10-27 10:36:15,519 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:36:15,520 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:36:15,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `currency` varchar(140)
2025-10-27 11:56:22,968 WARNING database DDL Query made to DB:
create table `tabTRA Charges Paid` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 11:57:10,052 WARNING database DDL Query made to DB:
create table `tabShipping Charges Paid` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 11:57:49,589 WARNING database DDL Query made to DB:
create table `tabPhysical Verification Charges Paid` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 11:58:04,867 WARNING database DDL Query made to DB:
RENAME TABLE `tabPhysical Verification Charges Paid` TO `tabVerification Charges Paid`
2025-10-27 11:59:11,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 11:59:12,089 WARNING database DDL Query made to DB:
RENAME TABLE `tabPhysical Charges` TO `tabVerification Charges`
2025-10-27 11:59:42,760 WARNING database DDL Query made to DB:
create table `tabPort Charges Paid` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 12:04:56,085 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:04:56,085 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:04:56,085 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:04:56,086 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:04:56,086 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:04:56,086 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:04:56,087 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:04:56,087 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:04:56,087 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:04:56,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:04:56,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:04:56,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:06:41,657 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:41,657 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:41,657 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:41,658 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:41,658 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:41,658 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:41,659 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:41,659 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:41,659 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:41,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140)
2025-10-27 12:06:41,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140)
2025-10-27 12:06:41,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140)
2025-10-27 12:06:59,178 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:59,178 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:59,178 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:06:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:06:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:08:55,879 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:08:55,879 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:08:55,879 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:08:55,880 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:08:55,880 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:08:55,880 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:08:55,881 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:08:55,881 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:08:55,881 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:08:55,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:08:55,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:08:55,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:11:03,045 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:11:03,045 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:11:03,045 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:11:03,046 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:11:03,046 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:11:03,046 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:11:03,047 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:11:03,047 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:11:03,047 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:11:03,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:11:03,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:11:03,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:14:52,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-10-27 12:14:52,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-10-27 12:14:52,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-10-27 12:15:54,213 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:15:54,213 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:15:54,213 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:15:54,215 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:15:54,215 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:15:54,215 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:15:54,217 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:15:54,217 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:15:54,217 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:15:54,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 12:15:54,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 12:15:54,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 12:18:24,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:18:24,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:18:24,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:25:10,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:25:10,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:25:10,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:25:11,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:25:11,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:25:11,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:40:46,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:40:46,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:40:46,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:40:46,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:40:46,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:40:46,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:42:58,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:42:58,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:42:58,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:54:09,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 12:54:09,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 12:54:09,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
