2025-06-18 10:30:58,750 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-18 10:30:59,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-18 10:31:00,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-18 10:31:01,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-18 10:31:07,455 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-06-18 11:40:52,088 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-18 11:40:53,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-18 11:40:54,067 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-18 11:40:55,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0
2025-06-18 11:40:56,088 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-18 11:40:56,362 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-18 11:40:57,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-18 11:40:57,929 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-18 11:41:02,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0
2025-06-18 12:07:08,782 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-18 12:07:10,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-18 12:07:11,754 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-18 12:07:13,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `original_invoice_number` varchar(140)
2025-06-18 12:07:13,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0
2025-06-18 12:07:14,259 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-18 12:07:14,631 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-18 12:07:16,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-18 12:07:16,678 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-18 12:07:24,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-06-18 12:09:45,193 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-18 12:09:46,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-18 12:09:49,115 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-18 12:09:52,092 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-18 12:09:52,692 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-18 12:09:55,047 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-18 12:09:55,325 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-18 12:10:02,307 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0
2025-06-23 13:20:06,598 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 13:20:07,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `background_color` varchar(140)
2025-06-23 13:20:08,545 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-23 13:20:09,579 WARNING database DDL Query made to DB:
create table `tabPegged Currency Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_currency` varchar(140),
`pegged_against` varchar(140),
`pegged_exchange_rate` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-23 13:20:10,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-23 13:20:10,774 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0
2025-06-23 13:20:12,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing File` ADD COLUMN `total_charges` decimal(21,9) not null default 0
2025-06-23 13:20:12,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-23 13:20:12,821 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0
2025-06-23 13:20:13,209 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-23 13:20:13,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` MODIFY `estimated_cost` decimal(21,9) not null default 0
2025-06-23 13:20:13,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` DROP INDEX `parent`
2025-06-23 13:20:13,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` MODIFY `cost` decimal(21,9) not null default 0, MODIFY `old_price` decimal(21,9) not null default 0, MODIFY `new_price` decimal(21,9) not null default 0
2025-06-23 13:20:13,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` DROP INDEX `parent`
2025-06-23 13:20:14,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` MODIFY `amt_ex__sr` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `vat` decimal(21,9) not null default 0, MODIFY `amt_excl_vat` decimal(21,9) not null default 0
2025-06-23 13:20:14,060 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` DROP INDEX `parent`
2025-06-23 13:20:14,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Company Detail` DROP INDEX `parent`
2025-06-23 13:20:14,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabStation Members` DROP INDEX `parent`
2025-06-23 13:20:14,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Item Details` DROP INDEX `parent`
2025-06-23 13:20:15,061 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:15,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` DROP INDEX `parent`
2025-06-23 13:20:15,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:15,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` DROP INDEX `parent`
2025-06-23 13:20:15,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` MODIFY `cost_per_unit` decimal(21,9) not null default 0
2025-06-23 13:20:15,484 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` DROP INDEX `parent`
2025-06-23 13:20:15,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` MODIFY `item_balance` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 13:20:15,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` DROP INDEX `parent`
2025-06-23 13:20:15,920 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-23 13:20:15,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` DROP INDEX `parent`
2025-06-23 13:20:16,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Vehicle Detail` DROP INDEX `parent`
2025-06-23 13:20:16,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile Attachment` DROP INDEX `parent`
2025-06-23 13:20:16,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` MODIFY `flt_amount` decimal(21,9) not null default 0
2025-06-23 13:20:16,484 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` DROP INDEX `parent`
2025-06-23 13:20:16,702 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Attachment` DROP INDEX `parent`
2025-06-23 13:20:16,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-23 13:20:16,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` DROP INDEX `parent`
2025-06-23 13:20:17,138 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-06-23 13:20:17,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` DROP INDEX `parent`
2025-06-23 13:20:17,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabSQL Process Detail` DROP INDEX `parent`
2025-06-23 13:20:17,529 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-06-23 13:20:17,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` DROP INDEX `parent`
2025-06-23 13:20:17,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` MODIFY `qty` decimal(21,9) not null default 0
2025-06-23 13:20:17,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` DROP INDEX `parent`
2025-06-23 13:20:17,970 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` MODIFY `invoice_gain_or_loss` decimal(21,9) not null default 0, MODIFY `invoice_exchange_rate` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0
2025-06-23 13:20:18,012 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` DROP INDEX `parent`
2025-06-23 13:20:18,214 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Policy Holder Detail` DROP INDEX `parent`
2025-06-23 13:20:18,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-23 13:20:18,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` DROP INDEX `parent`
2025-06-23 13:20:18,611 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `task_rate` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 13:20:18,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` DROP INDEX `parent`
2025-06-23 13:20:18,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` MODIFY `billitemmiscamount` decimal(21,9) not null default 0, MODIFY `billitemamount` decimal(21,9) not null default 0, MODIFY `billitemeqvamount` decimal(21,9) not null default 0
2025-06-23 13:20:18,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` DROP INDEX `parent`
2025-06-23 13:20:18,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Consignment Detail` DROP INDEX `parent`
2025-06-23 13:20:19,137 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Non Stock Item Details` DROP INDEX `parent`
2025-06-23 13:20:19,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-06-23 13:20:19,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` DROP INDEX `parent`
2025-06-23 13:20:19,536 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` MODIFY `debit_amount` decimal(21,9) not null default 0
2025-06-23 13:20:19,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` DROP INDEX `parent`
2025-06-23 13:20:19,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-23 13:20:19,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` DROP INDEX `parent`
2025-06-23 13:20:19,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Consignment Detail` DROP INDEX `parent`
2025-06-23 13:20:20,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:20,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` DROP INDEX `parent`
2025-06-23 13:20:20,235 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Currency Settings Rate` DROP INDEX `parent`
2025-06-23 13:20:20,371 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Details` DROP INDEX `parent`
2025-06-23 13:20:20,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Employee Salary Slip` DROP INDEX `parent`
2025-06-23 13:20:20,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabPossible Root Cause` DROP INDEX `parent`
2025-06-23 13:20:20,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` MODIFY `qty` decimal(21,9) not null default 0
2025-06-23 13:20:21,007 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` DROP INDEX `parent`
2025-06-23 13:20:21,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` MODIFY `request_amount` decimal(21,9) not null default 0
2025-06-23 13:20:21,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` DROP INDEX `parent`
2025-06-23 13:20:21,770 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` MODIFY `payable_account_currency` varchar(140), MODIFY `request_amount` decimal(21,9) not null default 0
2025-06-23 13:20:21,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` DROP INDEX `parent`
2025-06-23 13:20:22,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Reading` DROP INDEX `parent`
2025-06-23 13:20:22,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:22,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` DROP INDEX `parent`
2025-06-23 13:20:22,492 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Details` DROP INDEX `parent`
2025-06-23 13:20:22,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-06-23 13:20:22,652 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` DROP INDEX `parent`
2025-06-23 13:20:22,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-23 13:20:22,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` DROP INDEX `parent`
2025-06-23 13:20:23,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Detail` DROP INDEX `parent`
2025-06-23 13:20:23,149 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequired Permit` DROP INDEX `parent`
2025-06-23 13:20:23,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-23 13:20:23,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` DROP INDEX `parent`
2025-06-23 13:20:23,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Procedure Table` DROP INDEX `parent`
2025-06-23 13:20:23,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport Border Procedure Table` DROP INDEX `parent`
2025-06-23 13:20:23,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Status Table` DROP INDEX `parent`
2025-06-23 13:20:23,989 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` MODIFY `tare_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-06-23 13:20:24,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` DROP INDEX `parent`
2025-06-23 13:20:24,238 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Processing Vehicle Details` DROP INDEX `parent`
2025-06-23 13:20:24,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer File Closing Information` DROP INDEX `parent`
2025-06-23 13:20:24,636 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-23 13:20:24,675 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` DROP INDEX `parent`
2025-06-23 13:20:25,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabPermits Table` DROP INDEX `parent`
2025-06-23 13:20:25,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabMandatory Attachment Table` DROP INDEX `parent`
2025-06-23 13:20:25,306 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond History Table` DROP INDEX `parent`
2025-06-23 13:20:25,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Details` DROP INDEX `parent`
2025-06-23 13:20:25,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Details` DROP INDEX `parent`
2025-06-23 13:20:25,862 WARNING database DDL Query made to DB:
ALTER TABLE `tabEquipment Table` DROP INDEX `parent`
2025-06-23 13:20:26,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Trip Location Update` DROP INDEX `parent`
2025-06-23 13:20:26,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Checklist` DROP INDEX `parent`
2025-06-23 13:20:26,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Checklist` DROP INDEX `parent`
2025-06-23 13:20:26,760 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-23 13:20:26,894 WARNING database DDL Query made to DB:
ALTER TABLE `tabBrake System Details` DROP INDEX `parent`
2025-06-23 13:20:27,039 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `cost_per_litre` decimal(21,9) not null default 0
2025-06-23 13:20:27,074 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` DROP INDEX `parent`
2025-06-23 13:20:27,230 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel System Details` DROP INDEX `parent`
2025-06-23 13:20:27,421 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuspension Details` DROP INDEX `parent`
2025-06-23 13:20:27,551 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectronics Checklist` DROP INDEX `parent`
2025-06-23 13:20:27,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` MODIFY `distance` decimal(21,9) not null default 0
2025-06-23 13:20:27,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` DROP INDEX `parent`
2025-06-23 13:20:27,885 WARNING database DDL Query made to DB:
ALTER TABLE `tabBrake Checklist` DROP INDEX `parent`
2025-06-23 13:20:28,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Checklist` DROP INDEX `parent`
2025-06-23 13:20:28,145 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel System Checklist` DROP INDEX `parent`
2025-06-23 13:20:28,292 WARNING database DDL Query made to DB:
ALTER TABLE `tabLighting Checklist` DROP INDEX `parent`
2025-06-23 13:20:28,445 WARNING database DDL Query made to DB:
ALTER TABLE `tabLighting Checklist Details` DROP INDEX `parent`
2025-06-23 13:20:28,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Details` DROP INDEX `parent`
2025-06-23 13:20:28,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Checklist` DROP INDEX `parent`
2025-06-23 13:20:28,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuspension Checklist` DROP INDEX `parent`
2025-06-23 13:20:29,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteering Checklist` DROP INDEX `parent`
2025-06-23 13:20:29,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Details` DROP INDEX `parent`
2025-06-23 13:20:29,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteering Details` DROP INDEX `parent`
2025-06-23 13:20:29,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Documents` DROP INDEX `parent`
2025-06-23 13:20:29,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:29,766 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` DROP INDEX `parent`
2025-06-23 13:20:29,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabTires Checklist` DROP INDEX `parent`
2025-06-23 13:20:30,123 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectrical Checklist` DROP INDEX `parent`
2025-06-23 13:20:30,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 13:20:30,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` DROP INDEX `parent`
2025-06-23 13:20:30,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoute Steps Table` DROP INDEX `parent`
2025-06-23 13:20:30,585 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 13:20:30,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` DROP INDEX `parent`
2025-06-23 13:20:30,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubtrips Table` DROP INDEX `parent`
2025-06-23 13:20:30,871 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectrical Details` DROP INDEX `parent`
2025-06-23 13:20:31,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Checklist` DROP INDEX `parent`
2025-06-23 13:20:31,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabTires Details` DROP INDEX `parent`
2025-06-23 13:20:31,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectronics Details` DROP INDEX `parent`
2025-06-23 13:20:31,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabOrder Tracking Container` DROP INDEX `parent`
2025-06-23 13:20:31,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabBin List` DROP INDEX `parent`
2025-06-23 13:20:31,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0
2025-06-23 13:20:31,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` DROP INDEX `parent`
2025-06-23 13:20:32,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Item` DROP INDEX `parent`
2025-06-23 13:20:32,238 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` MODIFY `actual_amount` decimal(21,9) not null default 0, MODIFY `planned_amount` decimal(21,9) not null default 0
2025-06-23 13:20:32,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` DROP INDEX `parent`
2025-06-23 13:20:32,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` MODIFY `requested` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `issued` decimal(21,9) not null default 0
2025-06-23 13:20:32,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` DROP INDEX `parent`
2025-06-23 13:20:32,612 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 13:20:32,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` DROP INDEX `parent`
2025-06-23 13:20:32,780 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 13:20:32,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` DROP INDEX `parent`
2025-06-23 13:20:32,958 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` MODIFY `billable_hours` decimal(21,9) not null default 0, MODIFY `rate_per_hour` decimal(21,9) not null default 0
2025-06-23 13:20:32,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` DROP INDEX `parent`
2025-06-23 13:20:33,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` MODIFY `transfer_amount` decimal(21,9) not null default 0
2025-06-23 13:20:33,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` DROP INDEX `parent`
2025-06-23 13:20:38,954 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-06-23 18:35:49,625 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 18:35:50,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-23 18:35:52,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-23 18:35:53,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
2025-06-23 18:35:53,665 WARNING database DDL Query made to DB:
create table `tabClearing Service Charge Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`description` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-23 18:39:36,804 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 18:39:37,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-23 18:39:38,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-23 18:39:40,140 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Service Charge Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 18:46:25,504 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 18:46:26,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-23 18:46:27,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-23 18:46:28,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-23 18:46:29,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-23 18:46:30,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-23 18:46:30,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-23 18:46:36,422 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0
2025-06-23 18:50:15,692 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 18:50:16,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-23 18:50:17,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-23 18:50:19,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-23 18:50:19,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 18:50:20,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-23 18:50:21,119 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-23 18:50:25,401 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0
2025-06-25 11:17:22,565 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-25 11:17:22,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstalled Application` ADD COLUMN `has_setup_wizard` int(1) not null default 0, ADD COLUMN `is_setup_complete` int(1) not null default 0
2025-06-25 11:17:23,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Log` MODIFY `link` text
2025-06-25 11:17:24,361 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-25 11:17:24,886 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `deposit` decimal(21,9) not null default 0, MODIFY `withdrawal` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-06-25 11:17:26,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-25 11:17:27,970 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-25 11:17:28,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-25 11:17:29,541 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-25 11:17:29,761 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-25 11:17:36,069 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0
2025-07-13 11:32:47,560 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:32:49,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:32:50,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Merge Log` ADD COLUMN `company` varchar(140)
2025-07-13 11:32:50,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Merge Log` ADD INDEX `creation`(`creation`)
2025-07-13 11:32:51,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0
2025-07-13 11:32:52,686 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead` MODIFY `annual_revenue` decimal(21,9) not null default 0
2025-07-13 11:32:53,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity` MODIFY `annual_revenue` decimal(21,9) not null default 0, MODIFY `base_opportunity_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `opportunity_amount` decimal(21,9) not null default 0, MODIFY `first_response_time` decimal(21,9), MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-07-13 11:32:53,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `subject` varchar(140)
2025-07-13 11:32:53,799 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0
2025-07-13 11:32:54,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:32:54,721 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` ADD COLUMN `disassembled_qty` decimal(21,9) not null default 0
2025-07-13 11:32:54,743 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order` MODIFY `total_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `corrective_operation_cost` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `lead_time` decimal(21,9) not null default 0, MODIFY `additional_operating_cost` decimal(21,9) not null default 0
2025-07-13 11:32:55,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` ADD COLUMN `delivery_status` varchar(140), ADD COLUMN `per_delivered` decimal(21,9) not null default 0
2025-07-13 11:32:55,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `for_qty` decimal(21,9) not null default 0
2025-07-13 11:32:55,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `against_pick_list` varchar(140)
2025-07-13 11:32:55,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-07-13 11:32:55,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD INDEX `against_pick_list_index`(`against_pick_list`)
2025-07-13 11:32:56,578 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0
2025-07-13 11:32:56,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` ADD COLUMN `delivered_qty` decimal(21,9) not null default 0
2025-07-13 11:32:56,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` MODIFY `picked_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-07-13 11:32:57,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `price_list` varchar(140)
2025-07-13 11:32:57,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-07-13 11:32:57,816 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-07-13 11:32:58,020 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0
2025-07-13 11:32:58,221 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Movement` ADD INDEX `creation`(`creation`)
2025-07-13 11:32:59,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `journal_entry` varchar(140)
2025-07-13 11:32:59,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
2025-07-13 11:32:59,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `journal_entry` varchar(140)
2025-07-13 11:32:59,475 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-07-13 11:33:00,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:33:00,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:33:01,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` MODIFY `estimated_cost` decimal(21,9) not null default 0
2025-07-13 11:33:01,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` MODIFY `old_price` decimal(21,9) not null default 0, MODIFY `new_price` decimal(21,9) not null default 0, MODIFY `cost` decimal(21,9) not null default 0
2025-07-13 11:33:01,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` MODIFY `amt_excl_vat` decimal(21,9) not null default 0, MODIFY `vat` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `amt_ex__sr` decimal(21,9) not null default 0
2025-07-13 11:33:02,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:02,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:02,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` MODIFY `cost_per_unit` decimal(21,9) not null default 0
2025-07-13 11:33:02,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `item_balance` decimal(21,9) not null default 0
2025-07-13 11:33:03,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:33:03,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` MODIFY `flt_amount` decimal(21,9) not null default 0
2025-07-13 11:33:03,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-07-13 11:33:04,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-07-13 11:33:04,397 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0
2025-07-13 11:33:04,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` MODIFY `qty` decimal(21,9) not null default 0
2025-07-13 11:33:04,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` MODIFY `invoice_exchange_rate` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `invoice_gain_or_loss` decimal(21,9) not null default 0
2025-07-13 11:33:05,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-07-13 11:33:05,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `task_rate` decimal(21,9) not null default 0
2025-07-13 11:33:05,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` MODIFY `billitemamount` decimal(21,9) not null default 0, MODIFY `billitemeqvamount` decimal(21,9) not null default 0, MODIFY `billitemmiscamount` decimal(21,9) not null default 0
2025-07-13 11:33:06,006 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-07-13 11:33:06,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` MODIFY `debit_amount` decimal(21,9) not null default 0
2025-07-13 11:33:06,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-13 11:33:06,630 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA TAX Inv Item` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:09,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` MODIFY `qty` decimal(21,9) not null default 0
2025-07-13 11:33:09,471 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` MODIFY `request_amount` decimal(21,9) not null default 0
2025-07-13 11:33:09,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` MODIFY `request_amount` decimal(21,9) not null default 0, MODIFY `payable_account_currency` varchar(140)
2025-07-13 11:33:10,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:10,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-07-13 11:33:10,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-07-13 11:33:11,315 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:33:11,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` MODIFY `tare_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-07-13 11:33:12,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-07-13 11:33:14,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-13 11:33:15,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` MODIFY `cost_per_litre` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:33:15,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` MODIFY `distance` decimal(21,9) not null default 0
2025-07-13 11:33:17,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:17,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` MODIFY `amount` decimal(21,9) not null default 0
2025-07-13 11:33:18,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-13 11:33:19,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-07-13 11:33:19,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` MODIFY `planned_amount` decimal(21,9) not null default 0, MODIFY `actual_amount` decimal(21,9) not null default 0
2025-07-13 11:33:19,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` MODIFY `requested` decimal(21,9) not null default 0, MODIFY `issued` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0
2025-07-13 11:33:20,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:33:20,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:33:20,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` MODIFY `billable_hours` decimal(21,9) not null default 0, MODIFY `rate_per_hour` decimal(21,9) not null default 0
2025-07-13 11:33:20,781 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` MODIFY `transfer_amount` decimal(21,9) not null default 0
2025-07-13 11:33:25,134 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-13 11:33:25,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0
2025-07-13 11:36:49,205 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-13 11:36:50,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-13 11:36:52,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-13 11:36:54,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-13 11:36:54,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:36:56,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-13 11:36:56,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-13 11:37:01,246 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-07-26 16:14:10,580 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-26 16:14:11,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart` ADD COLUMN `show_values_over_chart` int(1) not null default 0
2025-07-26 16:14:12,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-26 16:14:13,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice` MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0
2025-07-26 16:14:14,983 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-26 16:14:15,380 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-26 16:14:15,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `planned_operating_cost` decimal(21,9) not null default 0, MODIFY `completed_qty` decimal(21,9) not null default 0, MODIFY `time_in_mins` decimal(21,9) not null default 0, MODIFY `actual_operating_cost` decimal(21,9) not null default 0, MODIFY `actual_operation_time` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `batch_size` decimal(21,9) not null default 0
2025-07-26 16:14:16,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` ADD COLUMN `delivered_by_supplier` int(1) not null default 0
2025-07-26 16:14:16,128 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `picked_qty` decimal(21,9) not null default 0
2025-07-26 16:14:16,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabSerial No` MODIFY `purchase_rate` decimal(21,9) not null default 0
2025-07-26 16:14:16,701 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` ADD COLUMN `buying_price_list` varchar(140)
2025-07-26 16:14:16,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-07-26 16:14:17,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0
2025-07-26 16:14:18,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-07-26 16:14:18,962 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-26 16:14:19,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Schedule Assignment` ADD INDEX `creation`(`creation`)
2025-07-26 16:14:19,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-26 16:14:19,914 WARNING database DDL Query made to DB:
create table `tabBank Statement Summary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`bank_account` varchar(140),
`end_of_month` date,
`bank_account_currency` varchar(140),
`count_of_transaction` int(11) not null default 0,
`deposit` decimal(21,9) not null default 0,
`withdrawal` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-26 16:14:20,240 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-07-26 16:14:20,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-26 16:14:20,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Ward` DROP INDEX `ward`
2025-07-26 16:14:20,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Village` ADD COLUMN `postcode` varchar(140)
2025-07-26 16:14:20,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Village` DROP INDEX `village`
2025-07-26 16:14:21,196 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `task_rate` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-26 16:14:21,494 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-07-26 16:14:21,688 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-07-26 16:14:22,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-26 16:14:23,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-26 16:14:23,756 WARNING database DDL Query made to DB:
create table `tabOpenAI Query Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`doctype_name` varchar(140),
`query` longtext,
`response` longtext,
`is_cached` int(1) not null default 0,
`resend_count` int(11) not null default 0,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-26 16:14:31,461 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `custom_beneficiary_bank_bic` varchar(140), ADD COLUMN `custom_employee_country` varchar(140), ADD COLUMN `custom_bank_account_name` varchar(140), ADD COLUMN `custom_bank_country_code` varchar(140), ADD COLUMN `custom_employee_country_code` varchar(140)
2025-07-26 16:14:31,483 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-07-26 16:14:31,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0
2025-07-27 11:11:40,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing File` ADD COLUMN `has_transit_bond` int(1) not null default 0, ADD COLUMN `valid_from` date, ADD COLUMN `valid_to` date, ADD COLUMN `bond_returned` int(1) not null default 0, ADD COLUMN `bond_release_date` date
2025-07-27 11:49:51,270 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 11:49:52,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 11:49:53,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 11:49:55,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 11:49:56,351 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 11:49:57,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 11:49:58,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 11:50:04,048 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-07-27 12:53:04,576 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 12:53:05,721 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 12:53:07,208 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 12:53:09,231 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 12:53:09,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 12:53:10,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 12:53:11,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 12:53:17,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-07-27 12:55:16,425 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 12:55:17,228 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 12:55:18,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 12:55:20,028 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 12:55:20,359 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 12:55:21,865 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 12:55:22,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-27 12:55:26,467 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-07-27 12:57:19,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` ADD COLUMN `has_transit_bond` int(1) not null default 0
2025-07-27 12:57:19,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-07-27 12:58:58,952 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` ADD COLUMN `valid_from` date, ADD COLUMN `valid_to` date
