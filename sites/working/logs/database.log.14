2025-05-28 16:03:53,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD COLUMN `spare_name` varchar(140), ADD COLUMN `quantity` decimal(21,9) not null default 0, ADD COLUMN `invoice` varchar(140)
2025-05-28 16:03:53,585 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-28 16:03:53,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` ADD COLUMN `fully_paid` int(1) not null default 0
2025-05-28 16:03:53,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` MODIFY `total` decimal(21,9) not null default 0, MODIFY `penalty` decimal(21,9) not null default 0, MODIFY `charge` decimal(21,9) not null default 0
2025-05-28 16:03:53,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `previous_invoice_number` varchar(140), ADD COLUMN `default_item_discount` decimal(21,9) not null default 0, ADD COLUMN `default_item_tax_template` varchar(140), ADD COLUMN `price_reduction` decimal(21,9) not null default 0, ADD COLUMN `tra_control_number` varchar(140), ADD COLUMN `witholding_tax_certificate_number` varchar(140), ADD COLUMN `electronic_fiscal_device` varchar(140), ADD COLUMN `efd_z_report` varchar(140), ADD COLUMN `excise_duty_applicable` int(1) not null default 0, ADD COLUMN `enabled_auto_create_delivery_notes` int(1) not null default 1, ADD COLUMN `delivery_status` varchar(140)
2025-05-28 16:03:53,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0
2025-05-28 16:03:53,882 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `is_ignored_in_pending_qty` int(1) not null default 0, ADD COLUMN `allow_over_sell` int(1) not null default 0, ADD COLUMN `withholding_tax_rate` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_entry` varchar(140), ADD COLUMN `allow_override_net_rate` int(1) not null default 0, ADD COLUMN `delivery_status` varchar(140)
2025-05-28 16:03:53,903 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-05-28 16:03:53,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `posting_date` date
2025-05-28 16:03:54,007 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0
2025-05-28 16:03:54,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` ADD COLUMN `material_request` varchar(140)
2025-05-28 16:03:54,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `amount_difference` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `current_valuation_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `current_amount` decimal(21,9) not null default 0
2025-05-28 16:03:54,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` ADD COLUMN `csf_tz_year` int(11) not null default 0, ADD COLUMN `csf_tz_acquisition_odometer` int(11) not null default 0, ADD COLUMN `csf_tz_engine_number` varchar(140)
2025-05-28 16:03:54,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `vehicle_value` decimal(21,9) not null default 0, MODIFY `uom` varchar(140)
2025-05-28 16:03:54,325 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `weight_per_unit` decimal(21,9) not null default 0, ADD COLUMN `total_weight` decimal(21,9) not null default 0, ADD COLUMN `weight_uom` varchar(140)
2025-05-28 16:03:54,345 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-05-28 16:03:54,409 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` ADD COLUMN `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_rate_on_sales` decimal(21,9) not null default 0, ADD COLUMN `excisable_item` int(1) not null default 0, ADD COLUMN `default_tax_template` varchar(140)
2025-05-28 16:03:54,429 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0, MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0
2025-05-28 16:03:54,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `withholding_tax_rate` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_entry` varchar(140)
2025-05-28 16:03:54,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0
2025-05-28 16:03:54,593 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `posting_date` date, ADD COLUMN `default_item_discount` decimal(21,9) not null default 0
2025-05-28 16:03:54,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0
2025-05-28 16:03:54,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `electronic_fiscal_device` varchar(140)
2025-05-28 16:03:54,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD COLUMN `trip_destination` varchar(140), ADD COLUMN `destination_description` varchar(140)
2025-05-28 16:03:54,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-05-28 16:03:54,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustom DocPerm` ADD COLUMN `dependent` int(1) not null default 0
2025-05-28 16:03:54,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` ADD COLUMN `source_warehouse` varchar(140), ADD COLUMN `fg_warehouse` varchar(140), ADD COLUMN `wip_warehouse` varchar(140), ADD COLUMN `scrap_warehouse` varchar(140)
2025-05-28 16:03:54,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0
2025-05-28 16:03:55,094 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `expense_record` varchar(140), ADD COLUMN `import_file` varchar(140)
2025-05-28 16:03:55,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-05-28 16:03:55,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` ADD COLUMN `form_sales_invoice` varchar(140)
2025-05-28 16:03:55,231 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-05-28 16:03:55,287 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` ADD COLUMN `image` text
2025-05-28 16:03:55,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` MODIFY `total_operation_time` decimal(21,9) not null default 0
2025-05-28 16:03:55,387 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `company_bank_details` text, ADD COLUMN `vrn` varchar(140), ADD COLUMN `tin` varchar(140), ADD COLUMN `p_o_box` varchar(140), ADD COLUMN `city` varchar(140), ADD COLUMN `plot_number` varchar(140), ADD COLUMN `block_number` varchar(140), ADD COLUMN `street` varchar(140), ADD COLUMN `max_records_in_dialog` int(11) not null default 0, ADD COLUMN `default_withholding_payable_account` varchar(140), ADD COLUMN `auto_create_for_purchase_withholding` int(1) not null default 0, ADD COLUMN `auto_submit_for_purchase_withholding` int(1) not null default 0, ADD COLUMN `default_withholding_receivable_account` varchar(140), ADD COLUMN `auto_create_for_sales_withholding` int(1) not null default 0, ADD COLUMN `auto_submit_for_sales_withholding` int(1) not null default 0, ADD COLUMN `bypass_material_request_validation` int(1) not null default 0, ADD COLUMN `default_item_tax_template` varchar(140), ADD COLUMN `enabled_auto_create_delivery_notes` int(1) not null default 1
2025-05-28 16:03:55,408 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-05-28 16:03:55,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` ADD COLUMN `import_file` varchar(140)
2025-05-28 16:03:55,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-05-28 16:03:55,572 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` ADD COLUMN `item` varchar(140)
2025-05-28 16:03:55,598 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` MODIFY `tax_rate` decimal(21,9) not null default 0
2025-05-28 16:03:56,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-05-28 16:03:56,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0
2025-05-28 16:03:56,228 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-05-28 16:03:56,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-05-28 16:03:56,494 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `csf_tz_is_auto_close_dn` int(1) not null default 0, ADD COLUMN `csf_tz_close_dn_after` int(11) not null default 0
2025-05-28 16:03:56,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-05-28 16:03:56,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD COLUMN `based_on_hourly_rate` int(1) not null default 0, ADD COLUMN `hourly_rate` decimal(21,9) not null default 0, ADD COLUMN `no_of_hours` decimal(21,9) not null default 0, ADD COLUMN `auto_repeat_frequency` varchar(140), ADD COLUMN `auto_repeat_end_date` date, ADD COLUMN `last_transaction_amount` decimal(21,9) not null default 0, ADD COLUMN `last_transaction_date` date, ADD COLUMN `auto_created_based_on` varchar(140)
2025-05-28 16:03:57,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:03:57,055 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-05-28 16:03:57,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `is_authotp_applied` int(1) not null default 0, ADD COLUMN `default_authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-05-28 16:03:57,781 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-05-28 16:03:57,888 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-05-28 16:03:57,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0
2025-05-28 16:03:58,131 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-05-28 16:03:58,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0
2025-05-28 16:03:58,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-05-28 16:03:58,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-05-28 16:03:58,290 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-05-28 16:03:58,320 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-05-28 16:03:58,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employee_country` varchar(140), ADD COLUMN `employee_country_code` varchar(140), ADD COLUMN `beneficiary_bank_bic` varchar(140), ADD COLUMN `bank_country_code` varchar(140), ADD COLUMN `bank_country` varchar(140), ADD COLUMN `bank_account_name` varchar(140)
2025-05-28 16:03:58,794 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-05-28 16:03:58,927 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `allow_negative` int(1) not null default 0
2025-05-28 16:03:58,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:03:58,981 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-05-28 16:03:59,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `bank_charges` decimal(21,9) not null default 0, ADD COLUMN `bank_charges_journal_entry` varchar(140), ADD COLUMN `bank_charges_description` text
2025-05-28 16:03:59,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0
2025-05-28 16:03:59,581 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_bank_charges_account` varchar(140)
2025-05-28 16:03:59,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-05-28 16:03:59,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0
2025-05-28 16:04:00,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD COLUMN `employee_advance_ref` varchar(140), ADD COLUMN `total_travel_cost` decimal(21,9) not null default 0
2025-05-28 16:04:00,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD INDEX `creation`(`creation`)
2025-05-28 16:04:00,159 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD COLUMN `travel_request_ref` varchar(140)
2025-05-28 16:04:00,184 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0
2025-05-28 16:04:00,219 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-05-28 16:04:00,308 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `max_unclaimed_ea` int(11) not null default 0
2025-05-28 16:04:00,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-05-28 16:04:00,466 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-05-28 16:04:00,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `enable_auto_close_material_request` int(1) not null default 0, ADD COLUMN `close_material_request_after` int(11) not null default 0
2025-05-28 16:04:00,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-05-28 16:04:00,949 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` ADD COLUMN `applicable_charges_per_item` decimal(21,9) not null default 0, ADD COLUMN `price_per_item` decimal(21,9) not null default 0
2025-05-28 16:04:00,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `applicable_charges` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:04:01,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Account` ADD COLUMN `bank_supplier` varchar(140)
2025-05-28 16:47:05,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` MODIFY `estimated_cost` decimal(21,9) not null default 0
2025-05-28 16:47:05,725 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template` MODIFY `qty` decimal(21,9) not null default 0
2025-05-28 16:47:05,862 WARNING database DDL Query made to DB:
ALTER TABLE `tabSection` MODIFY `monthly_target` decimal(21,9) not null default 0
2025-05-28 16:47:06,002 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` MODIFY `cost` decimal(21,9) not null default 0, MODIFY `old_price` decimal(21,9) not null default 0, MODIFY `new_price` decimal(21,9) not null default 0
2025-05-28 16:47:06,117 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` MODIFY `vat` decimal(21,9) not null default 0, MODIFY `amt_excl_vat` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `amt_ex__sr` decimal(21,9) not null default 0
2025-05-28 16:47:06,791 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report` MODIFY `a_turnover` decimal(21,9) not null default 0, MODIFY `b_vat` decimal(21,9) not null default 0, MODIFY `c_vat` decimal(21,9) not null default 0, MODIFY `total_excluding_vat_ticked` decimal(21,9) not null default 0, MODIFY `total_vat_ticked` decimal(21,9) not null default 0, MODIFY `b_turnover` decimal(21,9) not null default 0, MODIFY `money` decimal(21,9) not null default 0, MODIFY `total_turnover` decimal(21,9) not null default 0, MODIFY `e_turnover` decimal(21,9) not null default 0, MODIFY `c_turnover` decimal(21,9) not null default 0, MODIFY `d_vat` decimal(21,9) not null default 0, MODIFY `allowable_difference` decimal(21,9) not null default 0, MODIFY `d_net_sum` decimal(21,9) not null default 0, MODIFY `total_turnover_ticked` decimal(21,9) not null default 0, MODIFY `c_net_sum` decimal(21,9) not null default 0, MODIFY `b_net_sum` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `a_net_sum` decimal(21,9) not null default 0, MODIFY `total_vat` decimal(21,9) not null default 0, MODIFY `total_turnover_ex_sr` decimal(21,9) not null default 0, MODIFY `a_vat` decimal(21,9) not null default 0, MODIFY `d_turnover` decimal(21,9) not null default 0, MODIFY `total_turnover_exempted__sp_relief_ticked` decimal(21,9) not null default 0
2025-05-28 16:47:06,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:07,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:07,450 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Type` MODIFY `rate` decimal(21,9) not null default 0
2025-05-28 16:47:07,579 WARNING database DDL Query made to DB:
ALTER TABLE `tabNMB Callback` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:07,689 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting GL Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `credit_amount` decimal(21,9) not null default 0, MODIFY `debit_amount` decimal(21,9) not null default 0
2025-05-28 16:47:07,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework` MODIFY `task_rate` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-05-28 16:47:08,026 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` MODIFY `cost_per_unit` decimal(21,9) not null default 0
2025-05-28 16:47:08,236 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `item_balance` decimal(21,9) not null default 0
2025-05-28 16:47:08,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Cover Note` MODIFY `commisionpaid` decimal(21,9) not null default 0, MODIFY `commisionrate` decimal(21,9) not null default 0, MODIFY `totalpremiumamountincludingtax` decimal(21,9) not null default 0, MODIFY `totalpremiumamountexcludingtax` decimal(21,9) not null default 0, MODIFY `exchangerate` decimal(21,9) not null default 0
2025-05-28 16:47:09,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-28 16:47:09,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` MODIFY `flt_amount` decimal(21,9) not null default 0
2025-05-28 16:47:09,875 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-05-28 16:47:10,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` MODIFY `charge` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `penalty` decimal(21,9) not null default 0
2025-05-28 16:47:10,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:10,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:10,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` MODIFY `qty` decimal(21,9) not null default 0
2025-05-28 16:47:10,822 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` MODIFY `invoice_exchange_rate` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `invoice_gain_or_loss` decimal(21,9) not null default 0
2025-05-28 16:47:11,827 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill` MODIFY `billequivalentamount` decimal(21,9) not null default 0, MODIFY `miscellaneousamount` decimal(21,9) not null default 0, MODIFY `billedamount` decimal(21,9) not null default 0
2025-05-28 16:47:12,245 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-05-28 16:47:12,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `task_rate` decimal(21,9) not null default 0
2025-05-28 16:47:12,565 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` MODIFY `billitemeqvamount` decimal(21,9) not null default 0, MODIFY `billitemmiscamount` decimal(21,9) not null default 0, MODIFY `billitemamount` decimal(21,9) not null default 0
2025-05-28 16:47:13,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-05-28 16:47:13,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` MODIFY `debit_amount` decimal(21,9) not null default 0
2025-05-28 16:47:13,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-05-28 16:47:14,172 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Record` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:14,537 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpen Invoice Exchange Rate Revaluation` MODIFY `exchange_rate_to_company_currency` decimal(21,9) not null default 0
2025-05-28 16:47:16,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` MODIFY `qty` decimal(21,9) not null default 0
2025-05-28 16:47:17,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection` MODIFY `sample_size` decimal(21,9) not null default 0
2025-05-28 16:47:17,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` MODIFY `request_amount` decimal(21,9) not null default 0
2025-05-28 16:47:17,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` MODIFY `payable_account_currency` varchar(140), MODIFY `request_amount` decimal(21,9) not null default 0
2025-05-28 16:47:18,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:18,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-05-28 16:47:18,596 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-28 16:47:18,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-05-28 16:47:19,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-28 16:47:19,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport File` MODIFY `invoice_amount` decimal(21,9) not null default 0
2025-05-28 16:47:20,329 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `tare_weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-05-28 16:47:21,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Clearance` MODIFY `goods_quantity` decimal(21,9) not null default 0, MODIFY `loose_gross_weight` decimal(21,9) not null default 0, MODIFY `loose_net_weight` decimal(21,9) not null default 0
2025-05-28 16:47:21,188 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond` MODIFY `bond_value` decimal(21,9) not null default 0
2025-05-28 16:47:21,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-05-28 16:47:23,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Request` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:23,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-05-28 16:47:24,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` MODIFY `cost_per_litre` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-28 16:47:24,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` MODIFY `distance` decimal(21,9) not null default 0
2025-05-28 16:47:25,366 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense` MODIFY `fixed_value` decimal(21,9) not null default 0
2025-05-28 16:47:26,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense` MODIFY `fixed_value` decimal(21,9) not null default 0
2025-05-28 16:47:26,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:27,251 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:27,661 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Trip` MODIFY `main_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0
2025-05-28 16:47:27,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Route` MODIFY `total_tzs` decimal(21,9) not null default 0, MODIFY `total_usd` decimal(21,9) not null default 0
2025-05-28 16:47:27,948 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-28 16:47:29,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0
2025-05-28 16:47:29,786 WARNING database DDL Query made to DB:
ALTER TABLE `tabPast Sales` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:30,020 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` MODIFY `actual_amount` decimal(21,9) not null default 0, MODIFY `planned_amount` decimal(21,9) not null default 0
2025-05-28 16:47:30,204 WARNING database DDL Query made to DB:
ALTER TABLE `tabPast Serial No` MODIFY `amount` decimal(21,9) not null default 0
2025-05-28 16:47:30,497 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` MODIFY `requested` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `issued` decimal(21,9) not null default 0
2025-05-28 16:47:30,782 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-05-28 16:47:30,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-05-28 16:47:31,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` MODIFY `rate_per_hour` decimal(21,9) not null default 0, MODIFY `billable_hours` decimal(21,9) not null default 0
2025-05-28 16:47:32,274 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` MODIFY `transfer_amount` decimal(21,9) not null default 0
2025-05-28 16:47:32,467 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Initiation` MODIFY `control_sum` decimal(21,9) not null default 0
2025-05-30 22:59:25,240 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-30 22:59:26,442 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-30 22:59:27,816 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` ADD COLUMN `party_full_name` varchar(140)
2025-05-30 22:59:28,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-05-30 22:59:28,719 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-05-30 22:59:28,742 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-05-30 22:59:29,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-05-30 22:59:29,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0
2025-05-30 22:59:30,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-05-30 22:59:30,039 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0
2025-05-30 22:59:30,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-05-30 22:59:30,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0
2025-05-30 22:59:30,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-30 22:59:33,037 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-05-30 22:59:33,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-05-30 22:59:33,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-30 22:59:33,502 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD INDEX `creation`(`creation`)
2025-05-30 22:59:34,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-05-30 22:59:35,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-30 22:59:42,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `custom_uom` varchar(140), ADD COLUMN `custom_trade_in_item` varchar(140), ADD COLUMN `custom_trade_in_qty` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_uom` varchar(140), ADD COLUMN `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `custom_total_trade_in_value` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_batch_no` varchar(140), ADD COLUMN `custom_trade_in_serial_no` text
2025-05-30 22:59:42,478 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-05-30 22:59:42,562 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_control_account` varchar(140)
2025-05-30 22:59:42,583 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-05-30 22:59:42,652 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `custom_sales_invoice` varchar(140)
2025-05-30 22:59:42,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-05-30 22:59:42,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `custom_is_trade_in` int(1) not null default 0
2025-05-30 22:59:42,799 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-05-30 23:07:32,121 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-05-30 23:07:33,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-05-30 23:07:34,427 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-05-30 23:07:36,318 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-05-30 23:07:36,602 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-30 23:07:37,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-05-30 23:07:38,103 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-05-30 23:07:42,708 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-06-03 12:53:58,658 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-03 12:53:59,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-03 12:54:01,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-03 12:54:02,822 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-03 12:54:03,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-03 12:54:03,871 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type` varchar(140) default 'Sales',
`verification_code` varchar(140) unique,
`verification_status` varchar(140) default 'Pending',
`verification_date` date,
`company_name` varchar(140),
`receipt_number` varchar(140),
`subtotal` decimal(21,9) not null default 0,
`total_tax` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 12:54:04,102 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`quantity` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 12:54:04,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-03 12:54:05,219 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-03 12:54:06,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `create_cash_journal` int(1) not null default 0, ADD COLUMN `based_on_hourly_rate` int(1) not null default 0, ADD COLUMN `hourly_rate` decimal(21,9) not null default 0
2025-06-03 12:54:06,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-06-03 12:54:06,885 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `pension_fund` varchar(140), ADD COLUMN `pension_fund_number` varchar(140), ADD COLUMN `wcf_number` varchar(140), ADD COLUMN `tin_number` varchar(140), ADD COLUMN `national_identity` varchar(140), ADD COLUMN `bank_code` varchar(140)
2025-06-03 12:54:06,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-06-03 12:54:06,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0
2025-06-03 12:54:12,596 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` ADD COLUMN `custom_supplier_name` varchar(140)
2025-06-03 12:54:12,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-03 12:54:12,912 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-03 12:54:12,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-06-03 12:54:13,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-03 12:54:13,028 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `naming_series` varchar(140) default 'RE-.YYYY.-', MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0
2025-06-03 17:50:16,770 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-03 17:50:18,436 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-03 17:50:20,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-03 17:50:23,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-03 17:50:23,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-03 17:50:25,542 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-03 17:50:25,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-03 17:54:53,180 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-03 17:54:54,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-03 17:54:56,830 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-03 17:54:59,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-03 17:54:59,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-03 17:55:01,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-03 17:55:01,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-03 17:57:02,634 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-03 17:57:04,071 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-03 17:57:06,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-03 17:57:08,458 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-03 17:57:08,858 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-03 17:57:10,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-03 17:57:10,746 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-03 17:57:17,368 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0
2025-06-03 17:57:18,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0
2025-06-16 22:25:02,333 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-16 22:25:03,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-16 22:25:05,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-16 22:25:05,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-06-16 22:25:05,839 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` ADD COLUMN `ordered_qty` decimal(21,9) not null default 0
2025-06-16 22:25:05,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `wo_produced_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-06-16 22:25:06,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` MODIFY `qty_after_transaction` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `outgoing_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_value` decimal(21,9) not null default 0, MODIFY `stock_value_difference` decimal(21,9) not null default 0
2025-06-16 22:25:06,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_code`, DROP INDEX `posting_date_index`, DROP INDEX `warehouse`
2025-06-16 22:25:06,306 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_posting_datetime_creation_index`(item_code, warehouse, posting_datetime, creation)
2025-06-16 22:25:06,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0
2025-06-16 22:25:07,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD COLUMN `expense_account` varchar(140), ADD COLUMN `cost_center` varchar(140)
2025-06-16 22:25:07,309 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `current_stock` decimal(21,9) not null default 0
2025-06-16 22:25:07,837 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` ADD COLUMN `debit_note_number` varchar(140)
2025-06-16 22:25:07,861 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0
2025-06-16 22:25:08,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-16 22:25:08,896 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-16 22:25:10,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-16 22:25:10,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-16 22:25:10,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_datetime_creation_index`
2025-06-16 22:25:10,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_warehouse`
2025-06-16 22:25:15,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-06-17 15:00:23,715 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-17 15:00:24,836 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-17 15:00:26,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-17 15:00:27,754 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0
2025-06-17 15:00:28,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-17 15:00:28,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-17 15:00:29,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-17 15:00:29,927 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-17 15:00:35,642 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-17 15:40:37,321 WARNING database DDL Query made to DB:
alter table `tabCargo Detail` add column if not exists parent varchar(140)
2025-06-17 15:40:37,322 WARNING database DDL Query made to DB:
alter table `tabCargo Detail` add column if not exists parenttype varchar(140)
2025-06-17 15:40:37,324 WARNING database DDL Query made to DB:
alter table `tabCargo Detail` add column if not exists parentfield varchar(140)
2025-06-17 15:40:37,584 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Detail` ADD COLUMN `detail` varchar(140)
2025-06-17 15:40:37,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Detail` MODIFY `net_weight_tonne` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `tare_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-18 10:30:54,078 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-18 10:30:55,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-18 10:30:56,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-18 10:30:58,150 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
