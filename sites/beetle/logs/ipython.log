2025-07-09 09:58:36,203 INFO ipython === bench console session ===
2025-07-09 09:58:36,205 INFO ipython # Test the permission bypass functionality
2025-07-09 09:58:36,205 INFO ipython import frappe
2025-07-09 09:58:36,205 INFO ipython from csf_tz.csftz_hooks.employee_advance_payment_and_expense import create_payment_entry
2025-07-09 09:58:36,205 INFO ipython # Get a sample employee advance document
2025-07-09 09:58:36,205 INFO ipython advance = frappe.get_doc("Employee Advance", {"docstatus": 1, "travel_request_ref": ["!=", ""]})
2025-07-09 09:58:36,206 INFO ipython if advance:
        print(f"Testing with Employee Advance: {advance.name}")
            try:
                        result = create_payment_entry(advance)
2025-07-09 09:58:36,206 INFO ipython         print(f"Success: Payment Entry created - {result.name if result else 'None'}")
2025-07-09 09:58:36,206 INFO ipython     except Exception as e:
            print(f"Error: {str(e)}")
2025-07-09 09:58:36,206 INFO ipython else:
        print("No suitable Employee Advance found for testing")
2025-07-09 09:58:36,206 INFO ipython # Let's find an existing employee advance first
2025-07-09 09:58:36,206 INFO ipython advances = frappe.get_list("Employee Advance", filters={"docstatus": 1, "travel_request_ref": ["!=", ""]}, limit=1)
2025-07-09 09:58:36,206 INFO ipython print(f"Found advances: {advances}")
2025-07-09 09:58:36,206 INFO ipython # Check for any employee advances
2025-07-09 09:58:36,206 INFO ipython all_advances = frappe.get_list("Employee Advance", limit=5)
2025-07-09 09:58:36,207 INFO ipython print(f"All advances: {all_advances}")
2025-07-09 09:58:36,207 INFO ipython # Check for submitted advances
2025-07-09 09:58:36,207 INFO ipython submitted_advances = frappe.get_list("Employee Advance", filters={"docstatus": 1}, limit=5)
2025-07-09 09:58:36,207 INFO ipython print(f"Submitted advances: {submitted_advances}")
2025-07-09 09:58:36,207 INFO ipython === session end ===
2025-07-09 14:28:43,879 INFO ipython === bench console session ===
2025-07-09 14:28:43,882 INFO ipython # Check if there are any custom fields for Sales Order that include 'student'
2025-07-09 14:28:43,882 INFO ipython import frappe
2025-07-09 14:28:43,882 INFO ipython # Check custom fields for Sales Order
2025-07-09 14:28:43,882 INFO ipython custom_fields = frappe.get_all("Custom Field", 
    filters={"dt": "Sales Order"}, 
        fields=["fieldname", "label", "fieldtype", "options"])
2025-07-09 14:28:43,882 INFO ipython print("Custom fields for Sales Order:")
2025-07-09 14:28:43,882 INFO ipython for field in custom_fields:
        print(f"- {field.fieldname}: {field.label} ({field.fieldtype})")
            if field.options:
                        print(f"  Options: {field.options}")
2025-07-09 14:28:43,882 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site all mariadb
2025-07-09 14:28:43,883 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site all migrate
2025-07-09 14:28:43,883 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site all migrate
2025-07-09 14:28:43,883 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site all console
2025-07-09 14:28:43,883 INFO ipython cd /home/<USER>/Desktop/frappe-bench && bench --site all execute csf_tz.patches.add_student_field_to_sales_order.execute
2025-07-09 14:28:43,883 INFO ipython cd /home/<USER>/Desktop/frappe-bench && python fix_student_field.py
2025-07-09 14:28:43,883 INFO ipython === session end ===
2025-11-12 10:51:22,626 INFO ipython === bench console session ===
2025-11-12 10:51:22,626 INFO ipython import frappe
2025-11-12 10:51:22,626 INFO ipython print('FOUND' if frappe.db.exists('DocType', 'Gate Pass') else 'NOT')
2025-11-12 10:51:22,627 INFO ipython === session end ===
