["tabTherapy Session", "tabCompany", "tabClinical", "tabMedical Department", "tabAdvance", "tabSingles", "tabDocType Action", "tabPortal", "tabQuotation", "tabInpatient Record", "tabWeb Form List Column", "tabDrug Prescription", "tabPatient", "tabWeb", "tabBank", "tabTherapy", "tabDeleted Document", "tabSales Invoice Item", "tabDocPerm", "tabJournal", "tabCustomer", "tabMaterial Request", "tabItem", "tabLab Prescription", "tabIssue Materials Detail", "tabMode", "tabMedical", "tabPurchase", "tabCustom", "tabHealthcare Service Unit", "tabLab", "tabBank Transaction", "tabDiagnosis", "tabPatient En<PERSON>unter", "tabClinical Procedure Template", "tabInpatient", "tabComment", "tabStock Entry Detail", "tabTag Link", "tabPurchase Receipt Item", "tabPayment Request", "tabTag", "tabJournal Entry Account", "tabMaterial Request Item", "tabProcedure", "tabInstalled Application", "tabPurchase Invoice Item", "tabPortal Menu Item", "tabHealthcare Practitioner", "tabProperty Setter", "tabHealthcare Service Unit Type", "tabProcedure Prescription", "tabBank Guarantee", "tabDeleted", "tabClinical Procedure Item", "tabDocType Link", "tabDocType", "tabSales", "tabError", "tabPrint Heading", "tabPayment", "tabPatient Appointment", "tabDocType State", "tabJournal Entry", "tabProperty", "tabCustom Field", "tabVehicle", "tabIssue", "tabTherapy Type", "tabInstalled", "tabPrint", "tabMaterial", "tabEmployee", "tabSales Invoice", "tabStock", "tabDocField", "tabAdvance Payment Ledger Entry", "tabHealthcare", "tabTherapy Plan Detail", "tabBank Account", "tabVehicle Service", "tabError Log", "tabLab Test Template", "tabVehicle Log", "tabDrug"]