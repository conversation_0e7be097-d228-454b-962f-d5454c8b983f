2025-10-21 09:26:18,964 WARNING database DDL Query made to DB:
create table `tabGrievance Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:19,052 WARNING database DDL Query made to DB:
create table `tabShift Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140) unique,
`checkin_radius` int(11) not null default 0,
`latitude` decimal(21,9) not null default 0,
`longitude` decimal(21,9) not null default 0,
`geolocation` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:19,169 WARNING database DDL Query made to DB:
create table `tabJob Applicant` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`applicant_name` varchar(140),
`email_id` varchar(140),
`phone_number` varchar(140),
`country` varchar(140),
`job_title` varchar(140),
`designation` varchar(140),
`status` varchar(140),
`source` varchar(140),
`source_name` varchar(140),
`employee_referral` varchar(140),
`applicant_rating` decimal(3,2),
`notes` varchar(140),
`cover_letter` text,
`resume_attachment` text,
`resume_link` varchar(140),
`currency` varchar(140),
`lower_range` decimal(21,0) not null default 0,
`upper_range` decimal(21,0) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `job_title`(`job_title`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:19,246 WARNING database DDL Query made to DB:
create table `tabSkill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill_name` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:19,410 WARNING database DDL Query made to DB:
create table `tabAttendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`working_hours` decimal(21,1) not null default 0,
`status` varchar(140) default 'Present',
`leave_type` varchar(140),
`leave_application` varchar(140),
`attendance_date` date,
`company` varchar(140),
`department` varchar(140),
`attendance_request` varchar(140),
`half_day_status` varchar(140),
`shift` varchar(140),
`in_time` datetime(6),
`out_time` datetime(6),
`late_entry` int(1) not null default 0,
`early_exit` int(1) not null default 0,
`amended_from` varchar(140),
`modify_half_day_status` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `status`(`status`),
index `attendance_date`(`attendance_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:19,500 WARNING database DDL Query made to DB:
create table `tabEmployee Property History` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`property` varchar(140),
`current` varchar(140),
`new` varchar(140),
`fieldname` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:19,571 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary Group User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`email` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:19,661 WARNING database DDL Query made to DB:
create table `tabShift Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`start_time` time(6),
`end_time` time(6),
`holiday_list` varchar(140),
`color` varchar(140) default 'Blue',
`enable_auto_attendance` int(1) not null default 0,
`determine_check_in_and_check_out` varchar(140),
`working_hours_calculation_based_on` varchar(140),
`begin_check_in_before_shift_start_time` int(11) not null default 60,
`allow_check_out_after_shift_end_time` int(11) not null default 60,
`mark_auto_attendance_on_holidays` int(1) not null default 0,
`working_hours_threshold_for_half_day` decimal(21,1) not null default 0,
`working_hours_threshold_for_absent` decimal(21,1) not null default 0,
`process_attendance_after` date,
`last_sync_of_checkin` datetime(6),
`auto_update_last_sync` int(1) not null default 0,
`enable_late_entry_marking` int(1) not null default 0,
`late_entry_grace_period` int(11) not null default 0,
`enable_early_exit_marking` int(1) not null default 0,
`early_exit_grace_period` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:19,744 WARNING database DDL Query made to DB:
create table `tabTraining Event Employee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`status` varchar(140) default 'Open',
`attendance` varchar(140),
`is_mandatory` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:19,840 WARNING database DDL Query made to DB:
create table `tabLeave Ledger Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`leave_type` varchar(140),
`transaction_type` varchar(140),
`transaction_name` varchar(140),
`company` varchar(140),
`leaves` decimal(21,9) not null default 0,
`from_date` date,
`to_date` date,
`holiday_list` varchar(140),
`is_carry_forward` int(1) not null default 0,
`is_expired` int(1) not null default 0,
`is_lwp` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `transaction_type`(`transaction_type`),
index `transaction_name`(`transaction_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:19,905 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Ledger Entry`
				ADD INDEX IF NOT EXISTS `transaction_type_transaction_name_index`(transaction_type, transaction_name)
2025-10-21 09:26:19,959 WARNING database DDL Query made to DB:
create table `tabTraining Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`training_event` varchar(140) unique,
`amended_from` varchar(140),
`employee_emails` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,040 WARNING database DDL Query made to DB:
create table `tabEmployee Skill Map` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140) unique,
`employee_name` varchar(140),
`designation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,112 WARNING database DDL Query made to DB:
create table `tabEmployee Grade` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_salary_structure` varchar(140),
`currency` varchar(140),
`default_base_pay` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,200 WARNING database DDL Query made to DB:
create table `tabLeave Policy Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`leave_policy` varchar(140),
`carry_forward` int(1) not null default 0,
`assignment_based_on` varchar(140),
`leave_period` varchar(140),
`effective_from` date,
`effective_to` date,
`leaves_allocated` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,354 WARNING database DDL Query made to DB:
create table `tabLeave Policy` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,442 WARNING database DDL Query made to DB:
create table `tabTraining Result Employee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`hours` decimal(21,9) not null default 0,
`grade` varchar(140),
`comments` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,512 WARNING database DDL Query made to DB:
create table `tabInterviewer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,590 WARNING database DDL Query made to DB:
create table `tabCompensatory Leave Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`leave_type` varchar(140),
`leave_allocation` varchar(140),
`work_from_date` date,
`work_end_date` date,
`half_day` int(1) not null default 0,
`half_day_date` date,
`reason` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,682 WARNING database DDL Query made to DB:
create table `tabStaffing Plan Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`designation` varchar(140),
`vacancies` int(11) not null default 0,
`estimated_cost_per_position` decimal(21,9) not null default 0,
`total_estimated_cost` decimal(21,9) not null default 0,
`current_count` int(11) not null default 0,
`current_openings` int(11) not null default 0,
`number_of_positions` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,759 WARNING database DDL Query made to DB:
create table `tabExpense Claim Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`deferred_expense_account` int(1) not null default 0,
`expense_type` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,832 WARNING database DDL Query made to DB:
create table `tabPurpose of Travel` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`purpose_of_travel` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,904 WARNING database DDL Query made to DB:
create table `tabExpense Claim Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`default_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:20,981 WARNING database DDL Query made to DB:
create table `tabEmployee Promotion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`salary_currency` varchar(140),
`promotion_date` date,
`company` varchar(140),
`current_ctc` decimal(21,9) not null default 0,
`revised_ctc` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:21,095 WARNING database DDL Query made to DB:
create table `tabAppraisal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_image` text,
`company` varchar(140),
`appraisal_cycle` varchar(140),
`start_date` date,
`end_date` date,
`final_score` decimal(21,9) not null default 0,
`appraisal_template` varchar(140),
`rate_goals_manually` int(1) not null default 0,
`goal_score_percentage` decimal(21,9) not null default 0,
`remarks` text,
`total_score` decimal(21,9) not null default 0,
`avg_feedback_score` decimal(21,9) not null default 0,
`self_score` decimal(21,9) not null default 0,
`reflections` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:21,205 WARNING database DDL Query made to DB:
create table `tabLeave Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_type_name` varchar(140) unique,
`max_leaves_allowed` decimal(21,9) not null default 0,
`applicable_after` int(11) not null default 0,
`max_continuous_days_allowed` int(11) not null default 0,
`is_carry_forward` int(1) not null default 0,
`is_lwp` int(1) not null default 0,
`is_ppl` int(1) not null default 0,
`fraction_of_daily_salary_per_leave` decimal(21,9) not null default 0,
`is_optional_leave` int(1) not null default 0,
`allow_negative` int(1) not null default 0,
`allow_over_allocation` int(1) not null default 0,
`include_holiday` int(1) not null default 0,
`is_compensatory` int(1) not null default 0,
`maximum_carry_forwarded_leaves` decimal(21,9) not null default 0,
`expire_carry_forwarded_leaves_after_days` int(11) not null default 0,
`allow_encashment` int(1) not null default 0,
`max_encashable_leaves` int(11) not null default 0,
`non_encashable_leaves` int(11) not null default 0,
`earning_component` varchar(140),
`is_earned_leave` int(1) not null default 0,
`earned_leave_frequency` varchar(140),
`allocate_on_day` varchar(140) default 'Last Day',
`rounding` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:21,288 WARNING database DDL Query made to DB:
create table `tabExpense Claim Advance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee_advance` varchar(140),
`posting_date` date,
`advance_paid` decimal(21,9) not null default 0,
`unclaimed_amount` decimal(21,9) not null default 0,
`return_amount` decimal(21,9) not null default 0,
`allocated_amount` decimal(21,9) not null default 0,
`advance_account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:21,426 WARNING database DDL Query made to DB:
create table `tabEmployee Transfer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`transfer_date` date,
`company` varchar(140),
`new_company` varchar(140),
`department` varchar(140),
`reallocate_leaves` int(1) not null default 0,
`create_new_employee_id` int(1) not null default 0,
`new_employee_id` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:21,517 WARNING database DDL Query made to DB:
create table `tabEmployee Separation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`employee_grade` varchar(140),
`company` varchar(140),
`boarding_status` varchar(140) default 'Pending',
`resignation_letter_date` date,
`boarding_begins_on` date,
`project` varchar(140),
`employee_separation_template` varchar(140),
`notify_users_by_email` int(1) not null default 0,
`exit_interview` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:21,600 WARNING database DDL Query made to DB:
create table `tabAppraisal Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_title` varchar(140) unique,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:21,673 WARNING database DDL Query made to DB:
create table `tabDesignation Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:21,756 WARNING database DDL Query made to DB:
create table `tabFull and Final Statement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`transaction_date` date,
`company` varchar(140),
`status` varchar(140) default 'Unpaid',
`amended_from` varchar(140),
`date_of_joining` date,
`relieving_date` date,
`designation` varchar(140),
`department` varchar(140),
`total_asset_recovery_cost` decimal(21,9) not null default 0,
`total_payable_amount` decimal(21,9) not null default 0,
`total_receivable_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:21,837 WARNING database DDL Query made to DB:
create table `tabVehicle Service` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_item` varchar(140),
`type` varchar(140),
`frequency` varchar(140),
`expense_amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:21,921 WARNING database DDL Query made to DB:
create table `tabEmployee Performance Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`company` varchar(140),
`reviewer` varchar(140),
`reviewer_name` varchar(140),
`reviewer_designation` varchar(140),
`user` varchar(140),
`added_on` datetime(6),
`appraisal_cycle` varchar(140),
`appraisal` varchar(140),
`total_score` decimal(21,9) not null default 0,
`feedback` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:22,027 WARNING database DDL Query made to DB:
create table `tabTravel Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`travel_type` varchar(140),
`travel_funding` varchar(140),
`travel_proof` text,
`purpose_of_travel` varchar(140),
`details_of_sponsor` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`cell_number` varchar(140),
`prefered_email` varchar(140),
`company` varchar(140),
`date_of_birth` date,
`personal_id_type` varchar(140),
`personal_id_number` varchar(140),
`passport_number` varchar(140),
`description` text,
`cost_center` varchar(140),
`name_of_organizer` varchar(140),
`address_of_organizer` varchar(140),
`other_details` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:22,166 WARNING database DDL Query made to DB:
create table `tabDaily Work Summary Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`send_emails_at` varchar(140),
`holiday_list` varchar(140),
`subject` varchar(140) default 'What did you work on today?',
`message` longtext default '<p>Please share what did you do today. If you reply by midnight, your response will be recorded!</p>',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:22,256 WARNING database DDL Query made to DB:
create table `tabEmployee Health Insurance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`health_insurance_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:22,332 WARNING database DDL Query made to DB:
create table `tabInterview Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interviewer` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:22,409 WARNING database DDL Query made to DB:
create table `tabEmployee Feedback Rating` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`criteria` varchar(140),
`per_weightage` decimal(21,9) not null default 0,
`rating` decimal(3,2),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:22,494 WARNING database DDL Query made to DB:
create table `tabTraining Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`training_program` varchar(140),
`status` varchar(140) default 'Scheduled',
`company` varchar(140),
`trainer_name` varchar(140),
`trainer_email` varchar(140),
`supplier` varchar(140),
`contact_number` varchar(140),
`description` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:22,565 WARNING database DDL Query made to DB:
create table `tabAppraisal Template Goal` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`key_result_area` varchar(140),
`per_weightage` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:23,285 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Declaration Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_sub_category` varchar(140),
`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:23,360 WARNING database DDL Query made to DB:
create table `tabSalary Withholding Cycle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_date` date,
`to_date` date,
`is_salary_released` int(1) not null default 0,
`journal_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:23,489 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`max_amount` decimal(21,9) not null default 0,
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:23,591 WARNING database DDL Query made to DB:
create table `tabGratuity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`current_work_experience` decimal(21,9) not null default 0,
`posting_date` date,
`gratuity_rule` varchar(140),
`status` varchar(140) default 'Draft',
`company` varchar(140),
`amended_from` varchar(140),
`pay_via_salary_slip` int(1) not null default 1,
`amount` decimal(21,9) not null default 0,
`paid_amount` decimal(21,9) not null default 0,
`payroll_date` date,
`salary_component` varchar(140),
`cost_center` varchar(140),
`mode_of_payment` varchar(140),
`expense_account` varchar(140),
`payable_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:23,690 WARNING database DDL Query made to DB:
create table `tabEmployee Other Income` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amended_from` varchar(140),
`company` varchar(140),
`payroll_period` varchar(140),
`source` varchar(140),
`amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `company`(`company`),
index `payroll_period`(`payroll_period`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:23,787 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Proof Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`submission_date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`total_actual_amount` decimal(21,9) not null default 0,
`exemption_amount` decimal(21,9) not null default 0,
`attachments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:23,870 WARNING database DDL Query made to DB:
create table `tabPayroll Period Date` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`start_date` date,
`end_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:23,940 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`earning_component` varchar(140),
`pay_against_benefit_claim` int(1) not null default 0,
`max_benefit_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:24,016 WARNING database DDL Query made to DB:
create table `tabPayroll Period` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`start_date` date,
`end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:24,102 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Proof Submission Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_sub_category` varchar(140),
`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`type_of_proof` varchar(140),
`attach_proof` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:24,204 WARNING database DDL Query made to DB:
create table `tabSalary Structure Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`grade` varchar(140),
`salary_structure` varchar(140),
`from_date` date,
`income_tax_slab` varchar(140),
`company` varchar(140),
`payroll_payable_account` varchar(140),
`currency` varchar(140),
`base` decimal(21,9) not null default 0,
`variable` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`taxable_earnings_till_date` decimal(21,9) not null default 0,
`tax_deducted_till_date` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `salary_structure`(`salary_structure`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:24,322 WARNING database DDL Query made to DB:
create table `tabRetention Bonus` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`date_of_joining` varchar(140),
`salary_component` varchar(140),
`bonus_amount` decimal(21,9) not null default 0,
`bonus_payment_date` date,
`currency` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:24,510 WARNING database DDL Query made to DB:
create table `tabSalary Slip` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`branch` varchar(140),
`posting_date` date,
`letter_head` varchar(140),
`status` varchar(140),
`salary_withholding` varchar(140),
`salary_withholding_cycle` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 1.0,
`payroll_frequency` varchar(140),
`start_date` date,
`end_date` date,
`salary_structure` varchar(140),
`payroll_entry` varchar(140),
`mode_of_payment` varchar(140),
`salary_slip_based_on_timesheet` int(1) not null default 0,
`deduct_tax_for_unclaimed_employee_benefits` int(1) not null default 0,
`deduct_tax_for_unsubmitted_tax_exemption_proof` int(1) not null default 0,
`total_working_days` decimal(21,9) not null default 0,
`unmarked_days` decimal(21,9) not null default 0,
`leave_without_pay` decimal(21,9) not null default 0,
`absent_days` decimal(21,9) not null default 0,
`payment_days` decimal(21,9) not null default 0,
`total_working_hours` decimal(21,9) not null default 0,
`hour_rate` decimal(21,9) not null default 0,
`base_hour_rate` decimal(21,9) not null default 0,
`gross_pay` decimal(21,9) not null default 0,
`base_gross_pay` decimal(21,9) not null default 0,
`gross_year_to_date` decimal(21,9) not null default 0,
`base_gross_year_to_date` decimal(21,9) not null default 0,
`total_deduction` decimal(21,9) not null default 0,
`base_total_deduction` decimal(21,9) not null default 0,
`net_pay` decimal(21,9) not null default 0,
`base_net_pay` decimal(21,9) not null default 0,
`rounded_total` decimal(21,9) not null default 0,
`base_rounded_total` decimal(21,9) not null default 0,
`year_to_date` decimal(21,9) not null default 0,
`base_year_to_date` decimal(21,9) not null default 0,
`month_to_date` decimal(21,9) not null default 0,
`base_month_to_date` decimal(21,9) not null default 0,
`total_in_words` varchar(240),
`base_total_in_words` varchar(240),
`ctc` decimal(21,9) not null default 0,
`income_from_other_sources` decimal(21,9) not null default 0,
`total_earnings` decimal(21,9) not null default 0,
`non_taxable_earnings` decimal(21,9) not null default 0,
`standard_tax_exemption_amount` decimal(21,9) not null default 0,
`tax_exemption_declaration` decimal(21,9) not null default 0,
`deductions_before_tax_calculation` decimal(21,9) not null default 0,
`annual_taxable_amount` decimal(21,9) not null default 0,
`income_tax_deducted_till_date` decimal(21,9) not null default 0,
`current_month_income_tax` decimal(21,9) not null default 0,
`future_income_tax_deductions` decimal(21,9) not null default 0,
`total_income_tax` decimal(21,9) not null default 0,
`journal_entry` varchar(140),
`amended_from` varchar(140),
`bank_name` varchar(140),
`bank_account_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `start_date`(`start_date`),
index `end_date`(`end_date`),
index `salary_structure`(`salary_structure`),
index `payroll_entry`(`payroll_entry`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:24,585 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip`
				ADD INDEX IF NOT EXISTS `employee_start_date_end_date_index`(employee, start_date, end_date)
2025-10-21 09:26:24,648 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`claim_date` date,
`currency` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`earning_component` varchar(140),
`max_amount_eligible` decimal(21,9) not null default 0,
`pay_against_benefit_claim` int(1) not null default 0,
`claimed_amount` decimal(21,9) not null default 0,
`salary_slip` varchar(140),
`attachments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:24,740 WARNING database DDL Query made to DB:
create table `tabEmployee Incentive` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`amended_from` varchar(140),
`company` varchar(140),
`department` varchar(140),
`salary_component` varchar(140),
`currency` varchar(140),
`payroll_date` date,
`incentive_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:24,824 WARNING database DDL Query made to DB:
create table `tabEmployee Cost Center` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cost_center` varchar(140),
`percentage` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:24,950 WARNING database DDL Query made to DB:
create table `tabSalary Component Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`account` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,024 WARNING database DDL Query made to DB:
create table `tabPayroll Employee Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`is_salary_withheld` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,126 WARNING database DDL Query made to DB:
create table `tabAdditional Salary` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`is_recurring` int(1) not null default 0,
`disabled` int(1) not null default 0,
`from_date` date,
`to_date` date,
`payroll_date` date,
`amended_from` varchar(140),
`salary_component` varchar(140),
`type` varchar(140),
`currency` varchar(140),
`amount` decimal(21,9) not null default 0,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`overwrite_salary_structure_amount` int(1) not null default 1,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `payroll_date`(`payroll_date`),
index `salary_component`(`salary_component`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,237 WARNING database DDL Query made to DB:
create table `tabPayroll Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` date,
`company` varchar(140),
`currency` varchar(140),
`exchange_rate` decimal(21,9) not null default 0,
`payroll_payable_account` varchar(140),
`status` varchar(140),
`salary_slip_based_on_timesheet` int(1) not null default 0,
`payroll_frequency` varchar(140),
`start_date` date,
`end_date` date,
`deduct_tax_for_unclaimed_employee_benefits` int(1) not null default 0,
`deduct_tax_for_unsubmitted_tax_exemption_proof` int(1) not null default 0,
`branch` varchar(140),
`department` varchar(140),
`designation` varchar(140),
`grade` varchar(140),
`number_of_employees` int(11) not null default 0,
`validate_attendance` int(1) not null default 0,
`cost_center` varchar(140),
`project` varchar(140),
`payment_account` varchar(140),
`bank_account` varchar(140),
`salary_slips_created` int(1) not null default 0,
`salary_slips_submitted` int(1) not null default 0,
`error_message` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,342 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Declaration` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`company` varchar(140),
`payroll_period` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`total_declared_amount` decimal(21,9) not null default 0,
`total_exemption_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,427 WARNING database DDL Query made to DB:
create table `tabTaxable Salary Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_amount` decimal(21,9) not null default 0,
`to_amount` decimal(21,9) not null default 0,
`percent_deduction` decimal(21,9) not null default 0,
`condition` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,498 WARNING database DDL Query made to DB:
create table `tabSalary Slip Timesheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`time_sheet` varchar(140),
`working_hours` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,575 WARNING database DDL Query made to DB:
create table `tabSalary Slip Loan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`loan` varchar(140),
`loan_product` varchar(140),
`loan_account` varchar(140),
`interest_income_account` varchar(140),
`principal_amount` decimal(21,9) not null default 0,
`interest_amount` decimal(21,9) not null default 0,
`total_payment` decimal(21,9) not null default 0,
`loan_repayment_entry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,646 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab Other Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`percent` decimal(21,9) not null default 0,
`min_taxable_income` decimal(21,9) not null default 0,
`max_taxable_income` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,755 WARNING database DDL Query made to DB:
create table `tabSalary Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
`abbr` varchar(140),
`amount` decimal(21,9) not null default 0,
`year_to_date` decimal(21,9) not null default 0,
`additional_salary` varchar(140),
`is_recurring_additional_salary` int(1) not null default 0,
`statistical_component` int(1) not null default 0,
`depends_on_payment_days` int(1) not null default 0,
`exempted_from_income_tax` int(1) not null default 0,
`is_tax_applicable` int(1) not null default 0,
`is_flexible_benefit` int(1) not null default 0,
`variable_based_on_taxable_salary` int(1) not null default 0,
`do_not_include_in_total` int(1) not null default 0,
`do_not_include_in_accounts` int(1) not null default 0,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`condition` longtext,
`amount_based_on_formula` int(1) not null default 0,
`formula` longtext,
`default_amount` decimal(21,9) not null default 0,
`additional_amount` decimal(21,9) not null default 0,
`tax_on_flexible_benefit` decimal(21,9) not null default 0,
`tax_on_additional_salary` decimal(21,9) not null default 0,
index `salary_component`(`salary_component`),
index `exempted_from_income_tax`(`exempted_from_income_tax`),
index `is_tax_applicable`(`is_tax_applicable`),
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,838 WARNING database DDL Query made to DB:
create table `tabGratuity Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disable` int(1) not null default 0,
`calculate_gratuity_amount_based_on` varchar(140),
`total_working_days_per_year` decimal(21,9) not null default 365.0,
`work_experience_calculation_function` varchar(140) default 'Round off Work Experience',
`minimum_year_for_gratuity` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,913 WARNING database DDL Query made to DB:
create table `tabGratuity Applicable Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:25,980 WARNING database DDL Query made to DB:
create table `tabSalary Slip Leave` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`leave_type` varchar(140),
`total_allocated_leaves` decimal(21,9) not null default 0,
`expired_leaves` decimal(21,9) not null default 0,
`used_leaves` decimal(21,9) not null default 0,
`pending_leaves` decimal(21,9) not null default 0,
`available_leaves` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:26,064 WARNING database DDL Query made to DB:
create table `tabEmployee Benefit Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`department` varchar(140),
`date` date,
`payroll_period` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`currency` varchar(140),
`max_benefits` decimal(21,9) not null default 0,
`remaining_benefit` decimal(21,9) not null default 0,
`total_amount` decimal(21,9) not null default 0,
`pro_rata_dispensed_amount` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:26,188 WARNING database DDL Query made to DB:
create table `tabSalary Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_component` varchar(140) unique,
`salary_component_abbr` varchar(140),
`type` varchar(140),
`description` text,
`depends_on_payment_days` int(1) not null default 1,
`is_tax_applicable` int(1) not null default 1,
`deduct_full_tax_on_selected_payroll_date` int(1) not null default 0,
`variable_based_on_taxable_salary` int(1) not null default 0,
`is_income_tax_component` int(1) not null default 0,
`exempted_from_income_tax` int(1) not null default 0,
`round_to_the_nearest_integer` int(1) not null default 0,
`statistical_component` int(1) not null default 0,
`do_not_include_in_total` int(1) not null default 0,
`do_not_include_in_accounts` int(1) not null default 0,
`remove_if_zero_valued` int(1) not null default 1,
`disabled` int(1) not null default 0,
`condition` longtext,
`amount` decimal(21,9) not null default 0,
`amount_based_on_formula` int(1) not null default 0,
`formula` longtext,
`is_flexible_benefit` int(1) not null default 0,
`max_benefit_amount` decimal(21,9) not null default 0,
`pay_against_benefit_claim` int(1) not null default 0,
`only_tax_impact` int(1) not null default 0,
`create_separate_payment_entry_against_benefit_claim` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `variable_based_on_taxable_salary`(`variable_based_on_taxable_salary`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:26,305 WARNING database DDL Query made to DB:
create table `tabSalary Withholding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`employee` varchar(140),
`employee_name` varchar(140),
`company` varchar(140),
`posting_date` date,
`payroll_frequency` varchar(140),
`number_of_withholding_cycles` int(11) not null default 0,
`status` varchar(140) default 'Draft',
`from_date` date,
`to_date` date,
`date_of_joining` date,
`relieving_date` date,
`reason_for_withholding_salary` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `employee`(`employee`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:26,398 WARNING database DDL Query made to DB:
create table `tabIncome Tax Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`effective_from` date,
`company` varchar(140),
`currency` varchar(140),
`standard_tax_exemption_amount` decimal(21,9) not null default 0,
`allow_tax_exemption` int(1) not null default 0,
`amended_from` varchar(140),
`tax_relief_limit` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:26,479 WARNING database DDL Query made to DB:
create table `tabEmployee Tax Exemption Sub Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exemption_category` varchar(140),
`max_amount` decimal(21,9) not null default 0,
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:26,560 WARNING database DDL Query made to DB:
create table `tabGratuity Rule Slab` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_year` int(11) not null default 0,
`to_year` int(11) not null default 0,
`fraction_of_applicable_earnings` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:26,673 WARNING database DDL Query made to DB:
create table `tabSalary Structure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`letter_head` varchar(140),
`is_active` varchar(140) default 'Yes',
`is_default` varchar(140) default 'No',
`currency` varchar(140),
`amended_from` varchar(140),
`leave_encashment_amount_per_day` decimal(21,9) not null default 0,
`max_benefits` decimal(21,9) not null default 0,
`salary_slip_based_on_timesheet` int(1) not null default 0,
`payroll_frequency` varchar(140) default 'Monthly',
`salary_component` varchar(140),
`hour_rate` decimal(21,9) not null default 0,
`total_earning` decimal(21,9) not null default 0,
`total_deduction` decimal(21,9) not null default 0,
`net_pay` decimal(21,9) not null default 0,
`mode_of_payment` varchar(140),
`payment_account` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `currency`(`currency`),
index `salary_slip_based_on_timesheet`(`salary_slip_based_on_timesheet`),
index `payroll_frequency`(`payroll_frequency`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:26:27,974 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerms and Conditions` ADD COLUMN `hr` int(1) not null default 1
2025-10-21 09:26:28,041 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_expense_claim_payable_account` varchar(140), ADD COLUMN `default_employee_advance_account` varchar(140), ADD COLUMN `default_payroll_payable_account` varchar(140)
2025-10-21 09:26:28,067 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-10-21 09:26:28,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employment_type` varchar(140), ADD COLUMN `grade` varchar(140), ADD COLUMN `job_applicant` varchar(140), ADD COLUMN `default_shift` varchar(140), ADD COLUMN `expense_approver` varchar(140), ADD COLUMN `leave_approver` varchar(140), ADD COLUMN `shift_request_approver` varchar(140), ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `health_insurance_provider` varchar(140), ADD COLUMN `health_insurance_no` varchar(140)
2025-10-21 09:26:28,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-10-21 09:26:28,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation` ADD COLUMN `appraisal_template` varchar(140)
2025-10-21 09:26:28,252 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` ADD COLUMN `salary_slip` varchar(140)
2025-10-21 09:26:28,275 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet` MODIFY `base_total_billable_amount` decimal(21,9) not null default 0, MODIFY `base_total_billed_amount` decimal(21,9) not null default 0, MODIFY `base_total_costing_amount` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total_billed_hours` decimal(21,9) not null default 0, MODIFY `total_billable_hours` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0
2025-10-21 09:26:28,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` ADD COLUMN `total_expense_claim` decimal(21,9) not null default 0
2025-10-21 09:26:28,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject` MODIFY `total_billable_amount` decimal(21,9) not null default 0, MODIFY `total_consumed_material_cost` decimal(21,9) not null default 0, MODIFY `per_gross_margin` decimal(21,9) not null default 0, MODIFY `percent_complete` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `total_billed_amount` decimal(21,9) not null default 0, MODIFY `gross_margin` decimal(21,9) not null default 0, MODIFY `total_purchase_cost` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `estimated_costing` decimal(21,9) not null default 0, MODIFY `total_sales_amount` decimal(21,9) not null default 0
2025-10-21 09:26:28,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment` ADD COLUMN `payroll_cost_center` varchar(140), ADD COLUMN `leave_block_list` varchar(140)
2025-10-21 09:26:28,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` ADD COLUMN `total_expense_claim` decimal(21,9) not null default 0
2025-10-21 09:26:28,497 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `total_billing_amount` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `task_weight` decimal(21,9) not null default 0, MODIFY `progress` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0
2025-10-21 09:27:58,649 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-21 09:27:59,291 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-21 09:27:59,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-21 09:29:12,858 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-21 09:29:13,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-21 09:29:14,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-21 09:29:54,767 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-21 09:29:56,022 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-21 09:29:57,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-21 09:32:24,229 WARNING database DDL Query made to DB:
create table `tabKC Job Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_id` varchar(140),
`job_description` text,
`asset` varchar(140),
`sub_asset` varchar(140),
`work_order` varchar(140),
`department` varchar(140),
`shutdown_type` varchar(140),
`maintenance_type` varchar(140),
`warranty` int(1) not null default 0,
`assigned_to` varchar(140),
`start_date` date,
`end_date` date,
`total_hours` decimal(21,9) not null default 0,
`total_cost` decimal(21,9) not null default 0,
`status` varchar(140) default 'Draft',
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:32:24,341 WARNING database DDL Query made to DB:
create table `tabKC Job Progress` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`date` date,
`technician` varchar(140),
`work_done` text,
`manhours` decimal(21,2) not null default 0,
`materials_used` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:32:24,435 WARNING database DDL Query made to DB:
create table `tabInterworks Order Items` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`drawing_no` varchar(140),
`quantity` decimal(21,3) not null default 0,
`uom` varchar(140),
`remarks` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:32:24,541 WARNING database DDL Query made to DB:
create table `tabInterworks Order` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_department` varchar(140),
`target_department` varchar(140),
`job_card` varchar(140),
`order_date` date,
`completion_date` date,
`approval_status` varchar(140) default 'Draft',
`authorized_by` varchar(140),
`received_by` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:32:24,671 WARNING database DDL Query made to DB:
create table `tabAsset Tree` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`asset_name` varchar(140) unique,
`parent_asset` varchar(140),
`asset_code` varchar(140) unique,
`description` text,
`asset_category` varchar(140),
`location` varchar(140),
`status` varchar(140) default 'Active',
`is_group` int(1) not null default 0,
`specifications` text,
`maintenance_schedule` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 09:32:24,772 WARNING database DDL Query made to DB:
create table `tabKC Job Safety Checklist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`safety_item` varchar(140),
`description` text,
`is_completed` int(1) not null default 0,
`checked_by` varchar(140),
`check_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-21 16:49:56,339 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-21 16:49:57,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-21 16:49:58,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-21 17:34:14,587 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-21 17:34:14,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Tree`
				CHANGE COLUMN `parent_asset`
				`parent_asset_tree`
				varchar(140)
2025-10-21 17:34:17,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-21 17:34:20,368 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-22 09:14:32,784 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-22 09:14:33,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-22 09:14:34,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-22 09:14:35,708 WARNING database DDL Query made to DB:
ALTER TABLE `tabKC Job Card` ADD COLUMN `cost_center` varchar(140)
2025-10-22 09:14:35,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabKC Job Card` MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `total_hours` decimal(21,9) not null default 0
2025-10-22 09:14:35,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Tree` ADD COLUMN `lft` int(11) not null default 0, ADD COLUMN `rgt` int(11) not null default 0, ADD COLUMN `old_parent` varchar(140)
2025-10-22 09:21:20,857 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-22 09:21:21,788 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-22 09:21:22,725 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-22 09:23:54,549 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-22 09:23:55,497 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-22 09:23:56,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
