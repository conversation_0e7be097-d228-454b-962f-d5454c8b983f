2025-08-21 10:03:58,991 INFO ipython === bench console session ===
2025-08-21 10:03:58,991 INFO ipython import frappe
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee OT Component", force=True)
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee Education", force=True)
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee External Work History", force=True)
2025-08-21 10:03:58,992 INFO ipython frappe.reload_doctype("Employee Internal Work History", force=True)
2025-08-21 10:03:58,992 INFO ipython === session end ===
2025-11-12 10:51:09,712 INFO ipython === bench console session ===
2025-11-12 10:51:09,712 INFO ipython import frappe
2025-11-12 10:51:09,712 INFO ipython print(frappe.get_all('DocType', filters={'name': 'Gate Pass'}))
2025-11-12 10:51:09,712 INFO ipython === session end ===
2025-11-12 10:51:27,535 INFO ipython === bench console session ===
2025-11-12 10:51:27,535 INFO ipython import frappe
2025-11-12 10:51:27,536 INFO ipython print('FOUND' if frappe.db.exists('DocType', 'Gate Pass') else 'NOT')
2025-11-12 10:51:27,536 INFO ipython === session end ===
