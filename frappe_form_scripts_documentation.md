# Comprehensive Guide to Frappe Form Scripts, Form API, Form Events, and Child Table Events

## Table of Contents
1. [Introduction](#introduction)
2. [Form Scripts Overview](#form-scripts-overview)
3. [Form Events](#form-events)
4. [Child Table Events](#child-table-events)
5. [Form API Methods](#form-api-methods)
6. [Real-World Examples](#real-world-examples)
7. [Client Script in Frappe Desk](#client-script-in-frappe-desk)
8. [Best Practices](#best-practices)

## Introduction

Form Scripts in Frappe Framework provide powerful client-side customization capabilities for forms. They allow you to add dynamic behavior, validation, automation, and enhanced user experience to your DocType forms. This guide covers everything you need to know about Form Scripts, Form Events, Child Table Events, and the Form API with real-world examples from Frappe, ERPNext, Drive, and HRMS.

## Form Scripts Overview

### What are Form Scripts?

Form Scripts are JavaScript files that define client-side logic for forms. They are executed in the user's browser and provide a way to:
- Add custom validations
- Automate field calculations
- Control field visibility and properties
- Add custom buttons and actions
- Handle user interactions
- Manipulate child tables

### Types of Form Scripts

1. **Standard Form Scripts**: Created automatically when you create a DocType (`{doctype}.js`)
2. **Custom Form Scripts**: Created through Client Script DocType for site-specific customizations

### Basic Syntax

```javascript
frappe.ui.form.on('DocType Name', {
    event_name(frm) {
        // Your code here
    },
    
    field_name(frm) {
        // Triggered when field_name changes
    }
});
```

## Form Events

Form events are triggered at different stages of the form lifecycle. Here's a comprehensive list:

### Core Form Events

| Event Name | When Triggered | Use Case |
|------------|----------------|----------|
| `setup` | Once when form is created for the first time | Initialize form settings, set queries |
| `before_load` | Before form is about to load | Prepare data before loading |
| `onload` | When form is loaded and about to render | Set initial values, configure fields |
| `refresh` | When form is loaded and rendered | Add buttons, set field properties |
| `onload_post_render` | After form is loaded and rendered | Final UI adjustments |
| `validate` | Before `before_save` | Client-side validation |
| `before_save` | Before save is called | Last-minute data preparation |
| `after_save` | After form is saved | Post-save actions |
| `before_submit` | Before submit is called | Pre-submission validation |
| `on_submit` | After form is submitted | Post-submission actions |
| `before_cancel` | Before cancel is called | Pre-cancellation checks |
| `after_cancel` | After form is cancelled | Post-cancellation cleanup |
| `timeline_refresh` | After form timeline is rendered | Timeline customizations |

### Field-Specific Events

```javascript
frappe.ui.form.on('DocType Name', {
    field_name(frm) {
        // Triggered when field_name value changes
    }
});
```

## Child Table Events

Child table events are triggered in the context of table fields and provide parameters: `frm`, `cdt` (Child DocType), and `cdn` (Child Docname).

### Child Table Event Types

| Event Name | Description | Parameters |
|------------|-------------|------------|
| `{fieldname}_add` | When a row is added to table | `frm, cdt, cdn` |
| `{fieldname}_remove` | When a row is removed from table | `frm, cdt, cdn` |
| `before_{fieldname}_remove` | Before a row is removed | `frm, cdt, cdn` |
| `{fieldname}_move` | When a row is reordered | `frm, cdt, cdn` |
| `form_render` | When a row is opened as form | `frm, cdt, cdn` |
| `{fieldname}_on_form_rendered` | When row form is rendered | `frm, cdt, cdn` |

### Child Table Field Events

```javascript
frappe.ui.form.on('Child DocType Name', {
    child_field_name(frm, cdt, cdn) {
        let row = locals[cdt][cdn];
        // Access row data and manipulate
    }
});
```

## Form API Methods

### Core Form Methods

#### frm.set_value
Set field values with automatic change event triggering.

```javascript
// Set single value
frm.set_value('field_name', 'value');

// Set multiple values
frm.set_value({
    field1: 'value1',
    field2: 'value2'
});

// Returns a promise
frm.set_value('field_name', 'value').then(() => {
    // Do something after value is set
});
```

#### frm.refresh_field
Refresh a specific field and its dependencies.

```javascript
frm.refresh_field('field_name');
```

#### frm.save
Trigger form save with different actions.

```javascript
frm.save();                    // Normal save
frm.save('Submit');           // Submit
frm.save('Cancel');           // Cancel
frm.save('Update');           // Update after submit
```

#### frm.add_custom_button
Add custom buttons to the form toolbar.

```javascript
// Simple button
frm.add_custom_button('Button Label', () => {
    // Button action
});

// Button in group
frm.add_custom_button('Action', () => {
    // Action
}, 'Group Name');
```

#### frm.set_query
Apply filters to Link fields.

```javascript
// Basic filter
frm.set_query('link_field', () => {
    return {
        filters: {
            status: 'Active'
        }
    };
});

// Child table field filter
frm.set_query('item_code', 'items', () => {
    return {
        filters: {
            item_group: 'Products'
        }
    };
});
```

#### frm.toggle_enable / frm.toggle_reqd / frm.toggle_display
Control field properties dynamically.

```javascript
// Enable/disable fields
frm.toggle_enable(['field1', 'field2'], condition);

// Make fields mandatory
frm.toggle_reqd('field_name', condition);

// Show/hide fields
frm.toggle_display(['field1', 'field2'], condition);
```

#### frm.set_df_property
Change field properties dynamically.

```javascript
// Make field read-only
frm.set_df_property('field_name', 'read_only', 1);

// Change field options
frm.set_df_property('status', 'options', ['Open', 'Closed']);

// Make field mandatory
frm.set_df_property('field_name', 'reqd', 1);
```

#### frm.add_child
Add rows to child tables.

```javascript
let row = frm.add_child('table_field', {
    field1: 'value1',
    field2: 'value2'
});
frm.refresh_field('table_field');
```

#### frm.call
Call server-side methods.

```javascript
frm.call('method_name', {
    arg1: 'value1',
    arg2: 'value2'
}).then(r => {
    if (r.message) {
        // Handle response
    }
});
```

## Real-World Examples

### Example 1: ERPNext Sales Invoice - Healthcare Integration

```javascript
// From healthcare/healthcare/public/js/sales_invoice.js
frappe.ui.form.on('Sales Invoice', {
    refresh(frm) {
        if (frm.doc.docstatus === 0 && !frm.doc.is_return) {
            frm.add_custom_button(__('Healthcare Services'), function() {
                get_healthcare_services_to_invoice(frm);
            }, __('Get Items From'));
            
            frm.add_custom_button(__('Prescriptions'), function() {
                get_drugs_to_invoice(frm);
            }, __('Get Items From'));
        }
    },

    onload_post_render(frm) {
        if (frm.doc.items && frm.doc.items.length === 1 && !frm.doc.items[0].item_code) {
            frm.clear_table('items');
            frm.refresh_field('items');
        }
    }
});
```

### Example 2: HRMS Training Event - Multiple Add for Child Table

```javascript
// From hrms/hrms/hr/doctype/training_event/training_event.js
frappe.ui.form.on("Training Event", {
    onload_post_render: function (frm) {
        // Enable multiple selection for employees
        frm.get_field("employees").grid.set_multiple_add("employee");
    },
    
    refresh: function (frm) {
        if (!frm.doc.__islocal) {
            frm.add_custom_button(__("Training Result"), function () {
                frappe.route_options = {
                    training_event: frm.doc.name,
                };
                frappe.set_route("List", "Training Result");
            });
            
            frm.add_custom_button(__("Training Feedback"), function () {
                frappe.route_options = {
                    training_event: frm.doc.name,
                };
                frappe.set_route("List", "Training Feedback");
            });
        }
        frm.events.set_employee_query(frm);
    }
});
```

### Example 3: Healthcare Vital Signs - Field Calculations

```javascript
// From healthcare/healthcare/healthcare/doctype/vital_signs/vital_signs.js
frappe.ui.form.on('Vital Signs', {
    height: function(frm) {
        if (frm.doc.height && frm.doc.weight) {
            calculate_bmi(frm);
        }
    },

    weight: function(frm) {
        if (frm.doc.height && frm.doc.weight) {
            calculate_bmi(frm);
        }
    },

    bp_systolic: function(frm) {
        if (frm.doc.bp_systolic && frm.doc.bp_diastolic) {
            set_bp(frm);
        }
    },

    bp_diastolic: function(frm) {
        if (frm.doc.bp_systolic && frm.doc.bp_diastolic) {
            set_bp(frm);
        }
    }
});

function calculate_bmi(frm) {
    let height_in_meters = frm.doc.height / 100;
    let bmi = frm.doc.weight / (height_in_meters * height_in_meters);
    frm.set_value('bmi', bmi.toFixed(2));
}

function set_bp(frm) {
    let bp = frm.doc.bp_systolic + '/' + frm.doc.bp_diastolic;
    frm.set_value('bp', bp);
}
```

### Example 4: Child Table Events - Daily Checklist

```javascript
// From propms/propms/property_management_solution/doctype/daily_checklist/daily_checklist.js
frappe.ui.form.on('Daily Checklist', 'area', function(frm, cdt, cdn) {
    var doc = locals[cdt][cdn];
    if (doc.area) {
        frappe.call({
            method: "frappe.client.get",
            args: {
                name: doc.area,
                doctype: "Checklist Checkup Area"
            },
            callback(r) {
                if (r.message) {
                    for (var row in r.message.task) {
                        var child = frm.add_child("daily_checklist_detail");
                        frappe.model.set_value(child.doctype, child.name,
                            "checklist_task", r.message.task[row].task_name);
                        refresh_field("daily_checklist_detail");
                    }
                }
            }
        });
    }
});
```

### Example 5: ERPNext Event Participants - Advanced Set Query

```javascript
// From erpnext/erpnext/public/js/event.js
frappe.ui.form.on("Event", {
    refresh: function (frm) {
        frm.set_query("reference_doctype", "event_participants", function () {
            return {
                filters: {
                    name: ["in", ["Contact", "Lead", "Customer", "Supplier", "Employee", "Sales Partner"]],
                },
            };
        });

        frm.add_custom_button(
            __("Add Leads"),
            function () {
                new frappe.desk.eventParticipants(frm, "Lead");
            },
            __("Add Participants")
        );

        frm.add_custom_button(
            __("Add Customers"),
            function () {
                new frappe.desk.eventParticipants(frm, "Customer");
            },
            __("Add Participants")
        );
    }
});
```

### Example 6: HRMS Company - Multiple Set Query Examples

```javascript
// From hrms/hrms/public/js/erpnext/company.js
frappe.ui.form.on("Company", {
    refresh: function (frm) {
        frm.set_query("default_expense_claim_payable_account", function () {
            return {
                filters: {
                    company: frm.doc.name,
                    is_group: 0,
                },
            };
        });

        frm.set_query("default_employee_advance_account", function () {
            return {
                filters: {
                    company: frm.doc.name,
                    is_group: 0,
                    root_type: "Asset",
                    account_type: "Receivable",
                },
            };
        });

        frm.set_query("default_payroll_payable_account", function () {
            return {
                filters: {
                    company: frm.doc.name,
                    is_group: 0,
                    root_type: "Liability",
                },
            };
        });

        frm.set_query("hra_component", function () {
            return {
                filters: { type: "Earning" },
            };
        });
    },
});
```

### Example 7: LMS Timetable Template - Advanced Child Table Queries

```javascript
// From lms/lms/lms/doctype/lms_timetable_template/lms_timetable_template.js
frappe.ui.form.on("LMS Timetable Template", {
    refresh(frm) {
        frm.set_query("reference_doctype", "timetable", function () {
            let doctypes = ["Course Lesson", "LMS Quiz", "LMS Assignment"];
            return {
                filters: {
                    name: ["in", doctypes],
                },
            };
        });

        frm.set_query("reference_doctype", "timetable_legends", function () {
            let doctypes = [
                "Course Lesson",
                "LMS Quiz",
                "LMS Assignment",
                "LMS Live Class",
            ];
            return {
                filters: {
                    name: ["in", doctypes],
                },
            };
        });
    },
});
```

## Client Script in Frappe Desk

### Creating Client Scripts through Frappe Desk

1. **Navigate to Client Script**:
   - Go to Home > Customization > Client Script > New

2. **Configure the Script**:
   - **DocType**: Select the target DocType
   - **Script**: Write your JavaScript code
   - **Enabled**: Check to activate

### Client Script Examples for Frappe Desk

#### Example 1: Basic Validation

```javascript
frappe.ui.form.on('Task', {
    validate: function(frm) {
        if (frm.doc.from_date < frappe.datetime.get_today()) {
            frappe.msgprint('You cannot select past date in From Date');
            frappe.validated = false;
        }
    }
});
```

#### Example 2: Field Auto-Population

```javascript
frappe.ui.form.on('Sales Invoice', {
    customer: function(frm) {
        if (frm.doc.customer) {
            frappe.call({
                method: 'frappe.client.get_value',
                args: {
                    doctype: 'Customer',
                    fieldname: ['territory', 'customer_group'],
                    filters: {name: frm.doc.customer}
                },
                callback: function(r) {
                    if (r.message) {
                        frm.set_value('territory', r.message.territory);
                        frm.set_value('customer_group', r.message.customer_group);
                    }
                }
            });
        }
    }
});
```

#### Example 3: Dynamic Field Properties

```javascript
frappe.ui.form.on('Purchase Order', {
    refresh: function(frm) {
        // Make supplier mandatory only for certain item groups
        frm.toggle_reqd('supplier', frm.doc.total > 10000);

        // Hide certain fields for specific users
        let hide_fields = !frappe.user_roles.includes('Purchase Manager');
        frm.toggle_display(['discount_amount'], !hide_fields);
    },

    total: function(frm) {
        // Recalculate requirements when total changes
        frm.trigger('refresh');
    }
});
```

#### Example 4: Child Table Manipulation

```javascript
frappe.ui.form.on('Sales Order', {
    refresh: function(frm) {
        // Add custom button to calculate totals
        frm.add_custom_button('Recalculate', function() {
            let total = 0;
            frm.doc.items.forEach(function(item) {
                total += item.amount || 0;
            });
            frm.set_value('total', total);
        });
    }
});

frappe.ui.form.on('Sales Order Item', {
    qty: function(frm, cdt, cdn) {
        let row = locals[cdt][cdn];
        if (row.qty && row.rate) {
            frappe.model.set_value(cdt, cdn, 'amount', row.qty * row.rate);
        }
    },

    rate: function(frm, cdt, cdn) {
        let row = locals[cdt][cdn];
        if (row.qty && row.rate) {
            frappe.model.set_value(cdt, cdn, 'amount', row.qty * row.rate);
        }
    },

    items_add: function(frm, cdt, cdn) {
        // Set default values for new rows
        frappe.model.set_value(cdt, cdn, 'qty', 1);
    }
});
```

#### Example 5: Advanced Server Calls

```javascript
frappe.ui.form.on('Project', {
    refresh: function(frm) {
        if (!frm.doc.__islocal) {
            frm.add_custom_button('Get Tasks', function() {
                frappe.call({
                    method: 'erpnext.projects.doctype.project.project.get_project_tasks',
                    args: {
                        project: frm.doc.name
                    },
                    callback: function(r) {
                        if (r.message) {
                            // Clear existing tasks
                            frm.clear_table('tasks');

                            // Add tasks from server
                            r.message.forEach(function(task) {
                                let row = frm.add_child('tasks');
                                row.task = task.name;
                                row.subject = task.subject;
                                row.status = task.status;
                            });

                            frm.refresh_field('tasks');
                        }
                    }
                });
            });
        }
    }
});
```

## Best Practices

### 1. Performance Optimization

```javascript
// Good: Use specific field events
frappe.ui.form.on('DocType', {
    field_name: function(frm) {
        // Only runs when field_name changes
    }
});

// Avoid: Using refresh for field-specific logic
frappe.ui.form.on('DocType', {
    refresh: function(frm) {
        // Runs every time form refreshes
        if (frm.doc.field_name) {
            // This is inefficient
        }
    }
});
```

### 2. Error Handling

```javascript
frappe.ui.form.on('DocType', {
    field_name: function(frm) {
        try {
            // Your logic here
            if (!frm.doc.required_field) {
                frappe.throw('Required field is missing');
            }
        } catch (error) {
            frappe.msgprint({
                title: 'Error',
                message: error.message,
                indicator: 'red'
            });
        }
    }
});
```

### 3. Conditional Logic

```javascript
frappe.ui.form.on('DocType', {
    refresh: function(frm) {
        // Use early returns for cleaner code
        if (frm.doc.__islocal) return;
        if (!frappe.user_roles.includes('Manager')) return;

        // Add manager-specific buttons
        frm.add_custom_button('Manager Action', function() {
            // Manager action
        });
    }
});
```

### 4. Child Table Best Practices

```javascript
frappe.ui.form.on('Parent DocType', {
    refresh: function(frm) {
        // Set child table properties
        frm.get_field('child_table').grid.editable_fields = [
            {fieldname: 'field1', columns: 2},
            {fieldname: 'field2', columns: 3}
        ];
    }
});

frappe.ui.form.on('Child DocType', {
    field_name: function(frm, cdt, cdn) {
        let row = locals[cdt][cdn];

        // Validate child table data
        if (row.field_name < 0) {
            frappe.model.set_value(cdt, cdn, 'field_name', 0);
            frappe.show_alert('Value cannot be negative');
        }
    }
});
```

### 5. Memory Management

```javascript
frappe.ui.form.on('DocType', {
    onload: function(frm) {
        // Store references that will be reused
        frm.custom_data = {
            cached_values: {},
            intervals: []
        };
    },

    before_save: function(frm) {
        // Clear intervals before saving
        if (frm.custom_data && frm.custom_data.intervals) {
            frm.custom_data.intervals.forEach(clearInterval);
            frm.custom_data.intervals = [];
        }
    }
});
```

## Document Events vs Form Events

### Document Events (Server-side)
Defined in `hooks.py`:

```python
doc_events = {
    "Sales Invoice": {
        "validate": "myapp.api.sales_invoice.validate",
        "on_submit": "myapp.api.sales_invoice.on_submit",
        "before_save": "myapp.api.sales_invoice.before_save"
    }
}
```

### Form Events (Client-side)
Defined in JavaScript files:

```javascript
frappe.ui.form.on('Sales Invoice', {
    validate: function(frm) {
        // Client-side validation
    },

    refresh: function(frm) {
        // UI customizations
    }
});
```

## Debugging Form Scripts

### 1. Console Logging

```javascript
frappe.ui.form.on('DocType', {
    field_name: function(frm) {
        console.log('Field changed:', frm.doc.field_name);
        console.log('Full doc:', frm.doc);
    }
});
```

### 2. Using Frappe's Built-in Debugging

```javascript
frappe.ui.form.on('DocType', {
    refresh: function(frm) {
        // Enable debug mode
        if (frappe.boot.developer_mode) {
            console.log('Form loaded:', frm);
        }
    }
});
```

### 3. Error Tracking

```javascript
frappe.ui.form.on('DocType', {
    validate: function(frm) {
        try {
            // Your validation logic
        } catch (error) {
            console.error('Validation error:', error);
            frappe.msgprint('An error occurred during validation');
            frappe.validated = false;
        }
    }
});
```

This comprehensive guide covers all aspects of Frappe Form Scripts, providing you with the knowledge and examples needed to implement powerful form customizations in your Frappe applications. The examples are drawn from real-world implementations in Frappe, ERPNext, Healthcare, HRMS, and other applications, ensuring practical applicability.
```
