2025-11-07 11:06:07,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, <PERSON>ODIF<PERSON> `paid_by` decimal(21,9) not null default 0
2025-11-07 11:07:15,145 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parent varchar(140)
2025-11-07 11:07:15,146 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:07:15,147 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:07:15,231 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` ADD COLUMN `journal_entry` varchar(140), ADD COLUMN `disbursed_date` date
2025-11-07 11:07:15,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:07:30,432 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parent varchar(140)
2025-11-07 11:07:30,433 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:07:30,434 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:07:30,519 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:07:47,677 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parent varchar(140)
2025-11-07 11:07:47,677 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:07:47,678 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:07:47,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:08:24,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-07 11:23:51,707 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parent varchar(140)
2025-11-07 11:23:51,708 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:23:51,709 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:23:51,790 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` ADD COLUMN `journal_entry` varchar(140), ADD COLUMN `disbursed_date` date
2025-11-07 11:23:51,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:24:31,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 11:25:35,191 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parent varchar(140)
2025-11-07 11:25:35,192 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:25:35,193 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:25:35,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges` ADD COLUMN `journal_entry` varchar(140), ADD COLUMN `disbursed_date` date
2025-11-07 11:25:35,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:26:26,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 11:27:48,151 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parent varchar(140)
2025-11-07 11:27:48,152 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:27:48,153 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:27:48,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` ADD COLUMN `journal_entry` varchar(140), ADD COLUMN `disbursed_date` date
2025-11-07 11:27:48,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:28:05,219 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parent varchar(140)
2025-11-07 11:28:05,220 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:28:05,221 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:28:05,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:28:21,460 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parent varchar(140)
2025-11-07 11:28:21,461 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:28:21,461 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:28:21,519 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:28:57,323 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parent varchar(140)
2025-11-07 11:28:57,324 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:28:57,325 WARNING database DDL Query made to DB:
alter table `tabVerification Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:28:57,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:29:17,051 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parent varchar(140)
2025-11-07 11:29:17,052 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parenttype varchar(140)
2025-11-07 11:29:17,053 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parentfield varchar(140)
2025-11-07 11:29:17,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:49:18,788 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-07 11:49:20,069 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-07 11:49:21,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-07 11:49:22,564 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-07 11:49:22,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 11:49:22,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-11-07 11:49:23,191 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:49:23,335 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:49:23,451 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:49:23,631 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 11:49:23,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges` MODIFY `amount` decimal(21,9) not null default 0
2025-11-07 11:49:24,087 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-07 11:49:24,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-07 11:49:25,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-07 11:49:25,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-07 11:49:30,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-11-07 12:05:57,186 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-07 12:05:58,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-07 12:05:59,422 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-07 12:06:00,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 12:06:00,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-11-07 12:06:00,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-07 12:06:01,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-07 12:06:01,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-07 12:06:01,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-07 12:06:03,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-07 12:06:03,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-07 12:06:07,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-11-07 12:16:26,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-11-07 12:16:56,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 12:17:26,951 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-07 12:17:52,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-08 17:26:48,702 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-08 17:26:49,701 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-08 17:26:50,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-08 17:26:51,428 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Booking` MODIFY `total_amount` decimal(21,9) not null default 0
2025-11-08 17:56:36,720 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-08 17:56:37,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-08 17:56:38,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-08 18:09:13,324 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-08 18:09:14,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-08 18:09:15,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-09 09:27:51,459 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-09 09:27:52,124 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustom Field` MODIFY `link_filters` json, MODIFY `label` varchar(255)
2025-11-09 09:27:53,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form List Column` ADD COLUMN `options` text
2025-11-09 09:27:53,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-09 09:27:54,166 WARNING database DDL Query made to DB:
create table `tabProcess Period Closing Voucher` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parent_pcv` varchar(140),
`status` varchar(140) default 'Queued',
`p_l_closing_balance` json,
`bs_closing_balance` json,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-09 09:27:56,245 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,6) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,6) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `cost_center` varchar(140), MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-11-09 09:27:56,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD INDEX `rejected_serial_and_batch_bundle_index`(`rejected_serial_and_batch_bundle`)
2025-11-09 09:27:56,415 WARNING database DDL Query made to DB:
create table `tabProcess Period Closing Voucher Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`processing_date` date,
`report_type` varchar(140) default 'Profit and Loss',
`status` varchar(140) default 'Queued',
`closing_balance` json,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-09 09:27:58,445 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` ADD COLUMN `party_not_required` int(1) not null default 0
2025-11-09 09:27:58,467 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` MODIFY `difference` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_credit` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0
2025-11-09 09:27:58,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Account` MODIFY `bank_account_no` varchar(64), MODIFY `iban` varchar(64)
2025-11-09 09:27:59,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `transaction_type` varchar(64), MODIFY `withdrawal` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `deposit` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-11-09 09:27:59,083 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` DROP INDEX `transaction_id`
2025-11-09 09:27:59,297 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Guarantee` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `margin_money` decimal(21,9) not null default 0, MODIFY `charges` decimal(21,9) not null default 0
2025-11-09 09:27:59,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` MODIFY `credit` decimal(21,9) not null default 0, MODIFY `debit_in_account_currency` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `credit_in_account_currency` decimal(21,9) not null default 0, MODIFY `debit` decimal(21,9) not null default 0
2025-11-09 09:28:00,542 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` ADD INDEX `advance_voucher_type_index`(`advance_voucher_type`), ADD INDEX `advance_voucher_no_index`(`advance_voucher_no`)
2025-11-09 09:28:00,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` MODIFY `amount` decimal(21,9) not null default 0
2025-11-09 09:28:00,773 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry`
				ADD INDEX IF NOT EXISTS `against_voucher_type_against_voucher_no_index`(against_voucher_type, against_voucher_no)
2025-11-09 09:28:00,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry`
				ADD INDEX IF NOT EXISTS `voucher_type_voucher_no_index`(voucher_type, voucher_no)
2025-11-09 09:28:01,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-11-09 09:28:01,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `total_billing_amount` decimal(21,9) not null default 0, MODIFY `task_weight` decimal(21,9) not null default 0, MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0, MODIFY `progress` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0
2025-11-09 09:28:02,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-09 09:28:02,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `worker_subsistence` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `other_allowance` decimal(21,9) not null default 0
2025-11-09 09:28:02,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0
2025-11-09 09:28:03,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0
2025-11-09 09:28:03,184 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD INDEX `against_stock_entry_index`(`against_stock_entry`), ADD INDEX `ste_detail_index`(`ste_detail`)
2025-11-09 09:28:04,057 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `received_stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `amount_difference_with_purchase_invoice` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,6) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-11-09 09:28:04,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` ADD INDEX `material_request_item_index`(`material_request_item`), ADD INDEX `rejected_serial_and_batch_bundle_index`(`rejected_serial_and_batch_bundle`)
2025-11-09 09:28:04,821 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0
2025-11-09 09:28:05,386 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-09 09:28:05,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-09 09:28:06,131 WARNING database DDL Query made to DB:
create table `tabForeign Import Payment Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_entry` varchar(140),
`payment_date` date,
`payment_amount_foreign` decimal(21,9) not null default 0,
`payment_amount_base` decimal(21,9) not null default 0,
`payment_exchange_rate` decimal(21,9) not null default 0,
`exchange_difference` decimal(21,9) not null default 0,
`journal_entry_created` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-09 09:28:06,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-11-09 09:28:06,765 WARNING database DDL Query made to DB:
create table `tabTrip Sheet Item Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_document_id` varchar(140),
`item_name` varchar(140),
`qty` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-09 09:28:06,877 WARNING database DDL Query made to DB:
create table `tabForeign Import Exchange Difference Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`difference_type` varchar(140),
`amount` decimal(21,2) not null default 0,
`journal_entry` varchar(140),
`posting_date` date,
`remarks` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-09 09:28:07,000 WARNING database DDL Query made to DB:
create table `tabTrip Sheet References` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_document` varchar(140),
`part_name` varchar(140),
`note` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-09 09:28:07,249 WARNING database DDL Query made to DB:
create table `tabForeign Import LCV Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`landed_cost_voucher` varchar(140),
`lcv_date` date,
`lcv_amount_foreign` decimal(21,2) not null default 0,
`lcv_amount_base` decimal(21,9) not null default 0,
`exchange_rate_used` decimal(21,9) not null default 1.0,
`allocated_to_items` decimal(21,9) not null default 0,
`exchange_difference` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-09 09:28:07,388 WARNING database DDL Query made to DB:
create table `tabTrip Sheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`amended_from` varchar(140),
`vehicle` varchar(140),
`transporter_type` varchar(140),
`driver` varchar(140),
`driver_name` varchar(140),
`external_driver_name` varchar(140),
`start_km` decimal(21,9) not null default 0,
`end_km` decimal(21,9) not null default 0,
`fuel_consumed` decimal(21,9) not null default 0,
`status` varchar(140),
`start_location` varchar(140),
`end_location` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-09 09:28:07,645 WARNING database DDL Query made to DB:
create table `tabForeign Import Transaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`purchase_invoice` varchar(140),
`supplier` varchar(140),
`transaction_date` date,
`currency` varchar(140),
`original_exchange_rate` decimal(21,9) not null default 0,
`status` varchar(140) default 'Draft',
`invoice_amount_foreign` decimal(21,9) not null default 0,
`invoice_amount_base` decimal(21,9) not null default 0,
`total_gain_loss` decimal(21,9) not null default 0,
`net_difference` decimal(21,9) not null default 0,
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-09 09:28:07,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-11-09 09:28:08,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-11-09 09:28:08,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `gross_weight_index`, DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`
2025-11-09 09:28:08,992 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-09 09:28:09,194 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-09 09:28:09,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabFeedback Form` ADD COLUMN `dynamic_html` varchar(140)
2025-11-09 09:28:09,818 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-11-09 09:28:09,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-11-09 09:28:10,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `gross_volume` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-11-09 09:28:10,418 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` ADD INDEX `container_no_index`(`container_no`), ADD INDEX `gross_weight_index`(`gross_weight`), ADD INDEX `net_weight_index`(`net_weight`)
2025-11-09 09:28:16,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier` ADD COLUMN `track_import_exchanges` int(1) not null default 1, ADD COLUMN `preferred_exchange_account` varchar(140)
2025-11-09 09:28:17,917 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `foreign_import_tracker` varchar(140), ADD COLUMN `exchange_difference_amount` decimal(21,9) not null default 0
2025-11-09 09:28:17,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0
2025-11-09 09:28:18,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `foreign_import_tracker` varchar(140), ADD COLUMN `enable_import_tracking` int(1) not null default 1
2025-11-09 09:28:18,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-11-09 09:28:18,698 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-11-09 09:28:21,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` ADD COLUMN `foreign_import_tracker` varchar(140)
2025-11-09 09:28:21,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_credit` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0
2025-11-09 09:28:21,861 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `auto_create_import_tracker` int(1) not null default 1, ADD COLUMN `import_exchange_threshold` decimal(21,9) not null default 1.0
2025-11-09 09:28:21,916 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,2) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-11-09 09:28:22,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `is_deduction` int(1) not null default 0, ADD COLUMN `is_earning` int(1) not null default 0
2025-11-09 09:28:22,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `actual_encashable_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0
2025-11-09 09:28:22,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,6) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-11-10 11:20:23,974 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-10 11:20:25,315 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-10 11:20:26,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-10 11:20:28,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `road_fine_penalty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0, MODIFY `road_fine_amount` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-10 11:20:28,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-10 11:20:30,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `road_fine_penalty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0, MODIFY `road_fine_amount` decimal(21,9) not null default 0
2025-11-10 11:20:30,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-10 11:20:36,494 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,4) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,6) not null default 0, MODIFY `amount` decimal(21,3) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-11-10 12:16:12,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabBillet Production V2` MODIFY `aod` decimal(21,9) not null default 0, MODIFY `value_per_mt` decimal(21,9) not null default 0, MODIFY `furnace` decimal(21,9) not null default 0, MODIFY `gas_consuption` decimal(21,9) not null default 0, MODIFY `diesel_consumption` decimal(21,9) not null default 0, MODIFY `total_mt` decimal(21,9) not null default 0, MODIFY `price_per_unit` decimal(21,9) not null default 0, MODIFY `twp` decimal(21,9) not null default 0, MODIFY `scrap_billet` decimal(21,9) not null default 0, MODIFY `finished_goods_recovery` decimal(21,2) not null default 0, MODIFY `tpc` decimal(21,9) not null default 0, MODIFY `ccm` decimal(21,9) not null default 0, MODIFY `total_scrap_consumption` decimal(21,9) not null default 0, MODIFY `total_raw_material_consumption` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `flux` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `qty_of_random_billet` decimal(21,9) not null default 0, MODIFY `weight_of_billet_and_random_produced` decimal(21,9) not null default 0, MODIFY `labour_charges` decimal(21,9) not null default 0, MODIFY `total_amount_of_power_consumption` decimal(21,9) not null default 0, MODIFY `value_per_piece` decimal(21,9) not null default 0
2025-11-10 12:37:22,470 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-10 12:37:24,344 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-10 12:37:24,587 WARNING database DDL Query made to DB:
create table `tabProcess Period Closing Voucher` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parent_pcv` varchar(140),
`status` varchar(140) default 'Queued',
`p_l_closing_balance` json,
`bs_closing_balance` json,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-10 12:37:24,717 WARNING database DDL Query made to DB:
create table `tabProcess Period Closing Voucher Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`processing_date` date,
`report_type` varchar(140) default 'Profit and Loss',
`status` varchar(140) default 'Queued',
`closing_balance` json,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-10 12:37:25,112 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Account` MODIFY `iban` varchar(64), MODIFY `bank_account_no` varchar(64)
2025-11-10 12:37:25,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` MODIFY `deposit` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `withdrawal` decimal(21,9) not null default 0
2025-11-10 12:37:25,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction` DROP INDEX `transaction_id`
2025-11-10 12:37:25,642 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Guarantee` MODIFY `charges` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `margin_money` decimal(21,9) not null default 0
2025-11-10 12:37:25,984 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` MODIFY `debit` decimal(21,9) not null default 0, MODIFY `cost_center` varchar(140), MODIFY `credit_in_account_currency` decimal(21,9) not null default 0, MODIFY `debit_in_account_currency` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `credit` decimal(21,9) not null default 0
2025-11-10 12:37:26,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` ADD INDEX `advance_voucher_type_index`(`advance_voucher_type`), ADD INDEX `advance_voucher_no_index`(`advance_voucher_no`)
2025-11-10 12:37:26,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-11-10 12:37:27,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask` MODIFY `total_costing_amount` decimal(21,9) not null default 0, MODIFY `task_weight` decimal(21,9) not null default 0, MODIFY `total_billing_amount` decimal(21,9) not null default 0, MODIFY `total_expense_claim` decimal(21,9) not null default 0, MODIFY `progress` decimal(21,9) not null default 0, MODIFY `actual_time` decimal(21,9) not null default 0
2025-11-10 12:37:27,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-10 12:37:27,824 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-11-10 12:37:28,177 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0
2025-11-10 12:37:29,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0
2025-11-10 12:37:29,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-10 12:37:30,101 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-10 12:37:30,425 WARNING database DDL Query made to DB:
create table `tabForeign Import Payment Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_entry` varchar(140),
`payment_date` date,
`payment_amount_foreign` decimal(21,9) not null default 0,
`payment_amount_base` decimal(21,9) not null default 0,
`payment_exchange_rate` decimal(21,9) not null default 0,
`exchange_difference` decimal(21,9) not null default 0,
`journal_entry_created` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-10 12:37:30,901 WARNING database DDL Query made to DB:
create table `tabTrip Sheet Item Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_document_id` varchar(140),
`item_name` varchar(140),
`qty` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-10 12:37:31,012 WARNING database DDL Query made to DB:
create table `tabForeign Import Exchange Difference Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`difference_type` varchar(140),
`amount` decimal(21,2) not null default 0,
`journal_entry` varchar(140),
`posting_date` date,
`remarks` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-10 12:37:31,123 WARNING database DDL Query made to DB:
create table `tabTrip Sheet References` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_document` varchar(140),
`part_name` varchar(140),
`note` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-10 12:37:31,279 WARNING database DDL Query made to DB:
create table `tabForeign Import LCV Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`landed_cost_voucher` varchar(140),
`lcv_date` date,
`lcv_amount_foreign` decimal(21,2) not null default 0,
`lcv_amount_base` decimal(21,9) not null default 0,
`exchange_rate_used` decimal(21,9) not null default 1.0,
`allocated_to_items` decimal(21,9) not null default 0,
`exchange_difference` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-10 12:37:31,426 WARNING database DDL Query made to DB:
create table `tabTrip Sheet` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`amended_from` varchar(140),
`vehicle` varchar(140),
`transporter_type` varchar(140),
`driver` varchar(140),
`driver_name` varchar(140),
`external_driver_name` varchar(140),
`start_km` decimal(21,9) not null default 0,
`end_km` decimal(21,9) not null default 0,
`fuel_consumed` decimal(21,9) not null default 0,
`status` varchar(140),
`start_location` varchar(140),
`end_location` varchar(140),
`start_time` datetime(6),
`end_time` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-10 12:37:31,664 WARNING database DDL Query made to DB:
create table `tabForeign Import Transaction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`purchase_invoice` varchar(140),
`supplier` varchar(140),
`transaction_date` date,
`currency` varchar(140),
`original_exchange_rate` decimal(21,9) not null default 0,
`status` varchar(140) default 'Draft',
`invoice_amount_foreign` decimal(21,9) not null default 0,
`invoice_amount_base` decimal(21,9) not null default 0,
`total_gain_loss` decimal(21,9) not null default 0,
`net_difference` decimal(21,9) not null default 0,
`company` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-10 12:37:32,673 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-10 12:37:32,893 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-10 12:37:33,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabFeedback Form` ADD COLUMN `dynamic_html` varchar(140)
2025-11-10 12:37:37,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier` ADD COLUMN `track_import_exchanges` int(1) not null default 1, ADD COLUMN `preferred_exchange_account` varchar(140)
2025-11-10 12:37:37,989 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `foreign_import_tracker` varchar(140), ADD COLUMN `exchange_difference_amount` decimal(21,9) not null default 0
2025-11-10 12:37:38,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0
2025-11-10 12:37:38,065 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-11-10 12:37:38,160 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `foreign_import_tracker` varchar(140), ADD COLUMN `enable_import_tracking` int(1) not null default 1
2025-11-10 12:37:38,183 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-11-10 12:37:38,271 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `auto_create_import_tracker` int(1) not null default 1, ADD COLUMN `import_exchange_threshold` decimal(21,9) not null default 1.0
2025-11-10 12:37:38,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,2) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-11-10 12:37:38,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` ADD COLUMN `foreign_import_tracker` varchar(140)
2025-11-10 12:37:38,422 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` MODIFY `total_credit` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0
2025-11-10 12:37:38,702 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `transfer_goods_between_company` varchar(140)
2025-11-10 12:37:38,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-11-10 12:37:38,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `include_in_payroll_summary` int(1) not null default 0
2025-11-10 12:37:38,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-11-10 12:37:39,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-11-10 16:46:33,193 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-10 16:46:34,375 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-10 16:46:35,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-10 16:46:36,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-10 16:46:37,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-10 16:46:38,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabForeign Import Transaction` MODIFY `invoice_amount_foreign` decimal(21,9) not null default 0, MODIFY `invoice_amount_base` decimal(21,9) not null default 0, MODIFY `net_difference` decimal(21,9) not null default 0, MODIFY `original_exchange_rate` decimal(21,9) not null default 0, MODIFY `total_gain_loss` decimal(21,9) not null default 0
2025-11-10 16:46:40,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-10 16:46:40,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-10 16:46:45,114 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-11-11 10:02:35,195 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-11 10:02:36,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-11 10:02:37,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-11 10:02:39,325 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `road_fine_penalty` decimal(21,9) not null default 0, MODIFY `road_fine_amount` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-11 10:02:39,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-11 10:02:41,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `road_fine_amount` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0, MODIFY `road_fine_penalty` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-11-11 10:02:41,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-11 10:02:42,355 WARNING database DDL Query made to DB:
ALTER TABLE `tabFacebook Lead Form Question` MODIFY `mapped_to_crm_field` varchar(140)
2025-11-11 10:02:42,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabFacebook Lead Form Question` ADD INDEX `creation`(`creation`)
2025-11-11 10:02:42,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabFacebook Lead Form` ADD INDEX `creation`(`creation`)
2025-11-11 10:02:42,720 WARNING database DDL Query made to DB:
ALTER TABLE `tabFailed Lead Sync Log` ADD INDEX `creation`(`creation`)
2025-11-11 10:02:42,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabFacebook Page` ADD INDEX `creation`(`creation`)
2025-11-11 10:02:43,076 WARNING database DDL Query made to DB:
ALTER TABLE `tabLead Sync Source` ADD INDEX `creation`(`creation`)
2025-11-11 10:02:47,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `item_tax_template` varchar(140), MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,4) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,6) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,3) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-11-11 10:07:25,656 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-11-11 10:07:26,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-11-11 10:07:28,271 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-11-11 10:07:29,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-11-11 10:07:30,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-11-11 10:07:30,214 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-11-11 10:07:30,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-11-11 10:07:31,055 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-11 10:07:31,351 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-11-11 10:07:32,821 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-11-11 10:07:33,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-11-11 10:07:38,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-11-11 11:09:58,071 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `workflow_state` varchar(140)
2025-11-11 11:09:58,093 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-11-11 13:10:42,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `workflow_state` varchar(140)
2025-11-11 13:10:42,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-11-11 15:08:32,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-11-11 15:08:57,287 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-11-11 15:12:27,721 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `paid_by` decimal(21,9) not null default 0
2025-11-11 17:25:09,425 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_750153d80863bcad'@'localhost'
2025-11-11 17:25:09,427 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_750153d80863bcad`
2025-11-11 17:25:09,432 WARNING database DDL Query made to DB:
CREATE USER '_750153d80863bcad'@'localhost' IDENTIFIED BY 'snkfomfxipRxbi22'
2025-11-11 17:25:09,433 WARNING database DDL Query made to DB:
CREATE DATABASE `_750153d80863bcad` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-11-11 17:25:09,885 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-11-11 17:25:09,898 WARNING database DDL Query made to DB:
create table __global_search(
				doctype varchar(100),
				name varchar(140),
				title varchar(140),
				content text,
				fulltext(content),
				route varchar(140),
				published int(1) not null default 0,
				unique `doctype_name` (doctype, name))
				COLLATE=utf8mb4_unicode_ci
				ENGINE=MyISAM
				CHARACTER SET=utf8mb4
2025-11-11 17:25:09,913 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-11-11 17:25:11,433 WARNING database DDL Query made to DB:
create table `tabDocType State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`color` varchar(140) default 'Blue',
`custom` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-11 17:25:12,266 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` ADD COLUMN `non_negative` int(1) not null default 0, ADD COLUMN `is_virtual` int(1) not null default 0, ADD COLUMN `sort_options` int(1) not null default 0, ADD COLUMN `link_filters` json, ADD COLUMN `fetch_from` text, ADD COLUMN `show_on_timeline` int(1) not null default 0, ADD COLUMN `make_attachment_public` int(1) not null default 0, ADD COLUMN `documentation_url` varchar(140), ADD COLUMN `placeholder` varchar(140)
2025-11-11 17:25:12,332 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` MODIFY `label` varchar(140), MODIFY `depends_on` longtext, MODIFY `fieldtype` varchar(140) default 'Data', MODIFY `collapsible_depends_on` longtext, MODIFY `print_width` varchar(64), MODIFY `max_height` varchar(64), MODIFY `oldfieldtype` varchar(140), MODIFY `read_only_depends_on` longtext, MODIFY `precision` varchar(140), MODIFY `fieldname` varchar(140), MODIFY `mandatory_depends_on` longtext, MODIFY `width` varchar(64), MODIFY `oldfieldname` varchar(140)
2025-11-11 17:25:12,424 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` ADD COLUMN `if_owner` int(1) not null default 0, ADD COLUMN `select` int(1) not null default 0
2025-11-11 17:25:12,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` MODIFY `role` varchar(140)
2025-11-11 17:25:12,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Action` ADD COLUMN `hidden` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-11-11 17:25:12,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Link` ADD COLUMN `parent_doctype` varchar(140), ADD COLUMN `table_fieldname` varchar(140), ADD COLUMN `hidden` int(1) not null default 0, ADD COLUMN `is_child_table` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-11-11 17:25:12,655 WARNING database DDL Query made to DB:
create table `tabForm Tour Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ui_tour` int(1) not null default 0,
`is_table_field` int(1) not null default 0,
`title` varchar(140),
`parent_fieldname` varchar(140),
`fieldname` varchar(140),
`element_selector` varchar(140),
`parent_element_selector` varchar(140),
`description` longtext,
`ondemand_description` longtext,
`position` varchar(140) default 'Bottom',
`hide_buttons` int(1) not null default 0,
`popover_element` int(1) not null default 0,
`modal_trigger` int(1) not null default 0,
`offset_x` int(11) not null default 0,
`offset_y` int(11) not null default 0,
`next_on_click` int(1) not null default 0,
`label` varchar(140),
`fieldtype` varchar(140) default '0',
`has_next_condition` int(1) not null default 0,
`next_step_condition` longtext,
`next_form_tour` varchar(140),
`child_doctype` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-11 17:25:12,748 WARNING database DDL Query made to DB:
create table `tabForm Tour` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`view_name` varchar(140),
`workspace_name` varchar(140),
`list_name` varchar(140) default 'List',
`report_name` varchar(140),
`dashboard_name` varchar(140),
`new_document_form` int(1) not null default 0,
`page_name` varchar(140),
`reference_doctype` varchar(140),
`module` varchar(140),
`ui_tour` int(1) not null default 0,
`track_steps` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`save_on_complete` int(1) not null default 0,
`first_document` int(1) not null default 0,
`include_name_field` int(1) not null default 0,
`page_route` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-11 17:25:12,886 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `is_calendar_and_gantt` int(1) not null default 0, ADD COLUMN `quick_entry` int(1) not null default 0, ADD COLUMN `grid_page_length` int(11) not null default 50, ADD COLUMN `rows_threshold_for_grid_search` int(11) not null default 20, ADD COLUMN `track_views` int(1) not null default 0, ADD COLUMN `queue_in_background` int(1) not null default 0, ADD COLUMN `documentation` varchar(140), ADD COLUMN `nsm_parent_field` varchar(140), ADD COLUMN `allow_events_in_timeline` int(1) not null default 0, ADD COLUMN `allow_auto_repeat` int(1) not null default 0, ADD COLUMN `make_attachments_public` int(1) not null default 0, ADD COLUMN `default_view` varchar(140), ADD COLUMN `force_re_route_to_default_view` int(1) not null default 0, ADD COLUMN `show_preview_popup` int(1) not null default 0, ADD COLUMN `default_email_template` varchar(140), ADD COLUMN `sender_name_field` varchar(140), ADD COLUMN `recipient_account_field` varchar(140), ADD COLUMN `protect_attached_files` int(1) not null default 0, ADD COLUMN `index_web_pages_for_search` int(1) not null default 1, ADD COLUMN `row_format` varchar(140) default 'Dynamic', ADD COLUMN `_comments` text, ADD COLUMN `_assign` text, ADD COLUMN `_liked_by` text
2025-11-11 17:25:12,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` MODIFY `timeline_field` varchar(140), MODIFY `document_type` varchar(140), MODIFY `migration_hash` varchar(140), MODIFY `image_field` varchar(140), MODIFY `engine` varchar(140) default 'InnoDB', MODIFY `subject_field` varchar(140), MODIFY `icon` varchar(140), MODIFY `sender_field` varchar(140), MODIFY `route` varchar(140), MODIFY `restrict_to_domain` varchar(140), MODIFY `module` varchar(140), MODIFY `search_fields` varchar(140), MODIFY `is_published_field` varchar(140), MODIFY `default_print_format` varchar(140), MODIFY `autoname` varchar(140), MODIFY `website_search_field` varchar(140), MODIFY `_user_tags` text, MODIFY `title_field` varchar(140), MODIFY `sort_order` varchar(140) default 'DESC', MODIFY `allow_rename` int(1) not null default 1, MODIFY `naming_rule` varchar(64), MODIFY `sort_field` varchar(140) default 'modified', MODIFY `color` varchar(140)
2025-11-11 17:25:12,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD INDEX `module_index`(`module`)
2025-11-11 17:25:13,186 WARNING database DDL Query made to DB:
create table `tabRole` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role_name` varchar(140) unique,
`home_page` varchar(140),
`restrict_to_domain` varchar(140),
`disabled` int(1) not null default 0,
`is_custom` int(1) not null default 0,
`desk_access` int(1) not null default 1,
`two_factor_auth` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-11 17:25:13,266 WARNING database DDL Query made to DB:
create table `tabHas Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-11-11 17:25:13,380 WARNING database DDL Query made to DB:
create table `tabCustom Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`dt` varchar(140),
`module` varchar(140),
`label` varchar(255),
`placeholder` varchar(140),
`fieldname` varchar(140),
`insert_after` varchar(140),
`length` int(11) not null default 0,
`link_filters` json,
`fieldtype` varchar(140) default 'Data',
`precision` varchar(140),
`hide_seconds` int(1) not null default 0,
`hide_days` int(1) not null default 0,
`options` text,
`sort_options` int(1) not null default 0,
`fetch_from` text,
`fetch_if_empty` int(1) not null default 0,
`collapsible` int(1) not null default 0,
`collapsible_depends_on` longtext,
`default` text,
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`non_negative` int(1) not null default 0,
`reqd` int(1) not null default 0,
`unique` int(1) not null default 0,
`is_virtual` int(1) not null default 0,
`read_only` int(1) not null default 0,
`ignore_user_permissions` int(1) not null default 0,
`hidden` int(1) not null default 0,
`print_hide` int(1) not null default 0,
`print_hide_if_no_value` int(1) not null default 0,
`print_width` varchar(140),
`no_copy` int(1) not null default 0,
`allow_on_submit` int(1) not null default 0,
`in_list_view` int(1) not null default 0,
`in_standard_filter` int(1) not null default 0,
`in_global_search` int(1) not null default 0,
`in_preview` int(1) not null default 0,
`bold` int(1) not null default 0,
`report_hide` int(1) not null default 0,
`search_index` int(1) not null default 0,
`allow_in_quick_entry` int(1) not null default 0,
`ignore_xss_filter` int(1) not null default 0,
`translatable` int(1) not null default 0,
`hide_border` int(1) not null default 0,
`show_dashboard` int(1) not null default 0,
`description` text,
`permlevel` int(11) not null default 0,
`width` varchar(140),
`columns` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `dt`(`dt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
