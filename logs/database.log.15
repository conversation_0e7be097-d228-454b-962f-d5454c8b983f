2025-10-27 13:33:04,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_paid` decimal(21,9) not null default 0
2025-10-27 13:33:04,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:00:20,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 14:03:31,750 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 14:03:47,861 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 14:05:23,027 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `total_paid` decimal(21,9) not null default 0
2025-10-27 14:05:23,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:06:07,852 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 14:08:13,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `total_paid_by_consignee` decimal(21,9) not null default 0
2025-10-27 14:08:13,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:09:18,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `total_paid` decimal(21,9) not null default 0
2025-10-27 14:09:18,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0
2025-10-27 14:10:52,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:11:35,477 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 14:18:55,888 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` ADD COLUMN `total_charges_paid_by_consignee` decimal(21,9) not null default 0, ADD COLUMN `total_paid` decimal(21,9) not null default 0
2025-10-27 14:18:55,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:56:34,449 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 14:57:05,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 14:57:39,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 14:58:24,641 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges_paid_by_consignee` decimal(21,9) not null default 0
2025-10-27 15:03:03,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` ADD COLUMN `paid_by` decimal(21,9) not null default 0
2025-10-27 15:03:04,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 15:03:31,261 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `paid_by` decimal(21,9) not null default 0
2025-10-27 15:03:31,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 15:04:32,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `paid_by` decimal(21,9) not null default 0
2025-10-27 15:04:32,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
2025-10-27 15:05:06,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `paid_by` decimal(21,9) not null default 0
2025-10-27 15:05:06,961 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid` decimal(21,9) not null default 0
