2025-10-25 16:23:56,674 WARNING database DDL Query made to DB:
create table `tabPrint Heading` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`print_heading` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:57,046 WARNING database DDL Query made to DB:
create table `tabNetwork Printer Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`server_ip` varchar(140) default 'localhost',
`port` int(11) not null default 631,
`printer_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:57,220 WARNING database DDL Query made to DB:
create table `tabLetter Head` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`letter_head_name` varchar(140) unique,
`source` varchar(140),
`footer_source` varchar(140) default 'HTML',
`disabled` int(1) not null default 0,
`is_default` int(1) not null default 0,
`image` text,
`image_height` decimal(21,9) not null default 0,
`image_width` decimal(21,9) not null default 0,
`align` varchar(140) default 'Left',
`content` longtext,
`footer` longtext,
`footer_image` text,
`footer_image_height` decimal(21,9) not null default 0,
`footer_image_width` decimal(21,9) not null default 0,
`footer_align` varchar(140),
`header_script` longtext,
`footer_script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `is_default`(`is_default`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:57,338 WARNING database DDL Query made to DB:
create table `tabPrint Format Field Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`field` varchar(140),
`template_file` varchar(140),
`module` varchar(140),
`standard` int(1) not null default 0,
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:57,878 WARNING database DDL Query made to DB:
create table `tabAddress Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country` varchar(140) unique,
`is_default` int(1) not null default 0,
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:58,261 WARNING database DDL Query made to DB:
create table `tabAddress` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`address_title` varchar(140),
`address_type` varchar(140),
`address_line1` varchar(240),
`address_line2` varchar(240),
`city` varchar(140),
`county` varchar(140),
`state` varchar(140),
`country` varchar(140),
`pincode` varchar(140),
`email_id` varchar(140),
`phone` varchar(140),
`fax` varchar(140),
`is_primary_address` int(1) not null default 0,
`is_shipping_address` int(1) not null default 0,
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `city`(`city`),
index `country`(`country`),
index `pincode`(`pincode`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:58,481 WARNING database DDL Query made to DB:
create table `tabContact Phone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`phone` varchar(140),
`is_primary_phone` int(1) not null default 0,
`is_primary_mobile_no` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:58,575 WARNING database DDL Query made to DB:
create table `tabSalutation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salutation` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:58,680 WARNING database DDL Query made to DB:
create table `tabGender` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gender` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:58,834 WARNING database DDL Query made to DB:
create table `tabContact` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`email_id` varchar(140),
`user` varchar(140),
`address` varchar(140),
`sync_with_google_contacts` int(1) not null default 0,
`status` varchar(140) default 'Passive',
`salutation` varchar(140),
`designation` varchar(140),
`gender` varchar(140),
`phone` varchar(140),
`mobile_no` varchar(140),
`company_name` varchar(140),
`image` text,
`google_contacts` varchar(140),
`google_contacts_id` varchar(140),
`pulled_from_google_contacts` int(1) not null default 0,
`is_primary_contact` int(1) not null default 0,
`department` varchar(140),
`unsubscribed` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email_id`(`email_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:58,986 WARNING database DDL Query made to DB:
create table `tabContact Email` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email_id` varchar(140),
`is_primary` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:59,163 WARNING database DDL Query made to DB:
create table `tabEnergy Point Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`rule_name` varchar(140) unique,
`reference_doctype` varchar(140),
`for_doc_event` varchar(140) default 'Custom',
`field_to_check` varchar(140),
`points` int(11) not null default 0,
`for_assigned_users` int(1) not null default 0,
`user_field` varchar(140),
`multiplier_field` varchar(140),
`max_points` int(11) not null default 0,
`apply_only_once` int(1) not null default 0,
`condition` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:59,563 WARNING database DDL Query made to DB:
create table `tabEnergy Point Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`type` varchar(140),
`points` int(11) not null default 0,
`rule` varchar(140),
`reference_doctype` varchar(140),
`reference_name` varchar(140),
`reverted` int(1) not null default 0,
`revert_of` varchar(140),
`reason` text,
`seen` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `reference_name`(`reference_name`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:59,730 WARNING database DDL Query made to DB:
create table `tabReview Level` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`level_name` varchar(140) unique,
`role` varchar(140) unique,
`review_points` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:59,829 WARNING database DDL Query made to DB:
create table `tabMilestone` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_type` varchar(140),
`reference_name` varchar(140),
`track_field` varchar(140),
`value` varchar(140),
`milestone_tracker` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_type`(`reference_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:23:59,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabMilestone`
				ADD INDEX IF NOT EXISTS `reference_type_reference_name_index`(reference_type, reference_name)
2025-10-25 16:24:00,252 WARNING database DDL Query made to DB:
create table `tabMilestone Tracker` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140) unique,
`track_field` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:24:00,348 WARNING database DDL Query made to DB:
create table `tabAssignment Rule User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:24:00,480 WARNING database DDL Query made to DB:
create table `tabAssignment Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`due_date_based_on` varchar(140),
`priority` int(11) not null default 0,
`disabled` int(1) not null default 0,
`description` text default 'Automatic Assignment',
`assign_condition` longtext,
`unassign_condition` longtext,
`close_condition` longtext,
`rule` varchar(140),
`field` varchar(140),
`last_user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:24:00,594 WARNING database DDL Query made to DB:
create table `tabReminder` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`remind_at` datetime(6),
`description` text,
`reminder_doctype` varchar(140),
`reminder_docname` varchar(140),
`notified` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `user`(`user`),
index `remind_at`(`remind_at`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:24:00,688 WARNING database DDL Query made to DB:
create table `tabAuto Repeat Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:24:00,814 WARNING database DDL Query made to DB:
create table `tabAuto Repeat` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_document` varchar(140),
`submit_on_creation` int(1) not null default 0,
`start_date` date,
`end_date` date,
`disabled` int(1) not null default 0,
`frequency` varchar(140),
`repeat_on_day` int(11) not null default 0,
`repeat_on_last_day` int(1) not null default 0,
`next_schedule_date` date,
`notify_by_email` int(1) not null default 0,
`recipients` text,
`template` varchar(140),
`subject` varchar(140),
`message` text default 'Please find attached {{ doc.doctype }} #{{ doc.name }}',
`print_format` varchar(140),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `next_schedule_date`(`next_schedule_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:24:00,928 WARNING database DDL Query made to DB:
create table `tabAssignment Rule Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-25 16:24:18,160 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_40f451af1a4c79a7'@'localhost'
2025-10-25 16:24:19,825 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_40f451af1a4c79a7`
2025-10-25 16:24:19,829 WARNING database DDL Query made to DB:
CREATE USER '_40f451af1a4c79a7'@'localhost' IDENTIFIED BY 'D1bHtWenYUzmdsT4'
2025-10-25 16:24:19,831 WARNING database DDL Query made to DB:
CREATE DATABASE `_40f451af1a4c79a7` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-10-25 17:19:13,201 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_40f451af1a4c79a7'@'localhost'
2025-10-25 17:19:15,132 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_40f451af1a4c79a7`
2025-10-25 17:19:15,135 WARNING database DDL Query made to DB:
CREATE USER '_40f451af1a4c79a7'@'localhost' IDENTIFIED BY 'D1bHtWenYUzmdsT4'
2025-10-25 17:19:15,136 WARNING database DDL Query made to DB:
CREATE DATABASE `_40f451af1a4c79a7` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-10-26 09:13:59,069 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-10-26 09:14:03,156 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-10-26 09:14:09,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-10-26 09:14:13,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-26 09:14:14,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,2) not null default 0
2025-10-26 09:14:15,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-10-26 09:14:15,767 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,2) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-10-26 09:14:23,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0
2025-10-27 10:11:31,964 WARNING database DDL Query made to DB:
create table `tabShipping Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 10:13:17,429 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parent varchar(140)
2025-10-27 10:13:17,430 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:13:17,431 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:13:17,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` decimal(21,9) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 10:13:55,374 WARNING database DDL Query made to DB:
create table `tabTRA Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 10:14:48,814 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parent varchar(140)
2025-10-27 10:14:48,816 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:14:48,816 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:14:48,886 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` decimal(21,9) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 10:15:37,960 WARNING database DDL Query made to DB:
create table `tabPort Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 10:16:30,969 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parent varchar(140)
2025-10-27 10:16:30,970 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:16:30,971 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:16:31,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` decimal(21,9) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 10:16:56,611 WARNING database DDL Query made to DB:
create table `tabPhysical Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 10:18:11,409 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parent varchar(140)
2025-10-27 10:18:11,410 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:18:11,411 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:18:11,485 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Charges` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` decimal(21,9) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 10:18:39,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 10:18:52,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 10:19:08,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 10:19:45,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 10:20:39,782 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 10:21:24,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 10:34:52,815 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parent varchar(140)
2025-10-27 10:34:52,816 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:34:52,816 WARNING database DDL Query made to DB:
alter table `tabPort Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:34:52,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `currency` varchar(140)
2025-10-27 10:35:18,923 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parent varchar(140)
2025-10-27 10:35:18,924 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:35:18,924 WARNING database DDL Query made to DB:
alter table `tabShipping Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:35:19,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges` MODIFY `currency` varchar(140), MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 10:35:40,326 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parent varchar(140)
2025-10-27 10:35:40,327 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:35:40,327 WARNING database DDL Query made to DB:
alter table `tabPhysical Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:35:40,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Charges` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `currency` varchar(140)
2025-10-27 10:36:15,518 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parent varchar(140)
2025-10-27 10:36:15,519 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parenttype varchar(140)
2025-10-27 10:36:15,520 WARNING database DDL Query made to DB:
alter table `tabTRA Charges` add column if not exists parentfield varchar(140)
2025-10-27 10:36:15,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `currency` varchar(140)
2025-10-27 11:56:22,968 WARNING database DDL Query made to DB:
create table `tabTRA Charges Paid` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 11:57:10,052 WARNING database DDL Query made to DB:
create table `tabShipping Charges Paid` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 11:57:49,589 WARNING database DDL Query made to DB:
create table `tabPhysical Verification Charges Paid` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 11:58:04,867 WARNING database DDL Query made to DB:
RENAME TABLE `tabPhysical Verification Charges Paid` TO `tabVerification Charges Paid`
2025-10-27 11:59:11,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 11:59:12,089 WARNING database DDL Query made to DB:
RENAME TABLE `tabPhysical Charges` TO `tabVerification Charges`
2025-10-27 11:59:42,760 WARNING database DDL Query made to DB:
create table `tabPort Charges Paid` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 12:04:56,085 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:04:56,085 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:04:56,085 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:04:56,086 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:04:56,086 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:04:56,086 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:04:56,087 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:04:56,087 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:04:56,087 WARNING database DDL Query made to DB:
alter table `tabVerification Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:04:56,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:04:56,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:04:56,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabVerification Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:06:41,657 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:41,657 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:41,657 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:41,658 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:41,658 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:41,658 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:41,659 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:41,659 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:41,659 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:41,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140)
2025-10-27 12:06:41,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140)
2025-10-27 12:06:41,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140)
2025-10-27 12:06:59,178 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:59,178 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:59,178 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:59,179 WARNING database DDL Query made to DB:
alter table `tabPort Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:06:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:06:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:06:59,241 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Charges Paid` ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:08:55,879 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:08:55,879 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:08:55,879 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:08:55,880 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:08:55,880 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:08:55,880 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:08:55,881 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:08:55,881 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:08:55,881 WARNING database DDL Query made to DB:
alter table `tabShipping Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:08:55,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:08:55,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:08:55,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:11:03,045 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:11:03,045 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:11:03,045 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:11:03,046 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:11:03,046 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:11:03,046 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:11:03,047 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:11:03,047 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:11:03,047 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:11:03,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:11:03,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:11:03,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` ADD COLUMN `item` varchar(140), ADD COLUMN `currency` varchar(140), ADD COLUMN `amount` decimal(21,9) not null default 0
2025-10-27 12:14:52,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-10-27 12:14:52,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-10-27 12:14:52,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0
2025-10-27 12:15:54,213 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:15:54,213 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:15:54,213 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parent varchar(140)
2025-10-27 12:15:54,215 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:15:54,215 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:15:54,215 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parenttype varchar(140)
2025-10-27 12:15:54,217 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:15:54,217 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:15:54,217 WARNING database DDL Query made to DB:
alter table `tabTRA Charges Paid` add column if not exists parentfield varchar(140)
2025-10-27 12:15:54,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 12:15:54,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 12:15:54,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Charges Paid` MODIFY `amount` decimal(21,9) not null default 0
2025-10-27 12:18:24,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:18:24,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:18:24,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:25:10,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:25:10,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:25:10,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:25:11,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:25:11,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:25:11,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:40:46,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:40:46,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:40:46,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:40:46,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:40:46,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:40:46,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:42:58,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:42:58,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:42:58,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:54:09,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 12:54:09,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 12:54:09,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 12:56:02,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:56:02,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:56:02,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` ADD COLUMN `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 12:56:02,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:56:02,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 12:56:02,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 13:08:16,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 13:08:16,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 13:08:16,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 13:18:15,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 13:18:15,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 13:18:15,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0
2025-10-27 13:18:27,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:18:27,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:18:27,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:18:53,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:18:53,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:18:53,881 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:20:22,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:20:22,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:20:22,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_charges_paid` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-10-27 13:23:07,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_paid_by_consignee` decimal(21,9) not null default 0
2025-10-27 13:23:07,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_paid_by_consignee` decimal(21,9) not null default 0
2025-10-27 13:23:07,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_paid_by_consignee` decimal(21,9) not null default 0
2025-10-27 13:23:07,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 13:23:07,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 13:23:07,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 13:24:35,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:24:35,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:24:35,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` ADD COLUMN `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:24:35,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 13:24:35,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 13:24:35,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-10-27 13:25:09,201 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_40f451af1a4c79a7'@'localhost'
2025-10-27 13:25:12,095 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_40f451af1a4c79a7`
2025-10-27 13:25:12,098 WARNING database DDL Query made to DB:
CREATE USER '_40f451af1a4c79a7'@'localhost' IDENTIFIED BY 'D1bHtWenYUzmdsT4'
2025-10-27 13:25:12,118 WARNING database DDL Query made to DB:
CREATE DATABASE `_40f451af1a4c79a7` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-10-27 13:27:16,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:27:16,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:27:16,706 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:29:53,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:29:53,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:29:53,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `customs_value` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `total_paid_by_consignee` decimal(21,9) not null default 0, MODIFY `total_tra_charges` decimal(21,9) not null default 0
2025-10-27 13:30:23,017 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_ee00f8741849c638'@'localhost'
2025-10-27 13:30:23,018 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_ee00f8741849c638`
2025-10-27 13:30:23,022 WARNING database DDL Query made to DB:
CREATE USER '_ee00f8741849c638'@'localhost' IDENTIFIED BY 'UNfhpcxY8gWl45rh'
2025-10-27 13:30:23,024 WARNING database DDL Query made to DB:
CREATE DATABASE `_ee00f8741849c638` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-10-27 13:30:23,492 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:23,515 WARNING database DDL Query made to DB:
create table __global_search(
				doctype varchar(100),
				name varchar(140),
				title varchar(140),
				content text,
				fulltext(content),
				route varchar(140),
				published int(1) not null default 0,
				unique `doctype_name` (doctype, name))
				COLLATE=utf8mb4_unicode_ci
				ENGINE=MyISAM
				CHARACTER SET=utf8mb4
2025-10-27 13:30:23,543 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:24,195 WARNING database DDL Query made to DB:
create table `tabDocType State` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`color` varchar(140) default 'Blue',
`custom` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:24,328 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` ADD COLUMN `non_negative` int(1) not null default 0, ADD COLUMN `is_virtual` int(1) not null default 0, ADD COLUMN `sort_options` int(1) not null default 0, ADD COLUMN `link_filters` json, ADD COLUMN `fetch_from` text, ADD COLUMN `show_on_timeline` int(1) not null default 0, ADD COLUMN `make_attachment_public` int(1) not null default 0, ADD COLUMN `documentation_url` varchar(140), ADD COLUMN `placeholder` varchar(140)
2025-10-27 13:30:24,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocField` MODIFY `depends_on` longtext, MODIFY `print_width` varchar(64), MODIFY `precision` varchar(140), MODIFY `read_only_depends_on` longtext, MODIFY `width` varchar(64), MODIFY `fieldname` varchar(140), MODIFY `collapsible_depends_on` longtext, MODIFY `mandatory_depends_on` longtext, MODIFY `oldfieldtype` varchar(140), MODIFY `oldfieldname` varchar(140), MODIFY `max_height` varchar(64), MODIFY `fieldtype` varchar(140) default 'Data', MODIFY `label` varchar(140)
2025-10-27 13:30:24,536 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` ADD COLUMN `if_owner` int(1) not null default 0, ADD COLUMN `select` int(1) not null default 0
2025-10-27 13:30:24,577 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocPerm` MODIFY `role` varchar(140)
2025-10-27 13:30:24,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Action` ADD COLUMN `hidden` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-10-27 13:30:24,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Link` ADD COLUMN `parent_doctype` varchar(140), ADD COLUMN `table_fieldname` varchar(140), ADD COLUMN `hidden` int(1) not null default 0, ADD COLUMN `is_child_table` int(1) not null default 0, ADD COLUMN `custom` int(1) not null default 0
2025-10-27 13:30:24,815 WARNING database DDL Query made to DB:
create table `tabForm Tour Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ui_tour` int(1) not null default 0,
`is_table_field` int(1) not null default 0,
`title` varchar(140),
`parent_fieldname` varchar(140),
`fieldname` varchar(140),
`element_selector` varchar(140),
`parent_element_selector` varchar(140),
`description` longtext,
`ondemand_description` longtext,
`position` varchar(140) default 'Bottom',
`hide_buttons` int(1) not null default 0,
`popover_element` int(1) not null default 0,
`modal_trigger` int(1) not null default 0,
`offset_x` int(11) not null default 0,
`offset_y` int(11) not null default 0,
`next_on_click` int(1) not null default 0,
`label` varchar(140),
`fieldtype` varchar(140) default '0',
`has_next_condition` int(1) not null default 0,
`next_step_condition` longtext,
`next_form_tour` varchar(140),
`child_doctype` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:24,909 WARNING database DDL Query made to DB:
create table `tabForm Tour` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`view_name` varchar(140),
`workspace_name` varchar(140),
`list_name` varchar(140) default 'List',
`report_name` varchar(140),
`dashboard_name` varchar(140),
`new_document_form` int(1) not null default 0,
`page_name` varchar(140),
`reference_doctype` varchar(140),
`module` varchar(140),
`ui_tour` int(1) not null default 0,
`track_steps` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`save_on_complete` int(1) not null default 0,
`first_document` int(1) not null default 0,
`include_name_field` int(1) not null default 0,
`page_route` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:25,062 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `is_calendar_and_gantt` int(1) not null default 0, ADD COLUMN `quick_entry` int(1) not null default 0, ADD COLUMN `grid_page_length` int(11) not null default 50, ADD COLUMN `rows_threshold_for_grid_search` int(11) not null default 20, ADD COLUMN `track_views` int(1) not null default 0, ADD COLUMN `queue_in_background` int(1) not null default 0, ADD COLUMN `documentation` varchar(140), ADD COLUMN `nsm_parent_field` varchar(140), ADD COLUMN `allow_events_in_timeline` int(1) not null default 0, ADD COLUMN `allow_auto_repeat` int(1) not null default 0, ADD COLUMN `make_attachments_public` int(1) not null default 0, ADD COLUMN `default_view` varchar(140), ADD COLUMN `force_re_route_to_default_view` int(1) not null default 0, ADD COLUMN `show_preview_popup` int(1) not null default 0, ADD COLUMN `default_email_template` varchar(140), ADD COLUMN `sender_name_field` varchar(140), ADD COLUMN `recipient_account_field` varchar(140), ADD COLUMN `protect_attached_files` int(1) not null default 0, ADD COLUMN `index_web_pages_for_search` int(1) not null default 1, ADD COLUMN `row_format` varchar(140) default 'Dynamic', ADD COLUMN `_comments` text, ADD COLUMN `_assign` text, ADD COLUMN `_liked_by` text
2025-10-27 13:30:25,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` MODIFY `autoname` varchar(140), MODIFY `sort_field` varchar(140) default 'modified', MODIFY `sender_field` varchar(140), MODIFY `_user_tags` text, MODIFY `module` varchar(140), MODIFY `website_search_field` varchar(140), MODIFY `is_published_field` varchar(140), MODIFY `sort_order` varchar(140) default 'DESC', MODIFY `default_print_format` varchar(140), MODIFY `document_type` varchar(140), MODIFY `route` varchar(140), MODIFY `restrict_to_domain` varchar(140), MODIFY `icon` varchar(140), MODIFY `naming_rule` varchar(64), MODIFY `title_field` varchar(140), MODIFY `color` varchar(140), MODIFY `search_fields` varchar(140), MODIFY `subject_field` varchar(140), MODIFY `migration_hash` varchar(140), MODIFY `timeline_field` varchar(140), MODIFY `allow_rename` int(1) not null default 1, MODIFY `image_field` varchar(140), MODIFY `engine` varchar(140) default 'InnoDB'
2025-10-27 13:30:25,149 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD INDEX `module_index`(`module`)
2025-10-27 13:30:25,429 WARNING database DDL Query made to DB:
create table `tabRole` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role_name` varchar(140) unique,
`home_page` varchar(140),
`restrict_to_domain` varchar(140),
`disabled` int(1) not null default 0,
`is_custom` int(1) not null default 0,
`desk_access` int(1) not null default 1,
`two_factor_auth` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:25,516 WARNING database DDL Query made to DB:
create table `tabHas Role` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:25,642 WARNING database DDL Query made to DB:
create table `tabCustom Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`dt` varchar(140),
`module` varchar(140),
`label` varchar(255),
`placeholder` varchar(140),
`fieldname` varchar(140),
`insert_after` varchar(140),
`length` int(11) not null default 0,
`link_filters` json,
`fieldtype` varchar(140) default 'Data',
`precision` varchar(140),
`hide_seconds` int(1) not null default 0,
`hide_days` int(1) not null default 0,
`options` text,
`sort_options` int(1) not null default 0,
`fetch_from` text,
`fetch_if_empty` int(1) not null default 0,
`collapsible` int(1) not null default 0,
`collapsible_depends_on` longtext,
`default` text,
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`non_negative` int(1) not null default 0,
`reqd` int(1) not null default 0,
`unique` int(1) not null default 0,
`is_virtual` int(1) not null default 0,
`read_only` int(1) not null default 0,
`ignore_user_permissions` int(1) not null default 0,
`hidden` int(1) not null default 0,
`print_hide` int(1) not null default 0,
`print_hide_if_no_value` int(1) not null default 0,
`print_width` varchar(140),
`no_copy` int(1) not null default 0,
`allow_on_submit` int(1) not null default 0,
`in_list_view` int(1) not null default 0,
`in_standard_filter` int(1) not null default 0,
`in_global_search` int(1) not null default 0,
`in_preview` int(1) not null default 0,
`bold` int(1) not null default 0,
`report_hide` int(1) not null default 0,
`search_index` int(1) not null default 0,
`allow_in_quick_entry` int(1) not null default 0,
`ignore_xss_filter` int(1) not null default 0,
`translatable` int(1) not null default 0,
`hide_border` int(1) not null default 0,
`show_dashboard` int(1) not null default 0,
`description` text,
`permlevel` int(11) not null default 0,
`width` varchar(140),
`columns` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `dt`(`dt`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:25,747 WARNING database DDL Query made to DB:
create table `tabProperty Setter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system_generated` int(1) not null default 0,
`doctype_or_field` varchar(140),
`doc_type` varchar(140),
`field_name` varchar(140),
`row_name` varchar(140),
`module` varchar(140),
`property` varchar(140),
`property_type` varchar(140),
`value` text,
`default_value` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `doc_type`(`doc_type`),
index `field_name`(`field_name`),
index `property`(`property`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:25,877 WARNING database DDL Query made to DB:
create table `tabWeb Form` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`published` int(1) not null default 0,
`doc_type` varchar(140),
`module` varchar(140),
`is_standard` int(1) not null default 0,
`introduction_text` longtext,
`anonymous` int(1) not null default 0,
`login_required` int(1) not null default 0,
`apply_document_permissions` int(1) not null default 0,
`allow_edit` int(1) not null default 0,
`allow_multiple` int(1) not null default 0,
`allow_delete` int(1) not null default 0,
`allow_incomplete` int(1) not null default 0,
`allow_comments` int(1) not null default 0,
`allow_print` int(1) not null default 0,
`print_format` varchar(140),
`max_attachment_size` int(11) not null default 0,
`show_attachments` int(1) not null default 0,
`allowed_embedding_domains` text,
`condition_json` json,
`show_list` int(1) not null default 0,
`list_title` varchar(140),
`show_sidebar` int(1) not null default 0,
`website_sidebar` varchar(140),
`button_label` varchar(140) default 'Save',
`banner_image` text,
`breadcrumbs` longtext,
`success_title` varchar(140),
`success_url` varchar(140),
`success_message` text,
`meta_title` varchar(140),
`meta_description` text,
`meta_image` text,
`client_script` longtext,
`custom_css` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:25,987 WARNING database DDL Query made to DB:
create table `tabWeb Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140) default 'Section',
`standard` int(1) not null default 0,
`module` varchar(140),
`template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,085 WARNING database DDL Query made to DB:
create table `tabWeb Form Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`fieldtype` varchar(140),
`label` varchar(140),
`allow_read_on_all_link_options` int(1) not null default 0,
`reqd` int(1) not null default 0,
`read_only` int(1) not null default 0,
`show_in_filter` int(1) not null default 0,
`hidden` int(1) not null default 0,
`options` text,
`max_length` int(11) not null default 0,
`max_value` int(11) not null default 0,
`precision` varchar(140),
`depends_on` longtext,
`mandatory_depends_on` longtext,
`read_only_depends_on` longtext,
`description` text,
`default` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,156 WARNING database DDL Query made to DB:
create table `tabPortal Menu Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`enabled` int(1) not null default 0,
`route` varchar(140),
`reference_doctype` varchar(140),
`role` varchar(140),
`target` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,249 WARNING database DDL Query made to DB:
create table `tabNumber Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_standard` int(1) not null default 0,
`module` varchar(140),
`label` varchar(140),
`type` varchar(140),
`report_name` varchar(140),
`method` varchar(140),
`function` varchar(140),
`aggregate_function_based_on` varchar(140),
`document_type` varchar(140),
`parent_document_type` varchar(140),
`report_field` varchar(140),
`report_function` varchar(140),
`is_public` int(1) not null default 0,
`currency` varchar(140),
`filters_config` longtext,
`show_percentage_stats` int(1) not null default 1,
`stats_time_interval` varchar(140) default 'Daily',
`filters_json` longtext,
`dynamic_filters_json` longtext,
`color` varchar(140),
`background_color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,375 WARNING database DDL Query made to DB:
create table `tabDashboard Chart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_standard` int(1) not null default 0,
`module` varchar(140),
`chart_name` varchar(140) unique,
`chart_type` varchar(140),
`report_name` varchar(140),
`use_report_chart` int(1) not null default 0,
`x_field` varchar(140),
`source` varchar(140),
`document_type` varchar(140),
`parent_document_type` varchar(140),
`based_on` varchar(140),
`value_based_on` varchar(140),
`group_by_type` varchar(140) default 'Count',
`group_by_based_on` varchar(140),
`aggregate_function_based_on` varchar(140),
`number_of_groups` int(11) not null default 0,
`is_public` int(1) not null default 0,
`heatmap_year` varchar(140),
`timespan` varchar(140),
`from_date` date,
`to_date` date,
`time_interval` varchar(140),
`timeseries` int(1) not null default 0,
`type` varchar(140) default 'Line',
`show_values_over_chart` int(1) not null default 0,
`currency` varchar(140),
`filters_json` longtext,
`dynamic_filters_json` longtext,
`custom_options` longtext,
`color` varchar(140),
`last_synced_on` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,454 WARNING database DDL Query made to DB:
create table `tabDashboard` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dashboard_name` varchar(140) unique,
`is_default` int(1) not null default 0,
`is_standard` int(1) not null default 0,
`module` varchar(140),
`chart_options` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,523 WARNING database DDL Query made to DB:
create table `tabOnboarding Permission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`role` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,620 WARNING database DDL Query made to DB:
create table `tabOnboarding Step` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`is_complete` int(1) not null default 0,
`is_skipped` int(1) not null default 0,
`description` longtext,
`intro_video_url` varchar(140),
`action` varchar(140),
`action_label` varchar(140),
`reference_document` varchar(140),
`show_full_form` int(1) not null default 0,
`show_form_tour` int(1) not null default 0,
`form_tour` varchar(140),
`is_single` int(1) not null default 0,
`reference_report` varchar(140),
`report_reference_doctype` varchar(140),
`report_type` varchar(140),
`report_description` varchar(140),
`path` varchar(140),
`callback_title` varchar(140),
`callback_message` text,
`validate_action` int(1) not null default 1,
`field` varchar(140),
`value_to_validate` varchar(140),
`video_url` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,685 WARNING database DDL Query made to DB:
create table `tabOnboarding Step Map` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`step` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,755 WARNING database DDL Query made to DB:
create table `tabModule Onboarding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`subtitle` varchar(140),
`module` varchar(140),
`success_message` varchar(140),
`documentation_url` varchar(140),
`is_complete` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,838 WARNING database DDL Query made to DB:
create table `tabWorkspace Link` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140) default 'Link',
`label` varchar(140),
`icon` varchar(140),
`description` longtext,
`hidden` int(1) not null default 0,
`link_type` varchar(140),
`link_to` varchar(140),
`report_ref_doctype` varchar(140),
`dependencies` varchar(140),
`only_for` varchar(140),
`onboard` int(1) not null default 0,
`is_query_report` int(1) not null default 0,
`link_count` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,907 WARNING database DDL Query made to DB:
create table `tabWorkspace Chart` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`chart_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:26,999 WARNING database DDL Query made to DB:
create table `tabWorkspace Shortcut` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`type` varchar(140),
`link_to` varchar(140),
`url` varchar(140),
`doc_view` varchar(140),
`kanban_board` varchar(140),
`label` varchar(140),
`icon` varchar(140),
`restrict_to_domain` varchar(140),
`report_ref_doctype` varchar(140),
`stats_filter` longtext,
`color` varchar(140),
`format` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:27,074 WARNING database DDL Query made to DB:
create table `tabWorkspace Quick List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140),
`label` varchar(140),
`quick_list_filter` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:27,129 WARNING database DDL Query made to DB:
create table `tabWorkspace Number Card` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`number_card_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:27,191 WARNING database DDL Query made to DB:
create table `tabWorkspace Custom Block` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`custom_block_name` varchar(140),
`label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:27,294 WARNING database DDL Query made to DB:
create table `tabWorkspace` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140) unique,
`title` varchar(140),
`sequence_id` decimal(21,9) not null default 0,
`for_user` varchar(140),
`parent_page` varchar(140),
`module` varchar(140),
`icon` varchar(140),
`indicator_color` varchar(140),
`restrict_to_domain` varchar(140),
`hide_custom` int(1) not null default 0,
`public` int(1) not null default 0,
`is_hidden` int(1) not null default 0,
`content` longtext default '[]',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `restrict_to_domain`(`restrict_to_domain`),
index `public`(`public`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:27,389 WARNING database DDL Query made to DB:
create table `tabPage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`system_page` int(1) not null default 0,
`page_name` varchar(140) unique,
`title` varchar(140),
`icon` varchar(140),
`module` varchar(140),
`restrict_to_domain` varchar(140),
`standard` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `standard`(`standard`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:27,503 WARNING database DDL Query made to DB:
create table `tabReport` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`report_name` varchar(140) unique,
`ref_doctype` varchar(140),
`reference_report` varchar(140),
`is_standard` varchar(140),
`module` varchar(140),
`report_type` varchar(140),
`letter_head` varchar(140),
`add_total_row` int(1) not null default 0,
`disabled` int(1) not null default 0,
`prepared_report` int(1) not null default 0,
`add_translate_data` int(1) not null default 0,
`timeout` int(11) not null default 0,
`query` longtext,
`report_script` longtext,
`javascript` longtext,
`json` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:27,582 WARNING database DDL Query made to DB:
create table `tabDashboard Chart Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140) unique,
`module` varchar(140),
`timeseries` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:27,694 WARNING database DDL Query made to DB:
create table `tabPrint Format` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`print_format_for` varchar(140) default 'DocType',
`doc_type` varchar(140),
`report` varchar(140),
`module` varchar(140),
`default_print_language` varchar(140),
`standard` varchar(140) default 'No',
`custom_format` int(1) not null default 0,
`disabled` int(1) not null default 0,
`pdf_generator` varchar(140) default 'wkhtmltopdf',
`print_format_type` varchar(140) default 'Jinja',
`raw_printing` int(1) not null default 0,
`html` longtext,
`raw_commands` longtext,
`margin_top` decimal(21,9) not null default 15.0,
`margin_bottom` decimal(21,9) not null default 15.0,
`margin_left` decimal(21,9) not null default 15.0,
`margin_right` decimal(21,9) not null default 15.0,
`align_labels_right` int(1) not null default 0,
`show_section_headings` int(1) not null default 0,
`line_breaks` int(1) not null default 0,
`absolute_value` int(1) not null default 0,
`font_size` int(11) not null default 14,
`font` varchar(140),
`page_number` varchar(140) default 'Hide',
`css` longtext,
`format_data` longtext,
`print_format_builder` int(1) not null default 0,
`print_format_builder_beta` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `standard`(`standard`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:27,804 WARNING database DDL Query made to DB:
create table `tabWeb Page` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`dynamic_route` int(1) not null default 0,
`published` int(1) not null default 1,
`module` varchar(140),
`content_type` varchar(140) default 'Page Builder',
`slideshow` varchar(140),
`dynamic_template` int(1) not null default 0,
`main_section` longtext,
`main_section_md` longtext,
`main_section_html` longtext,
`context_script` longtext,
`javascript` longtext,
`insert_style` int(1) not null default 0,
`text_align` varchar(140),
`css` longtext,
`full_width` int(1) not null default 1,
`show_title` int(1) not null default 0,
`start_date` datetime(6),
`end_date` datetime(6),
`meta_title` varchar(140),
`meta_description` text,
`meta_image` text,
`show_sidebar` int(1) not null default 0,
`website_sidebar` varchar(140),
`enable_comments` int(1) not null default 0,
`header` longtext,
`breadcrumbs` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:27,964 WARNING database DDL Query made to DB:
create table `tabWebsite Theme` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`theme` varchar(140) unique,
`module` varchar(140) default 'Website',
`custom` int(1) not null default 1,
`google_font` varchar(140),
`font_size` varchar(140),
`font_properties` varchar(140) default 'wght@300;400;500;600;700;800',
`button_rounded_corners` int(1) not null default 1,
`button_shadows` int(1) not null default 0,
`button_gradients` int(1) not null default 0,
`primary_color` varchar(140),
`text_color` varchar(140),
`light_color` varchar(140),
`dark_color` varchar(140),
`background_color` varchar(140),
`custom_overrides` longtext,
`custom_scss` longtext,
`theme_scss` longtext,
`theme_url` varchar(140),
`js` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:28,113 WARNING database DDL Query made to DB:
create table `tabNotification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`is_standard` int(1) not null default 0,
`module` varchar(140),
`channel` varchar(140) default 'Email',
`slack_webhook_url` varchar(140),
`subject` varchar(140),
`event` varchar(140),
`document_type` varchar(140),
`method` varchar(140),
`date_changed` varchar(140),
`days_in_advance` int(11) not null default 0,
`value_changed` varchar(140),
`sender` varchar(140),
`send_system_notification` int(1) not null default 0,
`sender_email` varchar(140),
`condition` longtext,
`set_property_after_alert` varchar(140),
`property_value` varchar(140),
`send_to_all_assignees` int(1) not null default 0,
`message_type` varchar(140) default 'Markdown',
`message` longtext default 'Add your message here',
`attach_print` int(1) not null default 0,
`print_format` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `event`(`event`),
index `document_type`(`document_type`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:28,255 WARNING database DDL Query made to DB:
create table `tabPrint Style` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`print_style_name` varchar(140) unique,
`disabled` int(1) not null default 0,
`standard` int(1) not null default 0,
`css` longtext,
`preview` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:28,325 WARNING database DDL Query made to DB:
create table `tabClient Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dt` varchar(140),
`view` varchar(140) default 'Form',
`module` varchar(140),
`enabled` int(1) not null default 0,
`script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:28,415 WARNING database DDL Query made to DB:
create table `tabServer Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`script_type` varchar(140),
`reference_doctype` varchar(140),
`event_frequency` varchar(140),
`cron_format` varchar(140),
`doctype_event` varchar(140),
`api_method` varchar(140),
`allow_guest` int(1) not null default 0,
`module` varchar(140),
`disabled` int(1) not null default 0,
`script` longtext,
`enable_rate_limit` int(1) not null default 0,
`rate_limit_count` int(11) not null default 5,
`rate_limit_seconds` int(11) not null default 86400,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_doctype`(`reference_doctype`),
index `module`(`module`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:28,519 WARNING database DDL Query made to DB:
ALTER TABLE `tabDefaultValue` MODIFY `defkey` varchar(140)
2025-10-27 13:30:28,566 WARNING database DDL Query made to DB:
ALTER TABLE `tabDefaultValue`
				ADD INDEX IF NOT EXISTS `defaultvalue_parent_parenttype_index`(parent, parenttype)
2025-10-27 13:30:28,661 WARNING database DDL Query made to DB:
create table `tabReport Column` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`label` varchar(140),
`fieldtype` varchar(140),
`options` varchar(140),
`width` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:28,796 WARNING database DDL Query made to DB:
create table `tabSuccess Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140) unique,
`first_success_message` varchar(140) default 'Congratulations on first creations',
`message` varchar(140) default 'Successfully created',
`next_actions` varchar(140),
`action_timeout` int(11) not null default 7,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:28,859 WARNING database DDL Query made to DB:
create table `tabPackage` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package_name` varchar(140),
`readme` longtext,
`license_type` varchar(140),
`license` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:28,927 WARNING database DDL Query made to DB:
create table `tabVersion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ref_doctype` varchar(140),
`docname` varchar(140),
`data` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=COMPRESSED
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-10-27 13:30:28,970 WARNING database DDL Query made to DB:
ALTER TABLE `tabVersion`
				ADD INDEX IF NOT EXISTS `ref_doctype_docname_index`(ref_doctype, docname)
