#!/usr/bin/env python3

import frappe

def create_missing_currency():
    """Create AED currency if it doesn't exist"""
    frappe.init(site='marinair')
    frappe.connect()
    
    # Create AED currency if it doesn't exist
    if not frappe.db.exists('Currency', 'AED'):
        currency_doc = frappe.get_doc({
            'doctype': 'Currency',
            'currency_name': 'UAE Dirham',
            'name': 'AED',
            'fraction': 'Fils',
            'fraction_units': 100,
            'number_format': '#,###.##',
            'smallest_currency_fraction_value': 0.01,
            'symbol': 'د.إ'
        })
        currency_doc.insert()
        frappe.db.commit()
        print('AED currency created successfully')
    else:
        print('AED currency already exists')
    
    frappe.destroy()

if __name__ == '__main__':
    create_missing_currency()
