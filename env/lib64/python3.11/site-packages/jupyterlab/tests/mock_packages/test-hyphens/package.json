{"name": "test-hyphens", "version": "3.0.2", "private": true, "main": "index.js", "files": ["index.js"], "scripts": {"build:labextension": "jupyter labextension build .", "clean": "rimraf ./test_hyphens/labextension"}, "dependencies": {"@jupyterlab/launcher": "^4.0.0-alpha.21"}, "devDependencies": {"@jupyterlab/builder": "^4.0.0-alpha.21", "rimraf": "~3.0.0"}, "jupyterlab": {"extension": true, "outputDir": "./test_hyphens/labextension"}}